{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Deep Learning\\\\LTAGIT\\\\LTA\\\\frontend\\\\src\\\\pages\\\\UserManagement\\\\ClientManagement.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Table, Button, Modal, Form, Alert, Badge, Spinner } from 'react-bootstrap';\nimport { FaPlus, FaEdit, FaTrash, FaEye, FaBuilding, FaUsers, FaProjectDiagram } from 'react-icons/fa';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ClientManagement = ({\n  user\n}) => {\n  _s();\n  const [clients, setClients] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [showModal, setShowModal] = useState(false);\n  const [modalMode, setModalMode] = useState('create'); // 'create', 'edit', 'view'\n  const [selectedClient, setSelectedClient] = useState(null);\n  const [formData, setFormData] = useState({\n    organizationName: '',\n    contactPerson: '',\n    email: '',\n    phone: '',\n    address: '',\n    accessModules: ['Blind Spot', 'Road Safety'],\n    maxProjects: 10,\n    maxUsers: 50\n  });\n  const [alert, setAlert] = useState({\n    show: false,\n    message: '',\n    type: 'success'\n  });\n  useEffect(() => {\n    fetchClients();\n  }, []);\n  const fetchClients = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get('/api/user-management/clients', {\n        params: {\n          user_role: user.role\n        }\n      });\n      if (response.data.success) {\n        setClients(response.data.clients);\n      } else {\n        showAlert('Failed to fetch clients', 'danger');\n      }\n    } catch (error) {\n      console.error('Error fetching clients:', error);\n      showAlert('Error fetching clients', 'danger');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const showAlert = (message, type = 'success') => {\n    setAlert({\n      show: true,\n      message,\n      type\n    });\n    setTimeout(() => setAlert({\n      show: false,\n      message: '',\n      type: 'success'\n    }), 5000);\n  };\n  const handleShowModal = (mode, client = null) => {\n    setModalMode(mode);\n    setSelectedClient(client);\n    if (mode === 'create') {\n      setFormData({\n        organizationName: '',\n        contactPerson: '',\n        email: '',\n        phone: '',\n        address: '',\n        accessModules: ['Blind Spot', 'Road Safety'],\n        maxProjects: 10,\n        maxUsers: 50\n      });\n    } else if (client) {\n      setFormData({\n        organizationName: client.organizationName || '',\n        contactPerson: client.contactPerson || '',\n        email: client.email || '',\n        phone: client.phone || '',\n        address: client.address || '',\n        accessModules: client.accessModules || ['Blind Spot', 'Road Safety'],\n        maxProjects: client.maxProjects || 10,\n        maxUsers: client.maxUsers || 50\n      });\n    }\n    setShowModal(true);\n  };\n  const handleCloseModal = () => {\n    setShowModal(false);\n    setSelectedClient(null);\n    setFormData({\n      organizationName: '',\n      contactPerson: '',\n      email: '',\n      phone: '',\n      address: '',\n      accessModules: ['Blind Spot', 'Road Safety'],\n      maxProjects: 10,\n      maxUsers: 50\n    });\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value,\n      type,\n      checked\n    } = e.target;\n    if (name === 'accessModules') {\n      const modules = [...formData.accessModules];\n      if (checked) {\n        modules.push(value);\n      } else {\n        const index = modules.indexOf(value);\n        if (index > -1) modules.splice(index, 1);\n      }\n      setFormData({\n        ...formData,\n        accessModules: modules\n      });\n    } else {\n      setFormData({\n        ...formData,\n        [name]: type === 'number' ? parseInt(value) : value\n      });\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    try {\n      let response;\n      const submitData = {\n        ...formData,\n        adminId: user.userId || 'admin_001',\n        createdBy: user.userId || 'admin_001'\n      };\n      if (modalMode === 'create') {\n        response = await axios.post('/api/user-management/clients', submitData);\n      } else if (modalMode === 'edit') {\n        response = await axios.put(`/api/user-management/clients/${selectedClient.clientId}`, submitData);\n      }\n      if (response.data.success) {\n        showAlert(modalMode === 'create' ? 'Client created successfully' : 'Client updated successfully', 'success');\n        fetchClients();\n        handleCloseModal();\n      } else {\n        showAlert(response.data.message || 'Operation failed', 'danger');\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Error saving client:', error);\n      showAlert(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Error saving client', 'danger');\n    }\n  };\n  const handleDelete = async clientId => {\n    if (!window.confirm('Are you sure you want to delete this client? This action cannot be undone.')) {\n      return;\n    }\n    try {\n      const response = await axios.delete(`/api/user-management/clients/${clientId}`);\n      if (response.data.success) {\n        showAlert('Client deleted successfully', 'success');\n        fetchClients();\n      } else {\n        showAlert(response.data.message || 'Failed to delete client', 'danger');\n      }\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      console.error('Error deleting client:', error);\n      showAlert(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'Error deleting client', 'danger');\n    }\n  };\n  const getStatusBadge = isActive => {\n    return /*#__PURE__*/_jsxDEV(Badge, {\n      bg: isActive ? 'success' : 'secondary',\n      children: isActive ? 'Active' : 'Inactive'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 174,\n      columnNumber: 7\n    }, this);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Container, {\n      className: \"d-flex justify-content-center align-items-center\",\n      style: {\n        minHeight: '400px'\n      },\n      children: /*#__PURE__*/_jsxDEV(Spinner, {\n        animation: \"border\",\n        role: \"status\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"visually-hidden\",\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Container, {\n    fluid: true,\n    className: \"p-4\",\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-4\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-between align-items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: [/*#__PURE__*/_jsxDEV(FaBuilding, {\n                className: \"me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 19\n              }, this), \"Client Management\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted\",\n              children: \"Manage client organizations and their settings\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this), user.role === 'Admin' && /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"primary\",\n            onClick: () => handleShowModal('create'),\n            children: [/*#__PURE__*/_jsxDEV(FaPlus, {\n              className: \"me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 17\n            }, this), \"Add Client\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 192,\n      columnNumber: 7\n    }, this), alert.show && /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-3\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(Alert, {\n          variant: alert.type,\n          dismissible: true,\n          onClose: () => setAlert({\n            show: false,\n            message: '',\n            type: 'success'\n          }),\n          children: alert.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: [\"Clients (\", clients.length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: clients.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center py-4\",\n              children: [/*#__PURE__*/_jsxDEV(FaBuilding, {\n                size: 48,\n                className: \"text-muted mb-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-muted\",\n                children: \"No clients found\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Table, {\n              responsive: true,\n              hover: true,\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Organization\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 234,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Contact Person\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 235,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Email\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 236,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 237,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Users\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 238,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Projects\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 239,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Actions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 240,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: clients.map(client => /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: client.organizationName\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 247,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 248,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                      className: \"text-muted\",\n                      children: client.clientId\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 249,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 246,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: client.contactPerson\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 251,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: client.email\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 252,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: getStatusBadge(client.isActive)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 253,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(Badge, {\n                      bg: \"info\",\n                      children: [/*#__PURE__*/_jsxDEV(FaUsers, {\n                        className: \"me-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 256,\n                        columnNumber: 29\n                      }, this), client.userCount || 0]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 255,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 254,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(Badge, {\n                      bg: \"secondary\",\n                      children: [/*#__PURE__*/_jsxDEV(FaProjectDiagram, {\n                        className: \"me-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 261,\n                        columnNumber: 29\n                      }, this), client.projectCount || 0]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 260,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 259,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"btn-group\",\n                      role: \"group\",\n                      children: [/*#__PURE__*/_jsxDEV(Button, {\n                        variant: \"outline-info\",\n                        size: \"sm\",\n                        onClick: () => handleShowModal('view', client),\n                        title: \"View Details\",\n                        children: /*#__PURE__*/_jsxDEV(FaEye, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 272,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 266,\n                        columnNumber: 29\n                      }, this), user.role === 'Admin' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(Button, {\n                          variant: \"outline-warning\",\n                          size: \"sm\",\n                          onClick: () => handleShowModal('edit', client),\n                          title: \"Edit Client\",\n                          children: /*#__PURE__*/_jsxDEV(FaEdit, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 282,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 276,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(Button, {\n                          variant: \"outline-danger\",\n                          size: \"sm\",\n                          onClick: () => handleDelete(client.clientId),\n                          title: \"Delete Client\",\n                          children: /*#__PURE__*/_jsxDEV(FaTrash, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 290,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 284,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 265,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 264,\n                    columnNumber: 25\n                  }, this)]\n                }, client.clientId, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 218,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showModal,\n      onHide: handleCloseModal,\n      size: \"lg\",\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: [modalMode === 'create' && 'Add New Client', modalMode === 'edit' && 'Edit Client', modalMode === 'view' && 'Client Details']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: /*#__PURE__*/_jsxDEV(Form, {\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Organization Name *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 320,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"text\",\n                  name: \"organizationName\",\n                  value: formData.organizationName,\n                  onChange: handleInputChange,\n                  required: true,\n                  disabled: modalMode === 'view'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Contact Person *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 333,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"text\",\n                  name: \"contactPerson\",\n                  value: formData.contactPerson,\n                  onChange: handleInputChange,\n                  required: true,\n                  disabled: modalMode === 'view'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 334,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Email *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 349,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"email\",\n                  name: \"email\",\n                  value: formData.email,\n                  onChange: handleInputChange,\n                  required: true,\n                  disabled: modalMode === 'view'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 350,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 348,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Phone\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 362,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"tel\",\n                  name: \"phone\",\n                  value: formData.phone,\n                  onChange: handleInputChange,\n                  disabled: modalMode === 'view'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 363,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Address\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              as: \"textarea\",\n              rows: 2,\n              name: \"address\",\n              value: formData.address,\n              onChange: handleInputChange,\n              disabled: modalMode === 'view'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Max Projects\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 389,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"number\",\n                  name: \"maxProjects\",\n                  value: formData.maxProjects,\n                  onChange: handleInputChange,\n                  min: \"1\",\n                  disabled: modalMode === 'view'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 390,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 388,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Max Users\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 402,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"number\",\n                  name: \"maxUsers\",\n                  value: formData.maxUsers,\n                  onChange: handleInputChange,\n                  min: \"1\",\n                  disabled: modalMode === 'view'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 403,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 401,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Access Modules\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 416,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: ['Blind Spot', 'Road Safety'].map(module => /*#__PURE__*/_jsxDEV(Form.Check, {\n                type: \"checkbox\",\n                id: `module-${module}`,\n                label: module,\n                value: module,\n                checked: formData.accessModules.includes(module),\n                onChange: handleInputChange,\n                name: \"accessModules\",\n                disabled: modalMode === 'view',\n                inline: true\n              }, module, false, {\n                fileName: _jsxFileName,\n                lineNumber: 419,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 315,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: handleCloseModal,\n          children: modalMode === 'view' ? 'Close' : 'Cancel'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 437,\n          columnNumber: 11\n        }, this), modalMode !== 'view' && /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: handleSubmit,\n          children: modalMode === 'create' ? 'Create Client' : 'Update Client'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 441,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 436,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 307,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 191,\n    columnNumber: 5\n  }, this);\n};\n_s(ClientManagement, \"58VFNU9wGw123I0s6SW6Q2v1PSc=\");\n_c = ClientManagement;\nexport default ClientManagement;\nvar _c;\n$RefreshReg$(_c, \"ClientManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "Table", "<PERSON><PERSON>", "Modal", "Form", "<PERSON><PERSON>", "Badge", "Spinner", "FaPlus", "FaEdit", "FaTrash", "FaEye", "FaBuilding", "FaUsers", "FaProjectDiagram", "axios", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ClientManagement", "user", "_s", "clients", "setClients", "loading", "setLoading", "showModal", "setShowModal", "modalMode", "setModalMode", "selectedClient", "setSelectedClient", "formData", "setFormData", "organizationName", "<PERSON><PERSON><PERSON>", "email", "phone", "address", "accessModules", "maxProjects", "maxUsers", "alert", "<PERSON><PERSON><PERSON><PERSON>", "show", "message", "type", "fetchClients", "response", "get", "params", "user_role", "role", "data", "success", "show<PERSON><PERSON><PERSON>", "error", "console", "setTimeout", "handleShowModal", "mode", "client", "handleCloseModal", "handleInputChange", "e", "name", "value", "checked", "target", "modules", "push", "index", "indexOf", "splice", "parseInt", "handleSubmit", "preventDefault", "submitData", "adminId", "userId", "created<PERSON>y", "post", "put", "clientId", "_error$response", "_error$response$data", "handleDelete", "window", "confirm", "delete", "_error$response2", "_error$response2$data", "getStatusBadge", "isActive", "bg", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "style", "minHeight", "animation", "fluid", "variant", "onClick", "dismissible", "onClose", "Header", "length", "Body", "size", "responsive", "hover", "map", "userCount", "projectCount", "title", "onHide", "closeButton", "Title", "onSubmit", "md", "Group", "Label", "Control", "onChange", "required", "disabled", "as", "rows", "min", "module", "Check", "id", "label", "includes", "inline", "Footer", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Deep Learning/LTAGIT/LTA/frontend/src/pages/UserManagement/ClientManagement.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Con<PERSON>er, Row, Col, Card, Table, Button, Modal, Form, Alert, Badge, Spinner } from 'react-bootstrap';\nimport { FaPlus, FaEdit, FaTrash, FaEye, FaBuilding, FaUsers, FaProjectDiagram } from 'react-icons/fa';\nimport axios from 'axios';\n\nconst ClientManagement = ({ user }) => {\n  const [clients, setClients] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [showModal, setShowModal] = useState(false);\n  const [modalMode, setModalMode] = useState('create'); // 'create', 'edit', 'view'\n  const [selectedClient, setSelectedClient] = useState(null);\n  const [formData, setFormData] = useState({\n    organizationName: '',\n    contactPerson: '',\n    email: '',\n    phone: '',\n    address: '',\n    accessModules: ['Blind Spot', 'Road Safety'],\n    maxProjects: 10,\n    maxUsers: 50\n  });\n  const [alert, setAlert] = useState({ show: false, message: '', type: 'success' });\n\n  useEffect(() => {\n    fetchClients();\n  }, []);\n\n  const fetchClients = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get('/api/user-management/clients', {\n        params: { user_role: user.role }\n      });\n      \n      if (response.data.success) {\n        setClients(response.data.clients);\n      } else {\n        showAlert('Failed to fetch clients', 'danger');\n      }\n    } catch (error) {\n      console.error('Error fetching clients:', error);\n      showAlert('Error fetching clients', 'danger');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const showAlert = (message, type = 'success') => {\n    setAlert({ show: true, message, type });\n    setTimeout(() => setAlert({ show: false, message: '', type: 'success' }), 5000);\n  };\n\n  const handleShowModal = (mode, client = null) => {\n    setModalMode(mode);\n    setSelectedClient(client);\n    \n    if (mode === 'create') {\n      setFormData({\n        organizationName: '',\n        contactPerson: '',\n        email: '',\n        phone: '',\n        address: '',\n        accessModules: ['Blind Spot', 'Road Safety'],\n        maxProjects: 10,\n        maxUsers: 50\n      });\n    } else if (client) {\n      setFormData({\n        organizationName: client.organizationName || '',\n        contactPerson: client.contactPerson || '',\n        email: client.email || '',\n        phone: client.phone || '',\n        address: client.address || '',\n        accessModules: client.accessModules || ['Blind Spot', 'Road Safety'],\n        maxProjects: client.maxProjects || 10,\n        maxUsers: client.maxUsers || 50\n      });\n    }\n    \n    setShowModal(true);\n  };\n\n  const handleCloseModal = () => {\n    setShowModal(false);\n    setSelectedClient(null);\n    setFormData({\n      organizationName: '',\n      contactPerson: '',\n      email: '',\n      phone: '',\n      address: '',\n      accessModules: ['Blind Spot', 'Road Safety'],\n      maxProjects: 10,\n      maxUsers: 50\n    });\n  };\n\n  const handleInputChange = (e) => {\n    const { name, value, type, checked } = e.target;\n    \n    if (name === 'accessModules') {\n      const modules = [...formData.accessModules];\n      if (checked) {\n        modules.push(value);\n      } else {\n        const index = modules.indexOf(value);\n        if (index > -1) modules.splice(index, 1);\n      }\n      setFormData({ ...formData, accessModules: modules });\n    } else {\n      setFormData({\n        ...formData,\n        [name]: type === 'number' ? parseInt(value) : value\n      });\n    }\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    try {\n      let response;\n      const submitData = {\n        ...formData,\n        adminId: user.userId || 'admin_001',\n        createdBy: user.userId || 'admin_001'\n      };\n      \n      if (modalMode === 'create') {\n        response = await axios.post('/api/user-management/clients', submitData);\n      } else if (modalMode === 'edit') {\n        response = await axios.put(`/api/user-management/clients/${selectedClient.clientId}`, submitData);\n      }\n      \n      if (response.data.success) {\n        showAlert(\n          modalMode === 'create' ? 'Client created successfully' : 'Client updated successfully',\n          'success'\n        );\n        fetchClients();\n        handleCloseModal();\n      } else {\n        showAlert(response.data.message || 'Operation failed', 'danger');\n      }\n    } catch (error) {\n      console.error('Error saving client:', error);\n      showAlert(error.response?.data?.message || 'Error saving client', 'danger');\n    }\n  };\n\n  const handleDelete = async (clientId) => {\n    if (!window.confirm('Are you sure you want to delete this client? This action cannot be undone.')) {\n      return;\n    }\n    \n    try {\n      const response = await axios.delete(`/api/user-management/clients/${clientId}`);\n      \n      if (response.data.success) {\n        showAlert('Client deleted successfully', 'success');\n        fetchClients();\n      } else {\n        showAlert(response.data.message || 'Failed to delete client', 'danger');\n      }\n    } catch (error) {\n      console.error('Error deleting client:', error);\n      showAlert(error.response?.data?.message || 'Error deleting client', 'danger');\n    }\n  };\n\n  const getStatusBadge = (isActive) => {\n    return (\n      <Badge bg={isActive ? 'success' : 'secondary'}>\n        {isActive ? 'Active' : 'Inactive'}\n      </Badge>\n    );\n  };\n\n  if (loading) {\n    return (\n      <Container className=\"d-flex justify-content-center align-items-center\" style={{ minHeight: '400px' }}>\n        <Spinner animation=\"border\" role=\"status\">\n          <span className=\"visually-hidden\">Loading...</span>\n        </Spinner>\n      </Container>\n    );\n  }\n\n  return (\n    <Container fluid className=\"p-4\">\n      <Row className=\"mb-4\">\n        <Col>\n          <div className=\"d-flex justify-content-between align-items-center\">\n            <div>\n              <h2><FaBuilding className=\"me-2\" />Client Management</h2>\n              <p className=\"text-muted\">Manage client organizations and their settings</p>\n            </div>\n            {user.role === 'Admin' && (\n              <Button variant=\"primary\" onClick={() => handleShowModal('create')}>\n                <FaPlus className=\"me-2\" />Add Client\n              </Button>\n            )}\n          </div>\n        </Col>\n      </Row>\n\n      {alert.show && (\n        <Row className=\"mb-3\">\n          <Col>\n            <Alert variant={alert.type} dismissible onClose={() => setAlert({ show: false, message: '', type: 'success' })}>\n              {alert.message}\n            </Alert>\n          </Col>\n        </Row>\n      )}\n\n      <Row>\n        <Col>\n          <Card>\n            <Card.Header>\n              <h5 className=\"mb-0\">Clients ({clients.length})</h5>\n            </Card.Header>\n            <Card.Body>\n              {clients.length === 0 ? (\n                <div className=\"text-center py-4\">\n                  <FaBuilding size={48} className=\"text-muted mb-3\" />\n                  <p className=\"text-muted\">No clients found</p>\n                </div>\n              ) : (\n                <Table responsive hover>\n                  <thead>\n                    <tr>\n                      <th>Organization</th>\n                      <th>Contact Person</th>\n                      <th>Email</th>\n                      <th>Status</th>\n                      <th>Users</th>\n                      <th>Projects</th>\n                      <th>Actions</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    {clients.map((client) => (\n                      <tr key={client.clientId}>\n                        <td>\n                          <strong>{client.organizationName}</strong>\n                          <br />\n                          <small className=\"text-muted\">{client.clientId}</small>\n                        </td>\n                        <td>{client.contactPerson}</td>\n                        <td>{client.email}</td>\n                        <td>{getStatusBadge(client.isActive)}</td>\n                        <td>\n                          <Badge bg=\"info\">\n                            <FaUsers className=\"me-1\" />{client.userCount || 0}\n                          </Badge>\n                        </td>\n                        <td>\n                          <Badge bg=\"secondary\">\n                            <FaProjectDiagram className=\"me-1\" />{client.projectCount || 0}\n                          </Badge>\n                        </td>\n                        <td>\n                          <div className=\"btn-group\" role=\"group\">\n                            <Button\n                              variant=\"outline-info\"\n                              size=\"sm\"\n                              onClick={() => handleShowModal('view', client)}\n                              title=\"View Details\"\n                            >\n                              <FaEye />\n                            </Button>\n                            {user.role === 'Admin' && (\n                              <>\n                                <Button\n                                  variant=\"outline-warning\"\n                                  size=\"sm\"\n                                  onClick={() => handleShowModal('edit', client)}\n                                  title=\"Edit Client\"\n                                >\n                                  <FaEdit />\n                                </Button>\n                                <Button\n                                  variant=\"outline-danger\"\n                                  size=\"sm\"\n                                  onClick={() => handleDelete(client.clientId)}\n                                  title=\"Delete Client\"\n                                >\n                                  <FaTrash />\n                                </Button>\n                              </>\n                            )}\n                          </div>\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </Table>\n              )}\n            </Card.Body>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* Client Modal */}\n      <Modal show={showModal} onHide={handleCloseModal} size=\"lg\">\n        <Modal.Header closeButton>\n          <Modal.Title>\n            {modalMode === 'create' && 'Add New Client'}\n            {modalMode === 'edit' && 'Edit Client'}\n            {modalMode === 'view' && 'Client Details'}\n          </Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          <Form onSubmit={handleSubmit}>\n            <Row>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Organization Name *</Form.Label>\n                  <Form.Control\n                    type=\"text\"\n                    name=\"organizationName\"\n                    value={formData.organizationName}\n                    onChange={handleInputChange}\n                    required\n                    disabled={modalMode === 'view'}\n                  />\n                </Form.Group>\n              </Col>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Contact Person *</Form.Label>\n                  <Form.Control\n                    type=\"text\"\n                    name=\"contactPerson\"\n                    value={formData.contactPerson}\n                    onChange={handleInputChange}\n                    required\n                    disabled={modalMode === 'view'}\n                  />\n                </Form.Group>\n              </Col>\n            </Row>\n            \n            <Row>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Email *</Form.Label>\n                  <Form.Control\n                    type=\"email\"\n                    name=\"email\"\n                    value={formData.email}\n                    onChange={handleInputChange}\n                    required\n                    disabled={modalMode === 'view'}\n                  />\n                </Form.Group>\n              </Col>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Phone</Form.Label>\n                  <Form.Control\n                    type=\"tel\"\n                    name=\"phone\"\n                    value={formData.phone}\n                    onChange={handleInputChange}\n                    disabled={modalMode === 'view'}\n                  />\n                </Form.Group>\n              </Col>\n            </Row>\n            \n            <Form.Group className=\"mb-3\">\n              <Form.Label>Address</Form.Label>\n              <Form.Control\n                as=\"textarea\"\n                rows={2}\n                name=\"address\"\n                value={formData.address}\n                onChange={handleInputChange}\n                disabled={modalMode === 'view'}\n              />\n            </Form.Group>\n            \n            <Row>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Max Projects</Form.Label>\n                  <Form.Control\n                    type=\"number\"\n                    name=\"maxProjects\"\n                    value={formData.maxProjects}\n                    onChange={handleInputChange}\n                    min=\"1\"\n                    disabled={modalMode === 'view'}\n                  />\n                </Form.Group>\n              </Col>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Max Users</Form.Label>\n                  <Form.Control\n                    type=\"number\"\n                    name=\"maxUsers\"\n                    value={formData.maxUsers}\n                    onChange={handleInputChange}\n                    min=\"1\"\n                    disabled={modalMode === 'view'}\n                  />\n                </Form.Group>\n              </Col>\n            </Row>\n            \n            <Form.Group className=\"mb-3\">\n              <Form.Label>Access Modules</Form.Label>\n              <div>\n                {['Blind Spot', 'Road Safety'].map((module) => (\n                  <Form.Check\n                    key={module}\n                    type=\"checkbox\"\n                    id={`module-${module}`}\n                    label={module}\n                    value={module}\n                    checked={formData.accessModules.includes(module)}\n                    onChange={handleInputChange}\n                    name=\"accessModules\"\n                    disabled={modalMode === 'view'}\n                    inline\n                  />\n                ))}\n              </div>\n            </Form.Group>\n          </Form>\n        </Modal.Body>\n        <Modal.Footer>\n          <Button variant=\"secondary\" onClick={handleCloseModal}>\n            {modalMode === 'view' ? 'Close' : 'Cancel'}\n          </Button>\n          {modalMode !== 'view' && (\n            <Button variant=\"primary\" onClick={handleSubmit}>\n              {modalMode === 'create' ? 'Create Client' : 'Update Client'}\n            </Button>\n          )}\n        </Modal.Footer>\n      </Modal>\n    </Container>\n  );\n};\n\nexport default ClientManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,OAAO,QAAQ,iBAAiB;AAC9G,SAASC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,KAAK,EAAEC,UAAU,EAAEC,OAAO,EAAEC,gBAAgB,QAAQ,gBAAgB;AACtG,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1B,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EACrC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC8B,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgC,SAAS,EAAEC,YAAY,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACkC,SAAS,EAAEC,YAAY,CAAC,GAAGnC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;EACtD,MAAM,CAACoC,cAAc,EAAEC,iBAAiB,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACsC,QAAQ,EAAEC,WAAW,CAAC,GAAGvC,QAAQ,CAAC;IACvCwC,gBAAgB,EAAE,EAAE;IACpBC,aAAa,EAAE,EAAE;IACjBC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,EAAE;IACXC,aAAa,EAAE,CAAC,YAAY,EAAE,aAAa,CAAC;IAC5CC,WAAW,EAAE,EAAE;IACfC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGjD,QAAQ,CAAC;IAAEkD,IAAI,EAAE,KAAK;IAAEC,OAAO,EAAE,EAAE;IAAEC,IAAI,EAAE;EAAU,CAAC,CAAC;EAEjFnD,SAAS,CAAC,MAAM;IACdoD,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFtB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMuB,QAAQ,GAAG,MAAMlC,KAAK,CAACmC,GAAG,CAAC,8BAA8B,EAAE;QAC/DC,MAAM,EAAE;UAAEC,SAAS,EAAE/B,IAAI,CAACgC;QAAK;MACjC,CAAC,CAAC;MAEF,IAAIJ,QAAQ,CAACK,IAAI,CAACC,OAAO,EAAE;QACzB/B,UAAU,CAACyB,QAAQ,CAACK,IAAI,CAAC/B,OAAO,CAAC;MACnC,CAAC,MAAM;QACLiC,SAAS,CAAC,yBAAyB,EAAE,QAAQ,CAAC;MAChD;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CD,SAAS,CAAC,wBAAwB,EAAE,QAAQ,CAAC;IAC/C,CAAC,SAAS;MACR9B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM8B,SAAS,GAAGA,CAACV,OAAO,EAAEC,IAAI,GAAG,SAAS,KAAK;IAC/CH,QAAQ,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO;MAAEC;IAAK,CAAC,CAAC;IACvCY,UAAU,CAAC,MAAMf,QAAQ,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE,EAAE;MAAEC,IAAI,EAAE;IAAU,CAAC,CAAC,EAAE,IAAI,CAAC;EACjF,CAAC;EAED,MAAMa,eAAe,GAAGA,CAACC,IAAI,EAAEC,MAAM,GAAG,IAAI,KAAK;IAC/ChC,YAAY,CAAC+B,IAAI,CAAC;IAClB7B,iBAAiB,CAAC8B,MAAM,CAAC;IAEzB,IAAID,IAAI,KAAK,QAAQ,EAAE;MACrB3B,WAAW,CAAC;QACVC,gBAAgB,EAAE,EAAE;QACpBC,aAAa,EAAE,EAAE;QACjBC,KAAK,EAAE,EAAE;QACTC,KAAK,EAAE,EAAE;QACTC,OAAO,EAAE,EAAE;QACXC,aAAa,EAAE,CAAC,YAAY,EAAE,aAAa,CAAC;QAC5CC,WAAW,EAAE,EAAE;QACfC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIoB,MAAM,EAAE;MACjB5B,WAAW,CAAC;QACVC,gBAAgB,EAAE2B,MAAM,CAAC3B,gBAAgB,IAAI,EAAE;QAC/CC,aAAa,EAAE0B,MAAM,CAAC1B,aAAa,IAAI,EAAE;QACzCC,KAAK,EAAEyB,MAAM,CAACzB,KAAK,IAAI,EAAE;QACzBC,KAAK,EAAEwB,MAAM,CAACxB,KAAK,IAAI,EAAE;QACzBC,OAAO,EAAEuB,MAAM,CAACvB,OAAO,IAAI,EAAE;QAC7BC,aAAa,EAAEsB,MAAM,CAACtB,aAAa,IAAI,CAAC,YAAY,EAAE,aAAa,CAAC;QACpEC,WAAW,EAAEqB,MAAM,CAACrB,WAAW,IAAI,EAAE;QACrCC,QAAQ,EAAEoB,MAAM,CAACpB,QAAQ,IAAI;MAC/B,CAAC,CAAC;IACJ;IAEAd,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMmC,gBAAgB,GAAGA,CAAA,KAAM;IAC7BnC,YAAY,CAAC,KAAK,CAAC;IACnBI,iBAAiB,CAAC,IAAI,CAAC;IACvBE,WAAW,CAAC;MACVC,gBAAgB,EAAE,EAAE;MACpBC,aAAa,EAAE,EAAE;MACjBC,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE,EAAE;MACXC,aAAa,EAAE,CAAC,YAAY,EAAE,aAAa,CAAC;MAC5CC,WAAW,EAAE,EAAE;MACfC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC;EAED,MAAMsB,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC,KAAK;MAAEpB,IAAI;MAAEqB;IAAQ,CAAC,GAAGH,CAAC,CAACI,MAAM;IAE/C,IAAIH,IAAI,KAAK,eAAe,EAAE;MAC5B,MAAMI,OAAO,GAAG,CAAC,GAAGrC,QAAQ,CAACO,aAAa,CAAC;MAC3C,IAAI4B,OAAO,EAAE;QACXE,OAAO,CAACC,IAAI,CAACJ,KAAK,CAAC;MACrB,CAAC,MAAM;QACL,MAAMK,KAAK,GAAGF,OAAO,CAACG,OAAO,CAACN,KAAK,CAAC;QACpC,IAAIK,KAAK,GAAG,CAAC,CAAC,EAAEF,OAAO,CAACI,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MAC1C;MACAtC,WAAW,CAAC;QAAE,GAAGD,QAAQ;QAAEO,aAAa,EAAE8B;MAAQ,CAAC,CAAC;IACtD,CAAC,MAAM;MACLpC,WAAW,CAAC;QACV,GAAGD,QAAQ;QACX,CAACiC,IAAI,GAAGnB,IAAI,KAAK,QAAQ,GAAG4B,QAAQ,CAACR,KAAK,CAAC,GAAGA;MAChD,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMS,YAAY,GAAG,MAAOX,CAAC,IAAK;IAChCA,CAAC,CAACY,cAAc,CAAC,CAAC;IAElB,IAAI;MACF,IAAI5B,QAAQ;MACZ,MAAM6B,UAAU,GAAG;QACjB,GAAG7C,QAAQ;QACX8C,OAAO,EAAE1D,IAAI,CAAC2D,MAAM,IAAI,WAAW;QACnCC,SAAS,EAAE5D,IAAI,CAAC2D,MAAM,IAAI;MAC5B,CAAC;MAED,IAAInD,SAAS,KAAK,QAAQ,EAAE;QAC1BoB,QAAQ,GAAG,MAAMlC,KAAK,CAACmE,IAAI,CAAC,8BAA8B,EAAEJ,UAAU,CAAC;MACzE,CAAC,MAAM,IAAIjD,SAAS,KAAK,MAAM,EAAE;QAC/BoB,QAAQ,GAAG,MAAMlC,KAAK,CAACoE,GAAG,CAAC,gCAAgCpD,cAAc,CAACqD,QAAQ,EAAE,EAAEN,UAAU,CAAC;MACnG;MAEA,IAAI7B,QAAQ,CAACK,IAAI,CAACC,OAAO,EAAE;QACzBC,SAAS,CACP3B,SAAS,KAAK,QAAQ,GAAG,6BAA6B,GAAG,6BAA6B,EACtF,SACF,CAAC;QACDmB,YAAY,CAAC,CAAC;QACde,gBAAgB,CAAC,CAAC;MACpB,CAAC,MAAM;QACLP,SAAS,CAACP,QAAQ,CAACK,IAAI,CAACR,OAAO,IAAI,kBAAkB,EAAE,QAAQ,CAAC;MAClE;IACF,CAAC,CAAC,OAAOW,KAAK,EAAE;MAAA,IAAA4B,eAAA,EAAAC,oBAAA;MACd5B,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CD,SAAS,CAAC,EAAA6B,eAAA,GAAA5B,KAAK,CAACR,QAAQ,cAAAoC,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgB/B,IAAI,cAAAgC,oBAAA,uBAApBA,oBAAA,CAAsBxC,OAAO,KAAI,qBAAqB,EAAE,QAAQ,CAAC;IAC7E;EACF,CAAC;EAED,MAAMyC,YAAY,GAAG,MAAOH,QAAQ,IAAK;IACvC,IAAI,CAACI,MAAM,CAACC,OAAO,CAAC,4EAA4E,CAAC,EAAE;MACjG;IACF;IAEA,IAAI;MACF,MAAMxC,QAAQ,GAAG,MAAMlC,KAAK,CAAC2E,MAAM,CAAC,gCAAgCN,QAAQ,EAAE,CAAC;MAE/E,IAAInC,QAAQ,CAACK,IAAI,CAACC,OAAO,EAAE;QACzBC,SAAS,CAAC,6BAA6B,EAAE,SAAS,CAAC;QACnDR,YAAY,CAAC,CAAC;MAChB,CAAC,MAAM;QACLQ,SAAS,CAACP,QAAQ,CAACK,IAAI,CAACR,OAAO,IAAI,yBAAyB,EAAE,QAAQ,CAAC;MACzE;IACF,CAAC,CAAC,OAAOW,KAAK,EAAE;MAAA,IAAAkC,gBAAA,EAAAC,qBAAA;MACdlC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CD,SAAS,CAAC,EAAAmC,gBAAA,GAAAlC,KAAK,CAACR,QAAQ,cAAA0C,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBrC,IAAI,cAAAsC,qBAAA,uBAApBA,qBAAA,CAAsB9C,OAAO,KAAI,uBAAuB,EAAE,QAAQ,CAAC;IAC/E;EACF,CAAC;EAED,MAAM+C,cAAc,GAAIC,QAAQ,IAAK;IACnC,oBACE7E,OAAA,CAACX,KAAK;MAACyF,EAAE,EAAED,QAAQ,GAAG,SAAS,GAAG,WAAY;MAAAE,QAAA,EAC3CF,QAAQ,GAAG,QAAQ,GAAG;IAAU;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC;EAEZ,CAAC;EAED,IAAI3E,OAAO,EAAE;IACX,oBACER,OAAA,CAACpB,SAAS;MAACwG,SAAS,EAAC,kDAAkD;MAACC,KAAK,EAAE;QAAEC,SAAS,EAAE;MAAQ,CAAE;MAAAP,QAAA,eACpG/E,OAAA,CAACV,OAAO;QAACiG,SAAS,EAAC,QAAQ;QAACnD,IAAI,EAAC,QAAQ;QAAA2C,QAAA,eACvC/E,OAAA;UAAMoF,SAAS,EAAC,iBAAiB;UAAAL,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAEhB;EAEA,oBACEnF,OAAA,CAACpB,SAAS;IAAC4G,KAAK;IAACJ,SAAS,EAAC,KAAK;IAAAL,QAAA,gBAC9B/E,OAAA,CAACnB,GAAG;MAACuG,SAAS,EAAC,MAAM;MAAAL,QAAA,eACnB/E,OAAA,CAAClB,GAAG;QAAAiG,QAAA,eACF/E,OAAA;UAAKoF,SAAS,EAAC,mDAAmD;UAAAL,QAAA,gBAChE/E,OAAA;YAAA+E,QAAA,gBACE/E,OAAA;cAAA+E,QAAA,gBAAI/E,OAAA,CAACL,UAAU;gBAACyF,SAAS,EAAC;cAAM;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,qBAAiB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzDnF,OAAA;cAAGoF,SAAS,EAAC,YAAY;cAAAL,QAAA,EAAC;YAA8C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzE,CAAC,EACL/E,IAAI,CAACgC,IAAI,KAAK,OAAO,iBACpBpC,OAAA,CAACf,MAAM;YAACwG,OAAO,EAAC,SAAS;YAACC,OAAO,EAAEA,CAAA,KAAM/C,eAAe,CAAC,QAAQ,CAAE;YAAAoC,QAAA,gBACjE/E,OAAA,CAACT,MAAM;cAAC6F,SAAS,EAAC;YAAM;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,cAC7B;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELzD,KAAK,CAACE,IAAI,iBACT5B,OAAA,CAACnB,GAAG;MAACuG,SAAS,EAAC,MAAM;MAAAL,QAAA,eACnB/E,OAAA,CAAClB,GAAG;QAAAiG,QAAA,eACF/E,OAAA,CAACZ,KAAK;UAACqG,OAAO,EAAE/D,KAAK,CAACI,IAAK;UAAC6D,WAAW;UAACC,OAAO,EAAEA,CAAA,KAAMjE,QAAQ,CAAC;YAAEC,IAAI,EAAE,KAAK;YAAEC,OAAO,EAAE,EAAE;YAAEC,IAAI,EAAE;UAAU,CAAC,CAAE;UAAAiD,QAAA,EAC5GrD,KAAK,CAACG;QAAO;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDnF,OAAA,CAACnB,GAAG;MAAAkG,QAAA,eACF/E,OAAA,CAAClB,GAAG;QAAAiG,QAAA,eACF/E,OAAA,CAACjB,IAAI;UAAAgG,QAAA,gBACH/E,OAAA,CAACjB,IAAI,CAAC8G,MAAM;YAAAd,QAAA,eACV/E,OAAA;cAAIoF,SAAS,EAAC,MAAM;cAAAL,QAAA,GAAC,WAAS,EAACzE,OAAO,CAACwF,MAAM,EAAC,GAAC;YAAA;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eACdnF,OAAA,CAACjB,IAAI,CAACgH,IAAI;YAAAhB,QAAA,EACPzE,OAAO,CAACwF,MAAM,KAAK,CAAC,gBACnB9F,OAAA;cAAKoF,SAAS,EAAC,kBAAkB;cAAAL,QAAA,gBAC/B/E,OAAA,CAACL,UAAU;gBAACqG,IAAI,EAAE,EAAG;gBAACZ,SAAS,EAAC;cAAiB;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpDnF,OAAA;gBAAGoF,SAAS,EAAC,YAAY;gBAAAL,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,gBAENnF,OAAA,CAAChB,KAAK;cAACiH,UAAU;cAACC,KAAK;cAAAnB,QAAA,gBACrB/E,OAAA;gBAAA+E,QAAA,eACE/E,OAAA;kBAAA+E,QAAA,gBACE/E,OAAA;oBAAA+E,QAAA,EAAI;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrBnF,OAAA;oBAAA+E,QAAA,EAAI;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvBnF,OAAA;oBAAA+E,QAAA,EAAI;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACdnF,OAAA;oBAAA+E,QAAA,EAAI;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACfnF,OAAA;oBAAA+E,QAAA,EAAI;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACdnF,OAAA;oBAAA+E,QAAA,EAAI;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjBnF,OAAA;oBAAA+E,QAAA,EAAI;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACRnF,OAAA;gBAAA+E,QAAA,EACGzE,OAAO,CAAC6F,GAAG,CAAEtD,MAAM,iBAClB7C,OAAA;kBAAA+E,QAAA,gBACE/E,OAAA;oBAAA+E,QAAA,gBACE/E,OAAA;sBAAA+E,QAAA,EAASlC,MAAM,CAAC3B;oBAAgB;sBAAA8D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS,CAAC,eAC1CnF,OAAA;sBAAAgF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNnF,OAAA;sBAAOoF,SAAS,EAAC,YAAY;sBAAAL,QAAA,EAAElC,MAAM,CAACsB;oBAAQ;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrD,CAAC,eACLnF,OAAA;oBAAA+E,QAAA,EAAKlC,MAAM,CAAC1B;kBAAa;oBAAA6D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC/BnF,OAAA;oBAAA+E,QAAA,EAAKlC,MAAM,CAACzB;kBAAK;oBAAA4D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACvBnF,OAAA;oBAAA+E,QAAA,EAAKH,cAAc,CAAC/B,MAAM,CAACgC,QAAQ;kBAAC;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC1CnF,OAAA;oBAAA+E,QAAA,eACE/E,OAAA,CAACX,KAAK;sBAACyF,EAAE,EAAC,MAAM;sBAAAC,QAAA,gBACd/E,OAAA,CAACJ,OAAO;wBAACwF,SAAS,EAAC;sBAAM;wBAAAJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,EAACtC,MAAM,CAACuD,SAAS,IAAI,CAAC;oBAAA;sBAAApB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7C;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACLnF,OAAA;oBAAA+E,QAAA,eACE/E,OAAA,CAACX,KAAK;sBAACyF,EAAE,EAAC,WAAW;sBAAAC,QAAA,gBACnB/E,OAAA,CAACH,gBAAgB;wBAACuF,SAAS,EAAC;sBAAM;wBAAAJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,EAACtC,MAAM,CAACwD,YAAY,IAAI,CAAC;oBAAA;sBAAArB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACLnF,OAAA;oBAAA+E,QAAA,eACE/E,OAAA;sBAAKoF,SAAS,EAAC,WAAW;sBAAChD,IAAI,EAAC,OAAO;sBAAA2C,QAAA,gBACrC/E,OAAA,CAACf,MAAM;wBACLwG,OAAO,EAAC,cAAc;wBACtBO,IAAI,EAAC,IAAI;wBACTN,OAAO,EAAEA,CAAA,KAAM/C,eAAe,CAAC,MAAM,EAAEE,MAAM,CAAE;wBAC/CyD,KAAK,EAAC,cAAc;wBAAAvB,QAAA,eAEpB/E,OAAA,CAACN,KAAK;0BAAAsF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,EACR/E,IAAI,CAACgC,IAAI,KAAK,OAAO,iBACpBpC,OAAA,CAAAE,SAAA;wBAAA6E,QAAA,gBACE/E,OAAA,CAACf,MAAM;0BACLwG,OAAO,EAAC,iBAAiB;0BACzBO,IAAI,EAAC,IAAI;0BACTN,OAAO,EAAEA,CAAA,KAAM/C,eAAe,CAAC,MAAM,EAAEE,MAAM,CAAE;0BAC/CyD,KAAK,EAAC,aAAa;0BAAAvB,QAAA,eAEnB/E,OAAA,CAACR,MAAM;4BAAAwF,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC,eACTnF,OAAA,CAACf,MAAM;0BACLwG,OAAO,EAAC,gBAAgB;0BACxBO,IAAI,EAAC,IAAI;0BACTN,OAAO,EAAEA,CAAA,KAAMpB,YAAY,CAACzB,MAAM,CAACsB,QAAQ,CAAE;0BAC7CmC,KAAK,EAAC,eAAe;0BAAAvB,QAAA,eAErB/E,OAAA,CAACP,OAAO;4BAAAuF,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC;sBAAA,eACT,CACH;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA,GAlDEtC,MAAM,CAACsB,QAAQ;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAmDpB,CACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UACR;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnF,OAAA,CAACd,KAAK;MAAC0C,IAAI,EAAElB,SAAU;MAAC6F,MAAM,EAAEzD,gBAAiB;MAACkD,IAAI,EAAC,IAAI;MAAAjB,QAAA,gBACzD/E,OAAA,CAACd,KAAK,CAAC2G,MAAM;QAACW,WAAW;QAAAzB,QAAA,eACvB/E,OAAA,CAACd,KAAK,CAACuH,KAAK;UAAA1B,QAAA,GACTnE,SAAS,KAAK,QAAQ,IAAI,gBAAgB,EAC1CA,SAAS,KAAK,MAAM,IAAI,aAAa,EACrCA,SAAS,KAAK,MAAM,IAAI,gBAAgB;QAAA;UAAAoE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACfnF,OAAA,CAACd,KAAK,CAAC6G,IAAI;QAAAhB,QAAA,eACT/E,OAAA,CAACb,IAAI;UAACuH,QAAQ,EAAE/C,YAAa;UAAAoB,QAAA,gBAC3B/E,OAAA,CAACnB,GAAG;YAAAkG,QAAA,gBACF/E,OAAA,CAAClB,GAAG;cAAC6H,EAAE,EAAE,CAAE;cAAA5B,QAAA,eACT/E,OAAA,CAACb,IAAI,CAACyH,KAAK;gBAACxB,SAAS,EAAC,MAAM;gBAAAL,QAAA,gBAC1B/E,OAAA,CAACb,IAAI,CAAC0H,KAAK;kBAAA9B,QAAA,EAAC;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC5CnF,OAAA,CAACb,IAAI,CAAC2H,OAAO;kBACXhF,IAAI,EAAC,MAAM;kBACXmB,IAAI,EAAC,kBAAkB;kBACvBC,KAAK,EAAElC,QAAQ,CAACE,gBAAiB;kBACjC6F,QAAQ,EAAEhE,iBAAkB;kBAC5BiE,QAAQ;kBACRC,QAAQ,EAAErG,SAAS,KAAK;gBAAO;kBAAAoE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNnF,OAAA,CAAClB,GAAG;cAAC6H,EAAE,EAAE,CAAE;cAAA5B,QAAA,eACT/E,OAAA,CAACb,IAAI,CAACyH,KAAK;gBAACxB,SAAS,EAAC,MAAM;gBAAAL,QAAA,gBAC1B/E,OAAA,CAACb,IAAI,CAAC0H,KAAK;kBAAA9B,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACzCnF,OAAA,CAACb,IAAI,CAAC2H,OAAO;kBACXhF,IAAI,EAAC,MAAM;kBACXmB,IAAI,EAAC,eAAe;kBACpBC,KAAK,EAAElC,QAAQ,CAACG,aAAc;kBAC9B4F,QAAQ,EAAEhE,iBAAkB;kBAC5BiE,QAAQ;kBACRC,QAAQ,EAAErG,SAAS,KAAK;gBAAO;kBAAAoE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENnF,OAAA,CAACnB,GAAG;YAAAkG,QAAA,gBACF/E,OAAA,CAAClB,GAAG;cAAC6H,EAAE,EAAE,CAAE;cAAA5B,QAAA,eACT/E,OAAA,CAACb,IAAI,CAACyH,KAAK;gBAACxB,SAAS,EAAC,MAAM;gBAAAL,QAAA,gBAC1B/E,OAAA,CAACb,IAAI,CAAC0H,KAAK;kBAAA9B,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAChCnF,OAAA,CAACb,IAAI,CAAC2H,OAAO;kBACXhF,IAAI,EAAC,OAAO;kBACZmB,IAAI,EAAC,OAAO;kBACZC,KAAK,EAAElC,QAAQ,CAACI,KAAM;kBACtB2F,QAAQ,EAAEhE,iBAAkB;kBAC5BiE,QAAQ;kBACRC,QAAQ,EAAErG,SAAS,KAAK;gBAAO;kBAAAoE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNnF,OAAA,CAAClB,GAAG;cAAC6H,EAAE,EAAE,CAAE;cAAA5B,QAAA,eACT/E,OAAA,CAACb,IAAI,CAACyH,KAAK;gBAACxB,SAAS,EAAC,MAAM;gBAAAL,QAAA,gBAC1B/E,OAAA,CAACb,IAAI,CAAC0H,KAAK;kBAAA9B,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC9BnF,OAAA,CAACb,IAAI,CAAC2H,OAAO;kBACXhF,IAAI,EAAC,KAAK;kBACVmB,IAAI,EAAC,OAAO;kBACZC,KAAK,EAAElC,QAAQ,CAACK,KAAM;kBACtB0F,QAAQ,EAAEhE,iBAAkB;kBAC5BkE,QAAQ,EAAErG,SAAS,KAAK;gBAAO;kBAAAoE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENnF,OAAA,CAACb,IAAI,CAACyH,KAAK;YAACxB,SAAS,EAAC,MAAM;YAAAL,QAAA,gBAC1B/E,OAAA,CAACb,IAAI,CAAC0H,KAAK;cAAA9B,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAChCnF,OAAA,CAACb,IAAI,CAAC2H,OAAO;cACXI,EAAE,EAAC,UAAU;cACbC,IAAI,EAAE,CAAE;cACRlE,IAAI,EAAC,SAAS;cACdC,KAAK,EAAElC,QAAQ,CAACM,OAAQ;cACxByF,QAAQ,EAAEhE,iBAAkB;cAC5BkE,QAAQ,EAAErG,SAAS,KAAK;YAAO;cAAAoE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC,eAEbnF,OAAA,CAACnB,GAAG;YAAAkG,QAAA,gBACF/E,OAAA,CAAClB,GAAG;cAAC6H,EAAE,EAAE,CAAE;cAAA5B,QAAA,eACT/E,OAAA,CAACb,IAAI,CAACyH,KAAK;gBAACxB,SAAS,EAAC,MAAM;gBAAAL,QAAA,gBAC1B/E,OAAA,CAACb,IAAI,CAAC0H,KAAK;kBAAA9B,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrCnF,OAAA,CAACb,IAAI,CAAC2H,OAAO;kBACXhF,IAAI,EAAC,QAAQ;kBACbmB,IAAI,EAAC,aAAa;kBAClBC,KAAK,EAAElC,QAAQ,CAACQ,WAAY;kBAC5BuF,QAAQ,EAAEhE,iBAAkB;kBAC5BqE,GAAG,EAAC,GAAG;kBACPH,QAAQ,EAAErG,SAAS,KAAK;gBAAO;kBAAAoE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNnF,OAAA,CAAClB,GAAG;cAAC6H,EAAE,EAAE,CAAE;cAAA5B,QAAA,eACT/E,OAAA,CAACb,IAAI,CAACyH,KAAK;gBAACxB,SAAS,EAAC,MAAM;gBAAAL,QAAA,gBAC1B/E,OAAA,CAACb,IAAI,CAAC0H,KAAK;kBAAA9B,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAClCnF,OAAA,CAACb,IAAI,CAAC2H,OAAO;kBACXhF,IAAI,EAAC,QAAQ;kBACbmB,IAAI,EAAC,UAAU;kBACfC,KAAK,EAAElC,QAAQ,CAACS,QAAS;kBACzBsF,QAAQ,EAAEhE,iBAAkB;kBAC5BqE,GAAG,EAAC,GAAG;kBACPH,QAAQ,EAAErG,SAAS,KAAK;gBAAO;kBAAAoE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENnF,OAAA,CAACb,IAAI,CAACyH,KAAK;YAACxB,SAAS,EAAC,MAAM;YAAAL,QAAA,gBAC1B/E,OAAA,CAACb,IAAI,CAAC0H,KAAK;cAAA9B,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACvCnF,OAAA;cAAA+E,QAAA,EACG,CAAC,YAAY,EAAE,aAAa,CAAC,CAACoB,GAAG,CAAEkB,MAAM,iBACxCrH,OAAA,CAACb,IAAI,CAACmI,KAAK;gBAETxF,IAAI,EAAC,UAAU;gBACfyF,EAAE,EAAE,UAAUF,MAAM,EAAG;gBACvBG,KAAK,EAAEH,MAAO;gBACdnE,KAAK,EAAEmE,MAAO;gBACdlE,OAAO,EAAEnC,QAAQ,CAACO,aAAa,CAACkG,QAAQ,CAACJ,MAAM,CAAE;gBACjDN,QAAQ,EAAEhE,iBAAkB;gBAC5BE,IAAI,EAAC,eAAe;gBACpBgE,QAAQ,EAAErG,SAAS,KAAK,MAAO;gBAC/B8G,MAAM;cAAA,GATDL,MAAM;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAUZ,CACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACbnF,OAAA,CAACd,KAAK,CAACyI,MAAM;QAAA5C,QAAA,gBACX/E,OAAA,CAACf,MAAM;UAACwG,OAAO,EAAC,WAAW;UAACC,OAAO,EAAE5C,gBAAiB;UAAAiC,QAAA,EACnDnE,SAAS,KAAK,MAAM,GAAG,OAAO,GAAG;QAAQ;UAAAoE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,EACRvE,SAAS,KAAK,MAAM,iBACnBZ,OAAA,CAACf,MAAM;UAACwG,OAAO,EAAC,SAAS;UAACC,OAAO,EAAE/B,YAAa;UAAAoB,QAAA,EAC7CnE,SAAS,KAAK,QAAQ,GAAG,eAAe,GAAG;QAAe;UAAAoE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEhB,CAAC;AAAC9E,EAAA,CA3bIF,gBAAgB;AAAAyH,EAAA,GAAhBzH,gBAAgB;AA6btB,eAAeA,gBAAgB;AAAC,IAAAyH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}