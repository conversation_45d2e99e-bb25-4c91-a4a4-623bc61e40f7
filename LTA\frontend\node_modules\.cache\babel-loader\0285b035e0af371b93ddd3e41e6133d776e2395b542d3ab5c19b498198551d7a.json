{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Deep Learning\\\\LTAGIT\\\\LTA\\\\frontend\\\\src\\\\pages\\\\UserManagement\\\\HierarchyView.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Alert, Spinner, Badge } from 'react-bootstrap';\nimport { FaSitemap, FaUserTie, FaUserFriends, FaHardHat, FaBuilding } from 'react-icons/fa';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HierarchyView = ({\n  user\n}) => {\n  _s();\n  var _hierarchy$Admin, _hierarchy$Client, _hierarchy$Supervisor, _hierarchy$FieldOffi;\n  const [hierarchy, setHierarchy] = useState({});\n  const [loading, setLoading] = useState(true);\n  const [alert, setAlert] = useState({\n    show: false,\n    message: '',\n    type: 'success'\n  });\n  useEffect(() => {\n    fetchHierarchy();\n  }, []);\n  const fetchHierarchy = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get('/api/user-management/users/hierarchy', {\n        params: {\n          user_role: user.role,\n          user_id: user.userId || user.username,\n          client_id: user.clientId\n        }\n      });\n      if (response.data.success) {\n        setHierarchy(response.data.hierarchy);\n      } else {\n        showAlert('Failed to fetch hierarchy', 'danger');\n      }\n    } catch (error) {\n      console.error('Error fetching hierarchy:', error);\n      showAlert('Error fetching hierarchy', 'danger');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const showAlert = (message, type = 'success') => {\n    setAlert({\n      show: true,\n      message,\n      type\n    });\n    setTimeout(() => setAlert({\n      show: false,\n      message: '',\n      type: 'success'\n    }), 5000);\n  };\n  const getRoleIcon = role => {\n    switch (role) {\n      case 'Admin':\n        return /*#__PURE__*/_jsxDEV(FaBuilding, {\n          className: \"text-primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 16\n        }, this);\n      case 'Client':\n        return /*#__PURE__*/_jsxDEV(FaUserTie, {\n          className: \"text-success\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 16\n        }, this);\n      case 'Supervisor':\n        return /*#__PURE__*/_jsxDEV(FaUserFriends, {\n          className: \"text-warning\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 16\n        }, this);\n      case 'Field Officer':\n        return /*#__PURE__*/_jsxDEV(FaHardHat, {\n          className: \"text-info\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(FaUserFriends, {\n          className: \"text-secondary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  const getRoleBadgeColor = role => {\n    switch (role) {\n      case 'Admin':\n        return 'primary';\n      case 'Client':\n        return 'success';\n      case 'Supervisor':\n        return 'warning';\n      case 'Field Officer':\n        return 'info';\n      default:\n        return 'secondary';\n    }\n  };\n  const UserCard = ({\n    user,\n    role\n  }) => /*#__PURE__*/_jsxDEV(Card, {\n    className: \"mb-2 shadow-sm\",\n    children: /*#__PURE__*/_jsxDEV(Card.Body, {\n      className: \"py-2\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex align-items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"me-3\",\n          children: getRoleIcon(role)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-grow-1\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex justify-content-between align-items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: user.fullName || user.username\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 85,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                className: \"text-muted\",\n                children: user.email\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(Badge, {\n                bg: getRoleBadgeColor(role),\n                children: role\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 89,\n                columnNumber: 17\n              }, this), user.isActive !== undefined && /*#__PURE__*/_jsxDEV(Badge, {\n                bg: user.isActive ? 'success' : 'secondary',\n                className: \"ms-1\",\n                children: user.isActive ? 'Active' : 'Inactive'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 91,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 75,\n    columnNumber: 5\n  }, this);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Container, {\n      className: \"d-flex justify-content-center align-items-center\",\n      style: {\n        minHeight: '400px'\n      },\n      children: /*#__PURE__*/_jsxDEV(Spinner, {\n        animation: \"border\",\n        role: \"status\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"visually-hidden\",\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Container, {\n    fluid: true,\n    className: \"p-4\",\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-4\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-between align-items-center\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: [/*#__PURE__*/_jsxDEV(FaSitemap, {\n                className: \"me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 19\n              }, this), \"Hierarchy View\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted\",\n              children: \"Organizational structure and user relationships\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 7\n    }, this), alert.show && /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-3\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(Alert, {\n          variant: alert.type,\n          dismissible: true,\n          onClose: () => setAlert({\n            show: false,\n            message: '',\n            type: 'success'\n          }),\n          children: alert.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      children: [hierarchy.Admin && hierarchy.Admin.length > 0 && /*#__PURE__*/_jsxDEV(Col, {\n        lg: 3,\n        md: 6,\n        className: \"mb-4\",\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"h-100\",\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            className: \"bg-primary text-white\",\n            children: /*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"mb-0\",\n              children: [/*#__PURE__*/_jsxDEV(FaBuilding, {\n                className: \"me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 19\n              }, this), \"Administrators (\", hierarchy.Admin.length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            className: \"p-2\",\n            children: hierarchy.Admin.map(admin => /*#__PURE__*/_jsxDEV(UserCard, {\n              user: admin,\n              role: \"Admin\"\n            }, admin.userId || admin.username, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 11\n      }, this), hierarchy.Client && hierarchy.Client.length > 0 && /*#__PURE__*/_jsxDEV(Col, {\n        lg: 3,\n        md: 6,\n        className: \"mb-4\",\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"h-100\",\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            className: \"bg-success text-white\",\n            children: /*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"mb-0\",\n              children: [/*#__PURE__*/_jsxDEV(FaUserTie, {\n                className: \"me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 19\n              }, this), \"Clients (\", hierarchy.Client.length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            className: \"p-2\",\n            children: hierarchy.Client.map(client => /*#__PURE__*/_jsxDEV(UserCard, {\n              user: client,\n              role: \"Client\"\n            }, client.userId || client.username, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 11\n      }, this), hierarchy.Supervisor && hierarchy.Supervisor.length > 0 && /*#__PURE__*/_jsxDEV(Col, {\n        lg: 3,\n        md: 6,\n        className: \"mb-4\",\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"h-100\",\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            className: \"bg-warning text-dark\",\n            children: /*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"mb-0\",\n              children: [/*#__PURE__*/_jsxDEV(FaUserFriends, {\n                className: \"me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 19\n              }, this), \"Supervisors (\", hierarchy.Supervisor.length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            className: \"p-2\",\n            children: hierarchy.Supervisor.map(supervisor => /*#__PURE__*/_jsxDEV(UserCard, {\n              user: supervisor,\n              role: \"Supervisor\"\n            }, supervisor.userId || supervisor.username, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 11\n      }, this), hierarchy['Field Officer'] && hierarchy['Field Officer'].length > 0 && /*#__PURE__*/_jsxDEV(Col, {\n        lg: 3,\n        md: 6,\n        className: \"mb-4\",\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"h-100\",\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            className: \"bg-info text-white\",\n            children: /*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"mb-0\",\n              children: [/*#__PURE__*/_jsxDEV(FaHardHat, {\n                className: \"me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 19\n              }, this), \"Field Officers (\", hierarchy['Field Officer'].length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            className: \"p-2\",\n            children: hierarchy['Field Officer'].map(fieldOfficer => /*#__PURE__*/_jsxDEV(UserCard, {\n              user: fieldOfficer,\n              role: \"Field Officer\"\n            }, fieldOfficer.userId || fieldOfficer.username, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mt-4\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            children: /*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"mb-0\",\n              children: \"Summary Statistics\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: /*#__PURE__*/_jsxDEV(Row, {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                md: 3,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"border-end\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-primary\",\n                    children: ((_hierarchy$Admin = hierarchy.Admin) === null || _hierarchy$Admin === void 0 ? void 0 : _hierarchy$Admin.length) || 0\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 225,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: \"Administrators\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 226,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                md: 3,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"border-end\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-success\",\n                    children: ((_hierarchy$Client = hierarchy.Client) === null || _hierarchy$Client === void 0 ? void 0 : _hierarchy$Client.length) || 0\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 231,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: \"Clients\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 232,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                md: 3,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"border-end\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-warning\",\n                    children: ((_hierarchy$Supervisor = hierarchy.Supervisor) === null || _hierarchy$Supervisor === void 0 ? void 0 : _hierarchy$Supervisor.length) || 0\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 237,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: \"Supervisors\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 238,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                md: 3,\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-info\",\n                  children: ((_hierarchy$FieldOffi = hierarchy['Field Officer']) === null || _hierarchy$FieldOffi === void 0 ? void 0 : _hierarchy$FieldOffi.length) || 0\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"text-muted\",\n                  children: \"Field Officers\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 215,\n      columnNumber: 7\n    }, this), Object.keys(hierarchy).length === 0 && /*#__PURE__*/_jsxDEV(Row, {\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            className: \"text-center py-5\",\n            children: [/*#__PURE__*/_jsxDEV(FaSitemap, {\n              size: 48,\n              className: \"text-muted mb-3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"text-muted\",\n              children: \"No hierarchy data available\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted\",\n              children: \"Users and organizational structure will appear here once data is available.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 253,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 114,\n    columnNumber: 5\n  }, this);\n};\n_s(HierarchyView, \"m4xiknUeL3OdXjw3EQUyZowrYg4=\");\n_c = HierarchyView;\nexport default HierarchyView;\nvar _c;\n$RefreshReg$(_c, \"HierarchyView\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "<PERSON><PERSON>", "Spinner", "Badge", "FaSitemap", "FaUserTie", "FaUserFriends", "FaHardHat", "FaBuilding", "axios", "jsxDEV", "_jsxDEV", "HierarchyView", "user", "_s", "_hierarchy$Admin", "_hierarchy$Client", "_hierarchy$Supervisor", "_hierarchy$FieldOffi", "hierarchy", "setHierarchy", "loading", "setLoading", "alert", "<PERSON><PERSON><PERSON><PERSON>", "show", "message", "type", "fetchHierarchy", "response", "get", "params", "user_role", "role", "user_id", "userId", "username", "client_id", "clientId", "data", "success", "show<PERSON><PERSON><PERSON>", "error", "console", "setTimeout", "getRoleIcon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getRoleBadgeColor", "UserCard", "children", "Body", "fullName", "email", "bg", "isActive", "undefined", "style", "minHeight", "animation", "fluid", "variant", "dismissible", "onClose", "Admin", "length", "lg", "md", "Header", "map", "admin", "Client", "client", "Supervisor", "supervisor", "fieldOfficer", "Object", "keys", "size", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Deep Learning/LTAGIT/LTA/frontend/src/pages/UserManagement/HierarchyView.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Badge } from 'react-bootstrap';\nimport { FaSitemap, FaUserTie, FaUserFriends, FaHardHat, FaBuilding } from 'react-icons/fa';\nimport axios from 'axios';\n\nconst HierarchyView = ({ user }) => {\n  const [hierarchy, setHierarchy] = useState({});\n  const [loading, setLoading] = useState(true);\n  const [alert, setAlert] = useState({ show: false, message: '', type: 'success' });\n\n  useEffect(() => {\n    fetchHierarchy();\n  }, []);\n\n  const fetchHierarchy = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get('/api/user-management/users/hierarchy', {\n        params: { \n          user_role: user.role,\n          user_id: user.userId || user.username,\n          client_id: user.clientId\n        }\n      });\n      \n      if (response.data.success) {\n        setHierarchy(response.data.hierarchy);\n      } else {\n        showAlert('Failed to fetch hierarchy', 'danger');\n      }\n    } catch (error) {\n      console.error('Error fetching hierarchy:', error);\n      showAlert('Error fetching hierarchy', 'danger');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const showAlert = (message, type = 'success') => {\n    setAlert({ show: true, message, type });\n    setTimeout(() => setAlert({ show: false, message: '', type: 'success' }), 5000);\n  };\n\n  const getRoleIcon = (role) => {\n    switch (role) {\n      case 'Admin':\n        return <FaBuilding className=\"text-primary\" />;\n      case 'Client':\n        return <FaUserTie className=\"text-success\" />;\n      case 'Supervisor':\n        return <FaUserFriends className=\"text-warning\" />;\n      case 'Field Officer':\n        return <FaHardHat className=\"text-info\" />;\n      default:\n        return <FaUserFriends className=\"text-secondary\" />;\n    }\n  };\n\n  const getRoleBadgeColor = (role) => {\n    switch (role) {\n      case 'Admin':\n        return 'primary';\n      case 'Client':\n        return 'success';\n      case 'Supervisor':\n        return 'warning';\n      case 'Field Officer':\n        return 'info';\n      default:\n        return 'secondary';\n    }\n  };\n\n  const UserCard = ({ user, role }) => (\n    <Card className=\"mb-2 shadow-sm\">\n      <Card.Body className=\"py-2\">\n        <div className=\"d-flex align-items-center\">\n          <div className=\"me-3\">\n            {getRoleIcon(role)}\n          </div>\n          <div className=\"flex-grow-1\">\n            <div className=\"d-flex justify-content-between align-items-center\">\n              <div>\n                <strong>{user.fullName || user.username}</strong>\n                <br />\n                <small className=\"text-muted\">{user.email}</small>\n              </div>\n              <div>\n                <Badge bg={getRoleBadgeColor(role)}>{role}</Badge>\n                {user.isActive !== undefined && (\n                  <Badge bg={user.isActive ? 'success' : 'secondary'} className=\"ms-1\">\n                    {user.isActive ? 'Active' : 'Inactive'}\n                  </Badge>\n                )}\n              </div>\n            </div>\n          </div>\n        </div>\n      </Card.Body>\n    </Card>\n  );\n\n  if (loading) {\n    return (\n      <Container className=\"d-flex justify-content-center align-items-center\" style={{ minHeight: '400px' }}>\n        <Spinner animation=\"border\" role=\"status\">\n          <span className=\"visually-hidden\">Loading...</span>\n        </Spinner>\n      </Container>\n    );\n  }\n\n  return (\n    <Container fluid className=\"p-4\">\n      <Row className=\"mb-4\">\n        <Col>\n          <div className=\"d-flex justify-content-between align-items-center\">\n            <div>\n              <h2><FaSitemap className=\"me-2\" />Hierarchy View</h2>\n              <p className=\"text-muted\">Organizational structure and user relationships</p>\n            </div>\n          </div>\n        </Col>\n      </Row>\n\n      {alert.show && (\n        <Row className=\"mb-3\">\n          <Col>\n            <Alert variant={alert.type} dismissible onClose={() => setAlert({ show: false, message: '', type: 'success' })}>\n              {alert.message}\n            </Alert>\n          </Col>\n        </Row>\n      )}\n\n      <Row>\n        {/* Admin Level */}\n        {hierarchy.Admin && hierarchy.Admin.length > 0 && (\n          <Col lg={3} md={6} className=\"mb-4\">\n            <Card className=\"h-100\">\n              <Card.Header className=\"bg-primary text-white\">\n                <h6 className=\"mb-0\">\n                  <FaBuilding className=\"me-2\" />\n                  Administrators ({hierarchy.Admin.length})\n                </h6>\n              </Card.Header>\n              <Card.Body className=\"p-2\">\n                {hierarchy.Admin.map((admin) => (\n                  <UserCard key={admin.userId || admin.username} user={admin} role=\"Admin\" />\n                ))}\n              </Card.Body>\n            </Card>\n          </Col>\n        )}\n\n        {/* Client Level */}\n        {hierarchy.Client && hierarchy.Client.length > 0 && (\n          <Col lg={3} md={6} className=\"mb-4\">\n            <Card className=\"h-100\">\n              <Card.Header className=\"bg-success text-white\">\n                <h6 className=\"mb-0\">\n                  <FaUserTie className=\"me-2\" />\n                  Clients ({hierarchy.Client.length})\n                </h6>\n              </Card.Header>\n              <Card.Body className=\"p-2\">\n                {hierarchy.Client.map((client) => (\n                  <UserCard key={client.userId || client.username} user={client} role=\"Client\" />\n                ))}\n              </Card.Body>\n            </Card>\n          </Col>\n        )}\n\n        {/* Supervisor Level */}\n        {hierarchy.Supervisor && hierarchy.Supervisor.length > 0 && (\n          <Col lg={3} md={6} className=\"mb-4\">\n            <Card className=\"h-100\">\n              <Card.Header className=\"bg-warning text-dark\">\n                <h6 className=\"mb-0\">\n                  <FaUserFriends className=\"me-2\" />\n                  Supervisors ({hierarchy.Supervisor.length})\n                </h6>\n              </Card.Header>\n              <Card.Body className=\"p-2\">\n                {hierarchy.Supervisor.map((supervisor) => (\n                  <UserCard key={supervisor.userId || supervisor.username} user={supervisor} role=\"Supervisor\" />\n                ))}\n              </Card.Body>\n            </Card>\n          </Col>\n        )}\n\n        {/* Field Officer Level */}\n        {hierarchy['Field Officer'] && hierarchy['Field Officer'].length > 0 && (\n          <Col lg={3} md={6} className=\"mb-4\">\n            <Card className=\"h-100\">\n              <Card.Header className=\"bg-info text-white\">\n                <h6 className=\"mb-0\">\n                  <FaHardHat className=\"me-2\" />\n                  Field Officers ({hierarchy['Field Officer'].length})\n                </h6>\n              </Card.Header>\n              <Card.Body className=\"p-2\">\n                {hierarchy['Field Officer'].map((fieldOfficer) => (\n                  <UserCard key={fieldOfficer.userId || fieldOfficer.username} user={fieldOfficer} role=\"Field Officer\" />\n                ))}\n              </Card.Body>\n            </Card>\n          </Col>\n        )}\n      </Row>\n\n      {/* Summary Statistics */}\n      <Row className=\"mt-4\">\n        <Col>\n          <Card>\n            <Card.Header>\n              <h6 className=\"mb-0\">Summary Statistics</h6>\n            </Card.Header>\n            <Card.Body>\n              <Row className=\"text-center\">\n                <Col md={3}>\n                  <div className=\"border-end\">\n                    <h4 className=\"text-primary\">{hierarchy.Admin?.length || 0}</h4>\n                    <small className=\"text-muted\">Administrators</small>\n                  </div>\n                </Col>\n                <Col md={3}>\n                  <div className=\"border-end\">\n                    <h4 className=\"text-success\">{hierarchy.Client?.length || 0}</h4>\n                    <small className=\"text-muted\">Clients</small>\n                  </div>\n                </Col>\n                <Col md={3}>\n                  <div className=\"border-end\">\n                    <h4 className=\"text-warning\">{hierarchy.Supervisor?.length || 0}</h4>\n                    <small className=\"text-muted\">Supervisors</small>\n                  </div>\n                </Col>\n                <Col md={3}>\n                  <h4 className=\"text-info\">{hierarchy['Field Officer']?.length || 0}</h4>\n                  <small className=\"text-muted\">Field Officers</small>\n                </Col>\n              </Row>\n            </Card.Body>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* Empty State */}\n      {Object.keys(hierarchy).length === 0 && (\n        <Row>\n          <Col>\n            <Card>\n              <Card.Body className=\"text-center py-5\">\n                <FaSitemap size={48} className=\"text-muted mb-3\" />\n                <h5 className=\"text-muted\">No hierarchy data available</h5>\n                <p className=\"text-muted\">\n                  Users and organizational structure will appear here once data is available.\n                </p>\n              </Card.Body>\n            </Card>\n          </Col>\n        </Row>\n      )}\n    </Container>\n  );\n};\n\nexport default HierarchyView;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAEC,OAAO,EAAEC,KAAK,QAAQ,iBAAiB;AAClF,SAASC,SAAS,EAAEC,SAAS,EAAEC,aAAa,EAAEC,SAAS,EAAEC,UAAU,QAAQ,gBAAgB;AAC3F,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,aAAa,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,gBAAA,EAAAC,iBAAA,EAAAC,qBAAA,EAAAC,oBAAA;EAClC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGzB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC9C,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC4B,KAAK,EAAEC,QAAQ,CAAC,GAAG7B,QAAQ,CAAC;IAAE8B,IAAI,EAAE,KAAK;IAAEC,OAAO,EAAE,EAAE;IAAEC,IAAI,EAAE;EAAU,CAAC,CAAC;EAEjF/B,SAAS,CAAC,MAAM;IACdgC,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACFN,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMO,QAAQ,GAAG,MAAMpB,KAAK,CAACqB,GAAG,CAAC,sCAAsC,EAAE;QACvEC,MAAM,EAAE;UACNC,SAAS,EAAEnB,IAAI,CAACoB,IAAI;UACpBC,OAAO,EAAErB,IAAI,CAACsB,MAAM,IAAItB,IAAI,CAACuB,QAAQ;UACrCC,SAAS,EAAExB,IAAI,CAACyB;QAClB;MACF,CAAC,CAAC;MAEF,IAAIT,QAAQ,CAACU,IAAI,CAACC,OAAO,EAAE;QACzBpB,YAAY,CAACS,QAAQ,CAACU,IAAI,CAACpB,SAAS,CAAC;MACvC,CAAC,MAAM;QACLsB,SAAS,CAAC,2BAA2B,EAAE,QAAQ,CAAC;MAClD;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDD,SAAS,CAAC,0BAA0B,EAAE,QAAQ,CAAC;IACjD,CAAC,SAAS;MACRnB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMmB,SAAS,GAAGA,CAACf,OAAO,EAAEC,IAAI,GAAG,SAAS,KAAK;IAC/CH,QAAQ,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO;MAAEC;IAAK,CAAC,CAAC;IACvCiB,UAAU,CAAC,MAAMpB,QAAQ,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE,EAAE;MAAEC,IAAI,EAAE;IAAU,CAAC,CAAC,EAAE,IAAI,CAAC;EACjF,CAAC;EAED,MAAMkB,WAAW,GAAIZ,IAAI,IAAK;IAC5B,QAAQA,IAAI;MACV,KAAK,OAAO;QACV,oBAAOtB,OAAA,CAACH,UAAU;UAACsC,SAAS,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAChD,KAAK,QAAQ;QACX,oBAAOvC,OAAA,CAACN,SAAS;UAACyC,SAAS,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC/C,KAAK,YAAY;QACf,oBAAOvC,OAAA,CAACL,aAAa;UAACwC,SAAS,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACnD,KAAK,eAAe;QAClB,oBAAOvC,OAAA,CAACJ,SAAS;UAACuC,SAAS,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC5C;QACE,oBAAOvC,OAAA,CAACL,aAAa;UAACwC,SAAS,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IACvD;EACF,CAAC;EAED,MAAMC,iBAAiB,GAAIlB,IAAI,IAAK;IAClC,QAAQA,IAAI;MACV,KAAK,OAAO;QACV,OAAO,SAAS;MAClB,KAAK,QAAQ;QACX,OAAO,SAAS;MAClB,KAAK,YAAY;QACf,OAAO,SAAS;MAClB,KAAK,eAAe;QAClB,OAAO,MAAM;MACf;QACE,OAAO,WAAW;IACtB;EACF,CAAC;EAED,MAAMmB,QAAQ,GAAGA,CAAC;IAAEvC,IAAI;IAAEoB;EAAK,CAAC,kBAC9BtB,OAAA,CAACX,IAAI;IAAC8C,SAAS,EAAC,gBAAgB;IAAAO,QAAA,eAC9B1C,OAAA,CAACX,IAAI,CAACsD,IAAI;MAACR,SAAS,EAAC,MAAM;MAAAO,QAAA,eACzB1C,OAAA;QAAKmC,SAAS,EAAC,2BAA2B;QAAAO,QAAA,gBACxC1C,OAAA;UAAKmC,SAAS,EAAC,MAAM;UAAAO,QAAA,EAClBR,WAAW,CAACZ,IAAI;QAAC;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eACNvC,OAAA;UAAKmC,SAAS,EAAC,aAAa;UAAAO,QAAA,eAC1B1C,OAAA;YAAKmC,SAAS,EAAC,mDAAmD;YAAAO,QAAA,gBAChE1C,OAAA;cAAA0C,QAAA,gBACE1C,OAAA;gBAAA0C,QAAA,EAASxC,IAAI,CAAC0C,QAAQ,IAAI1C,IAAI,CAACuB;cAAQ;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,eACjDvC,OAAA;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNvC,OAAA;gBAAOmC,SAAS,EAAC,YAAY;gBAAAO,QAAA,EAAExC,IAAI,CAAC2C;cAAK;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,eACNvC,OAAA;cAAA0C,QAAA,gBACE1C,OAAA,CAACR,KAAK;gBAACsD,EAAE,EAAEN,iBAAiB,CAAClB,IAAI,CAAE;gBAAAoB,QAAA,EAAEpB;cAAI;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACjDrC,IAAI,CAAC6C,QAAQ,KAAKC,SAAS,iBAC1BhD,OAAA,CAACR,KAAK;gBAACsD,EAAE,EAAE5C,IAAI,CAAC6C,QAAQ,GAAG,SAAS,GAAG,WAAY;gBAACZ,SAAS,EAAC,MAAM;gBAAAO,QAAA,EACjExC,IAAI,CAAC6C,QAAQ,GAAG,QAAQ,GAAG;cAAU;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CACR;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CACP;EAED,IAAI7B,OAAO,EAAE;IACX,oBACEV,OAAA,CAACd,SAAS;MAACiD,SAAS,EAAC,kDAAkD;MAACc,KAAK,EAAE;QAAEC,SAAS,EAAE;MAAQ,CAAE;MAAAR,QAAA,eACpG1C,OAAA,CAACT,OAAO;QAAC4D,SAAS,EAAC,QAAQ;QAAC7B,IAAI,EAAC,QAAQ;QAAAoB,QAAA,eACvC1C,OAAA;UAAMmC,SAAS,EAAC,iBAAiB;UAAAO,QAAA,EAAC;QAAU;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAEhB;EAEA,oBACEvC,OAAA,CAACd,SAAS;IAACkE,KAAK;IAACjB,SAAS,EAAC,KAAK;IAAAO,QAAA,gBAC9B1C,OAAA,CAACb,GAAG;MAACgD,SAAS,EAAC,MAAM;MAAAO,QAAA,eACnB1C,OAAA,CAACZ,GAAG;QAAAsD,QAAA,eACF1C,OAAA;UAAKmC,SAAS,EAAC,mDAAmD;UAAAO,QAAA,eAChE1C,OAAA;YAAA0C,QAAA,gBACE1C,OAAA;cAAA0C,QAAA,gBAAI1C,OAAA,CAACP,SAAS;gBAAC0C,SAAS,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,kBAAc;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrDvC,OAAA;cAAGmC,SAAS,EAAC,YAAY;cAAAO,QAAA,EAAC;YAA+C;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1E;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAEL3B,KAAK,CAACE,IAAI,iBACTd,OAAA,CAACb,GAAG;MAACgD,SAAS,EAAC,MAAM;MAAAO,QAAA,eACnB1C,OAAA,CAACZ,GAAG;QAAAsD,QAAA,eACF1C,OAAA,CAACV,KAAK;UAAC+D,OAAO,EAAEzC,KAAK,CAACI,IAAK;UAACsC,WAAW;UAACC,OAAO,EAAEA,CAAA,KAAM1C,QAAQ,CAAC;YAAEC,IAAI,EAAE,KAAK;YAAEC,OAAO,EAAE,EAAE;YAAEC,IAAI,EAAE;UAAU,CAAC,CAAE;UAAA0B,QAAA,EAC5G9B,KAAK,CAACG;QAAO;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDvC,OAAA,CAACb,GAAG;MAAAuD,QAAA,GAEDlC,SAAS,CAACgD,KAAK,IAAIhD,SAAS,CAACgD,KAAK,CAACC,MAAM,GAAG,CAAC,iBAC5CzD,OAAA,CAACZ,GAAG;QAACsE,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAACxB,SAAS,EAAC,MAAM;QAAAO,QAAA,eACjC1C,OAAA,CAACX,IAAI;UAAC8C,SAAS,EAAC,OAAO;UAAAO,QAAA,gBACrB1C,OAAA,CAACX,IAAI,CAACuE,MAAM;YAACzB,SAAS,EAAC,uBAAuB;YAAAO,QAAA,eAC5C1C,OAAA;cAAImC,SAAS,EAAC,MAAM;cAAAO,QAAA,gBAClB1C,OAAA,CAACH,UAAU;gBAACsC,SAAS,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,oBACf,EAAC/B,SAAS,CAACgD,KAAK,CAACC,MAAM,EAAC,GAC1C;YAAA;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eACdvC,OAAA,CAACX,IAAI,CAACsD,IAAI;YAACR,SAAS,EAAC,KAAK;YAAAO,QAAA,EACvBlC,SAAS,CAACgD,KAAK,CAACK,GAAG,CAAEC,KAAK,iBACzB9D,OAAA,CAACyC,QAAQ;cAAsCvC,IAAI,EAAE4D,KAAM;cAACxC,IAAI,EAAC;YAAO,GAAzDwC,KAAK,CAACtC,MAAM,IAAIsC,KAAK,CAACrC,QAAQ;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAA6B,CAC3E;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN,EAGA/B,SAAS,CAACuD,MAAM,IAAIvD,SAAS,CAACuD,MAAM,CAACN,MAAM,GAAG,CAAC,iBAC9CzD,OAAA,CAACZ,GAAG;QAACsE,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAACxB,SAAS,EAAC,MAAM;QAAAO,QAAA,eACjC1C,OAAA,CAACX,IAAI;UAAC8C,SAAS,EAAC,OAAO;UAAAO,QAAA,gBACrB1C,OAAA,CAACX,IAAI,CAACuE,MAAM;YAACzB,SAAS,EAAC,uBAAuB;YAAAO,QAAA,eAC5C1C,OAAA;cAAImC,SAAS,EAAC,MAAM;cAAAO,QAAA,gBAClB1C,OAAA,CAACN,SAAS;gBAACyC,SAAS,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,aACrB,EAAC/B,SAAS,CAACuD,MAAM,CAACN,MAAM,EAAC,GACpC;YAAA;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eACdvC,OAAA,CAACX,IAAI,CAACsD,IAAI;YAACR,SAAS,EAAC,KAAK;YAAAO,QAAA,EACvBlC,SAAS,CAACuD,MAAM,CAACF,GAAG,CAAEG,MAAM,iBAC3BhE,OAAA,CAACyC,QAAQ;cAAwCvC,IAAI,EAAE8D,MAAO;cAAC1C,IAAI,EAAC;YAAQ,GAA7D0C,MAAM,CAACxC,MAAM,IAAIwC,MAAM,CAACvC,QAAQ;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAA+B,CAC/E;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN,EAGA/B,SAAS,CAACyD,UAAU,IAAIzD,SAAS,CAACyD,UAAU,CAACR,MAAM,GAAG,CAAC,iBACtDzD,OAAA,CAACZ,GAAG;QAACsE,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAACxB,SAAS,EAAC,MAAM;QAAAO,QAAA,eACjC1C,OAAA,CAACX,IAAI;UAAC8C,SAAS,EAAC,OAAO;UAAAO,QAAA,gBACrB1C,OAAA,CAACX,IAAI,CAACuE,MAAM;YAACzB,SAAS,EAAC,sBAAsB;YAAAO,QAAA,eAC3C1C,OAAA;cAAImC,SAAS,EAAC,MAAM;cAAAO,QAAA,gBAClB1C,OAAA,CAACL,aAAa;gBAACwC,SAAS,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,iBACrB,EAAC/B,SAAS,CAACyD,UAAU,CAACR,MAAM,EAAC,GAC5C;YAAA;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eACdvC,OAAA,CAACX,IAAI,CAACsD,IAAI;YAACR,SAAS,EAAC,KAAK;YAAAO,QAAA,EACvBlC,SAAS,CAACyD,UAAU,CAACJ,GAAG,CAAEK,UAAU,iBACnClE,OAAA,CAACyC,QAAQ;cAAgDvC,IAAI,EAAEgE,UAAW;cAAC5C,IAAI,EAAC;YAAY,GAA7E4C,UAAU,CAAC1C,MAAM,IAAI0C,UAAU,CAACzC,QAAQ;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAuC,CAC/F;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN,EAGA/B,SAAS,CAAC,eAAe,CAAC,IAAIA,SAAS,CAAC,eAAe,CAAC,CAACiD,MAAM,GAAG,CAAC,iBAClEzD,OAAA,CAACZ,GAAG;QAACsE,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAACxB,SAAS,EAAC,MAAM;QAAAO,QAAA,eACjC1C,OAAA,CAACX,IAAI;UAAC8C,SAAS,EAAC,OAAO;UAAAO,QAAA,gBACrB1C,OAAA,CAACX,IAAI,CAACuE,MAAM;YAACzB,SAAS,EAAC,oBAAoB;YAAAO,QAAA,eACzC1C,OAAA;cAAImC,SAAS,EAAC,MAAM;cAAAO,QAAA,gBAClB1C,OAAA,CAACJ,SAAS;gBAACuC,SAAS,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,oBACd,EAAC/B,SAAS,CAAC,eAAe,CAAC,CAACiD,MAAM,EAAC,GACrD;YAAA;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eACdvC,OAAA,CAACX,IAAI,CAACsD,IAAI;YAACR,SAAS,EAAC,KAAK;YAAAO,QAAA,EACvBlC,SAAS,CAAC,eAAe,CAAC,CAACqD,GAAG,CAAEM,YAAY,iBAC3CnE,OAAA,CAACyC,QAAQ;cAAoDvC,IAAI,EAAEiE,YAAa;cAAC7C,IAAI,EAAC;YAAe,GAAtF6C,YAAY,CAAC3C,MAAM,IAAI2C,YAAY,CAAC1C,QAAQ;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAA4C,CACxG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNvC,OAAA,CAACb,GAAG;MAACgD,SAAS,EAAC,MAAM;MAAAO,QAAA,eACnB1C,OAAA,CAACZ,GAAG;QAAAsD,QAAA,eACF1C,OAAA,CAACX,IAAI;UAAAqD,QAAA,gBACH1C,OAAA,CAACX,IAAI,CAACuE,MAAM;YAAAlB,QAAA,eACV1C,OAAA;cAAImC,SAAS,EAAC,MAAM;cAAAO,QAAA,EAAC;YAAkB;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACdvC,OAAA,CAACX,IAAI,CAACsD,IAAI;YAAAD,QAAA,eACR1C,OAAA,CAACb,GAAG;cAACgD,SAAS,EAAC,aAAa;cAAAO,QAAA,gBAC1B1C,OAAA,CAACZ,GAAG;gBAACuE,EAAE,EAAE,CAAE;gBAAAjB,QAAA,eACT1C,OAAA;kBAAKmC,SAAS,EAAC,YAAY;kBAAAO,QAAA,gBACzB1C,OAAA;oBAAImC,SAAS,EAAC,cAAc;oBAAAO,QAAA,EAAE,EAAAtC,gBAAA,GAAAI,SAAS,CAACgD,KAAK,cAAApD,gBAAA,uBAAfA,gBAAA,CAAiBqD,MAAM,KAAI;kBAAC;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAChEvC,OAAA;oBAAOmC,SAAS,EAAC,YAAY;oBAAAO,QAAA,EAAC;kBAAc;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvC,OAAA,CAACZ,GAAG;gBAACuE,EAAE,EAAE,CAAE;gBAAAjB,QAAA,eACT1C,OAAA;kBAAKmC,SAAS,EAAC,YAAY;kBAAAO,QAAA,gBACzB1C,OAAA;oBAAImC,SAAS,EAAC,cAAc;oBAAAO,QAAA,EAAE,EAAArC,iBAAA,GAAAG,SAAS,CAACuD,MAAM,cAAA1D,iBAAA,uBAAhBA,iBAAA,CAAkBoD,MAAM,KAAI;kBAAC;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACjEvC,OAAA;oBAAOmC,SAAS,EAAC,YAAY;oBAAAO,QAAA,EAAC;kBAAO;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvC,OAAA,CAACZ,GAAG;gBAACuE,EAAE,EAAE,CAAE;gBAAAjB,QAAA,eACT1C,OAAA;kBAAKmC,SAAS,EAAC,YAAY;kBAAAO,QAAA,gBACzB1C,OAAA;oBAAImC,SAAS,EAAC,cAAc;oBAAAO,QAAA,EAAE,EAAApC,qBAAA,GAAAE,SAAS,CAACyD,UAAU,cAAA3D,qBAAA,uBAApBA,qBAAA,CAAsBmD,MAAM,KAAI;kBAAC;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACrEvC,OAAA;oBAAOmC,SAAS,EAAC,YAAY;oBAAAO,QAAA,EAAC;kBAAW;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvC,OAAA,CAACZ,GAAG;gBAACuE,EAAE,EAAE,CAAE;gBAAAjB,QAAA,gBACT1C,OAAA;kBAAImC,SAAS,EAAC,WAAW;kBAAAO,QAAA,EAAE,EAAAnC,oBAAA,GAAAC,SAAS,CAAC,eAAe,CAAC,cAAAD,oBAAA,uBAA1BA,oBAAA,CAA4BkD,MAAM,KAAI;gBAAC;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACxEvC,OAAA;kBAAOmC,SAAS,EAAC,YAAY;kBAAAO,QAAA,EAAC;gBAAc;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL6B,MAAM,CAACC,IAAI,CAAC7D,SAAS,CAAC,CAACiD,MAAM,KAAK,CAAC,iBAClCzD,OAAA,CAACb,GAAG;MAAAuD,QAAA,eACF1C,OAAA,CAACZ,GAAG;QAAAsD,QAAA,eACF1C,OAAA,CAACX,IAAI;UAAAqD,QAAA,eACH1C,OAAA,CAACX,IAAI,CAACsD,IAAI;YAACR,SAAS,EAAC,kBAAkB;YAAAO,QAAA,gBACrC1C,OAAA,CAACP,SAAS;cAAC6E,IAAI,EAAE,EAAG;cAACnC,SAAS,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnDvC,OAAA;cAAImC,SAAS,EAAC,YAAY;cAAAO,QAAA,EAAC;YAA2B;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3DvC,OAAA;cAAGmC,SAAS,EAAC,YAAY;cAAAO,QAAA,EAAC;YAE1B;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACQ,CAAC;AAEhB,CAAC;AAACpC,EAAA,CAvQIF,aAAa;AAAAsE,EAAA,GAAbtE,aAAa;AAyQnB,eAAeA,aAAa;AAAC,IAAAsE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}