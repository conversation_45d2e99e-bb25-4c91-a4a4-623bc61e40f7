.sidebar {
  width: 250px !important;
  min-width: 250px !important; /* Ensure sidebar doesn't shrink */
  max-width: 250px !important;
  height: 100vh;
  background-color: #2c3e50;
  color: white;
  display: flex;
  flex-direction: column;
  transition: transform 0.3s ease;
  z-index: 1000;
  flex-shrink: 0 !important; /* Prevent sidebar from shrinking */
  position: fixed; /* Fix sidebar position */
  top: 0;
  left: 0;
  overflow-y: auto; /* Allow sidebar content to scroll if needed */
}

.sidebar-header {
  padding: 20px 15px;
  border-bottom: 1px solid #34495e;
  text-align: center;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sidebar-header h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.sidebar-menu {
  flex-grow: 1;
  padding: 10px 0;
  overflow-y: auto;
}

.sidebar-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: #ecf0f1;
  text-decoration: none;
  transition: background-color 0.3s, color 0.3s;
  border-left: 3px solid transparent;
}

.sidebar-item:hover {
  background-color: #34495e;
  color: white;
}

.sidebar-item.active {
  background-color: #34495e;
  color: #FF9B33;
  border-left-color: #FF9B33;
}

.sidebar-icon {
  margin-right: 10px;
  display: flex;
  align-items: center;
}

.sidebar-text {
  font-size: 0.95rem;
  flex-grow: 1;
}

/* Collapsible Menu Styles */
.sidebar-collapsible {
  margin: 0;
}

.collapsible-header {
  cursor: pointer;
  position: relative;
  justify-content: space-between;
}

.collapsible-header:hover {
  background-color: #34495e;
}

.collapsible-header.open {
  background-color: #34495e;
  color: #FF9B33;
}

.sidebar-chevron {
  display: flex;
  align-items: center;
  margin-left: auto;
  transition: transform 0.3s ease;
}

.sidebar-submenu {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
  background-color: #1a252f;
}

.sidebar-submenu.open {
  max-height: 300px; /* Adjust based on number of items */
}

.sidebar-subitem {
  display: flex;
  align-items: center;
  padding: 10px 20px 10px 45px; /* Extra left padding for indentation */
  color: #bdc3c7;
  text-decoration: none;
  transition: background-color 0.3s, color 0.3s;
  border-left: 3px solid transparent;
  font-size: 0.9rem;
}

.sidebar-subitem:hover {
  background-color: #2c3e50;
  color: white;
}

.sidebar-subitem.active {
  background-color: #2c3e50;
  color: #FF9B33;
  border-left-color: #FF9B33;
}

.sidebar-subitem .sidebar-icon {
  margin-right: 8px;
}

.sidebar-subitem .sidebar-text {
  font-size: 0.85rem;
}

.sidebar-footer {
  padding: 15px;
  border-top: 1px solid #34495e;
}

.sidebar-logout {
  display: flex;
  align-items: center;
  background: none;
  border: none;
  color: #ecf0f1;
  padding: 10px 0;
  cursor: pointer;
  width: 100%;
  text-align: left;
  transition: color 0.3s;
}

.sidebar-logout:hover {
  color: #FF9B33;
}

.sidebar-watermark {
  font-size: 0.8rem;
  color: #7f8c8d;
  text-align: center;
  margin-top: 15px;
}

/* Mobile sidebar toggle button */
.sidebar-toggle {
  position: fixed;
  top: 15px;
  left: 15px;
  z-index: 1001;
  border: none;
  background-color: #2c3e50;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.sidebar-close {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 5px;
}

/* Sidebar backdrop for mobile */
.sidebar-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: none;
}

.sidebar-backdrop.show {
  display: block;
}

/* Mobile navigation bar */
.mobile-nav {
  display: none;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #2c3e50;
  display: flex;
  justify-content: space-around;
  padding: 8px 0;
  box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.1);
  z-index: 999;
}

.mobile-nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-decoration: none;
  color: #ecf0f1;
  flex: 1;
  padding: 5px 0;
}

.mobile-nav-item.active {
  color: #FF9B33;
}

.mobile-nav-icon {
  margin-bottom: 4px;
}

.mobile-nav-text {
  font-size: 0.7rem;
  text-align: center;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    width: 80vw;
    max-width: 300px;
    min-width: unset;
    z-index: 1001;
    transform: translateX(-100%);
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
  }

  .sidebar.open {
    transform: translateX(0);
  }

  .sidebar.closed {
    transform: translateX(-100%);
  }

  /* Main content always full width on mobile */
  .content-container {
    margin-left: 0 !important;
    width: 100vw !important;
    max-width: 100vw !important;
    left: 0;
    right: 0;
    padding: 10px;
    background-color: #fff;
    height: calc(100vh - 60px);
    overflow-y: auto;
    box-sizing: border-box;
  }

  /* Show mobile bottom navigation */
  .mobile-nav {
    display: flex;
  }

  /* Mobile adjustments for collapsible menu */
  .sidebar-chevron {
    font-size: 12px;
  }

  .sidebar-submenu.open {
    max-height: 250px;
  }

  .sidebar-subitem {
    padding: 8px 15px 8px 35px;
    font-size: 0.8rem;
  }
}