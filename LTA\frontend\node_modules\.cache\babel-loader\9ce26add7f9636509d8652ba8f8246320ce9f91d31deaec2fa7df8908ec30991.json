{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Deep Learning\\\\LTAGIT\\\\LTA\\\\frontend\\\\src\\\\pages\\\\Clients.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Button, Table, Modal, Form, Alert, Badge } from 'react-bootstrap';\nimport { FaPlus, FaEdit, FaTrash, FaEye, FaBuilding, FaUsers, FaProjectDiagram } from 'react-icons/fa';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Clients = () => {\n  _s();\n  const [clients, setClients] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [showModal, setShowModal] = useState(false);\n  const [modalMode, setModalMode] = useState('create'); // 'create', 'edit', 'view'\n  const [selectedClient, setSelectedClient] = useState(null);\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    contactEmail: '',\n    contactPhone: '',\n    address: ''\n  });\n  const currentUser = JSON.parse(sessionStorage.getItem('currentUser') || '{}');\n  useEffect(() => {\n    fetchClients();\n  }, []);\n  const fetchClients = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get('/api/clients', {\n        params: {\n          user_role: currentUser.role,\n          user_id: currentUser.id\n        }\n      });\n      if (response.data.success) {\n        setClients(response.data.clients);\n      } else {\n        setError(response.data.message || 'Failed to fetch clients');\n      }\n    } catch (err) {\n      var _err$response, _err$response$data;\n      setError('Error fetching clients: ' + (((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || err.message));\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleShowModal = (mode, client = null) => {\n    setModalMode(mode);\n    setSelectedClient(client);\n    if (mode === 'create') {\n      setFormData({\n        name: '',\n        description: '',\n        contactEmail: '',\n        contactPhone: '',\n        address: ''\n      });\n    } else if (client) {\n      setFormData({\n        name: client.name || '',\n        description: client.description || '',\n        contactEmail: client.contactEmail || '',\n        contactPhone: client.contactPhone || '',\n        address: client.address || ''\n      });\n    }\n    setShowModal(true);\n  };\n  const handleCloseModal = () => {\n    setShowModal(false);\n    setSelectedClient(null);\n    setError('');\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    try {\n      const payload = {\n        ...formData,\n        user_id: currentUser.id,\n        user_role: currentUser.role\n      };\n      let response;\n      if (modalMode === 'create') {\n        response = await axios.post('/api/clients', payload);\n      } else if (modalMode === 'edit') {\n        response = await axios.put(`/api/clients/${selectedClient._id}`, payload);\n      }\n      if (response.data.success) {\n        await fetchClients();\n        handleCloseModal();\n      } else {\n        setError(response.data.message || 'Operation failed');\n      }\n    } catch (err) {\n      var _err$response2, _err$response2$data;\n      setError('Error: ' + (((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.message) || err.message));\n    }\n  };\n  const handleDelete = async clientId => {\n    if (!window.confirm('Are you sure you want to delete this client? This action cannot be undone.')) {\n      return;\n    }\n    try {\n      const response = await axios.delete(`/api/clients/${clientId}`, {\n        params: {\n          user_id: currentUser.id,\n          user_role: currentUser.role\n        }\n      });\n      if (response.data.success) {\n        await fetchClients();\n      } else {\n        setError(response.data.message || 'Failed to delete client');\n      }\n    } catch (err) {\n      var _err$response3, _err$response3$data;\n      setError('Error deleting client: ' + (((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : (_err$response3$data = _err$response3.data) === null || _err$response3$data === void 0 ? void 0 : _err$response3$data.message) || err.message));\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Container, {\n      className: \"mt-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"spinner-border\",\n          role: \"status\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"visually-hidden\",\n            children: \"Loading...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Container, {\n    fluid: true,\n    className: \"mt-4\",\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-between align-items-center mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: [/*#__PURE__*/_jsxDEV(FaBuilding, {\n              className: \"me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 17\n            }, this), \"Client Management\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this), currentUser.role === 'Admin' && /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"primary\",\n            onClick: () => handleShowModal('create'),\n            children: [/*#__PURE__*/_jsxDEV(FaPlus, {\n              className: \"me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 17\n            }, this), \"Add New Client\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"danger\",\n          dismissible: true,\n          onClose: () => setError(''),\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: /*#__PURE__*/_jsxDEV(Table, {\n              responsive: true,\n              striped: true,\n              hover: true,\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Client Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 175,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Contact Email\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 176,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Contact Phone\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 177,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Supervisors\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 178,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Field Officers\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 179,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Projects\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 180,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Actions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 181,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: clients.length === 0 ? /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: /*#__PURE__*/_jsxDEV(\"td\", {\n                    colSpan: \"7\",\n                    className: \"text-center\",\n                    children: \"No clients found\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 187,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 21\n                }, this) : clients.map(client => /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: client.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 193,\n                      columnNumber: 27\n                    }, this), client.description && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-muted small\",\n                      children: client.description\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 195,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 192,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: client.contactEmail\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 198,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: client.contactPhone || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 199,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(Badge, {\n                      bg: \"info\",\n                      children: client.supervisorCount || 0\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 201,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 200,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(Badge, {\n                      bg: \"success\",\n                      children: client.fieldOfficerCount || 0\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 204,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 203,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(Badge, {\n                      bg: \"warning\",\n                      children: client.projectCount || 0\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 207,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 206,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex gap-2\",\n                      children: [/*#__PURE__*/_jsxDEV(Button, {\n                        variant: \"outline-info\",\n                        size: \"sm\",\n                        onClick: () => handleShowModal('view', client),\n                        children: /*#__PURE__*/_jsxDEV(FaEye, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 216,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 211,\n                        columnNumber: 29\n                      }, this), currentUser.role === 'Admin' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(Button, {\n                          variant: \"outline-warning\",\n                          size: \"sm\",\n                          onClick: () => handleShowModal('edit', client),\n                          children: /*#__PURE__*/_jsxDEV(FaEdit, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 225,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 220,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(Button, {\n                          variant: \"outline-danger\",\n                          size: \"sm\",\n                          onClick: () => handleDelete(client._id),\n                          children: /*#__PURE__*/_jsxDEV(FaTrash, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 232,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 227,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 210,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 209,\n                    columnNumber: 25\n                  }, this)]\n                }, client._id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showModal,\n      onHide: handleCloseModal,\n      size: \"lg\",\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: [modalMode === 'create' && 'Add New Client', modalMode === 'edit' && 'Edit Client', modalMode === 'view' && 'Client Details']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"danger\",\n          dismissible: true,\n          onClose: () => setError(''),\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form, {\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Client Name *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 268,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"text\",\n                  name: \"name\",\n                  value: formData.name,\n                  onChange: handleInputChange,\n                  required: true,\n                  disabled: modalMode === 'view'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Contact Email *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"email\",\n                  name: \"contactEmail\",\n                  value: formData.contactEmail,\n                  onChange: handleInputChange,\n                  required: true,\n                  disabled: modalMode === 'view'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Contact Phone\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 297,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"tel\",\n                  name: \"contactPhone\",\n                  value: formData.contactPhone,\n                  onChange: handleInputChange,\n                  disabled: modalMode === 'view'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Address\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"text\",\n                  name: \"address\",\n                  value: formData.address,\n                  onChange: handleInputChange,\n                  disabled: modalMode === 'view'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              as: \"textarea\",\n              rows: 3,\n              name: \"description\",\n              value: formData.description,\n              onChange: handleInputChange,\n              disabled: modalMode === 'view'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: handleCloseModal,\n          children: modalMode === 'view' ? 'Close' : 'Cancel'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 11\n        }, this), modalMode !== 'view' && /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: handleSubmit,\n          children: modalMode === 'create' ? 'Create Client' : 'Update Client'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 334,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 249,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 152,\n    columnNumber: 5\n  }, this);\n};\n_s(Clients, \"2mxdsi2ZO4FiHPF0RWa10+6KjOQ=\");\n_c = Clients;\nexport default Clients;\nvar _c;\n$RefreshReg$(_c, \"Clients\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "<PERSON><PERSON>", "Table", "Modal", "Form", "<PERSON><PERSON>", "Badge", "FaPlus", "FaEdit", "FaTrash", "FaEye", "FaBuilding", "FaUsers", "FaProjectDiagram", "axios", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Clients", "_s", "clients", "setClients", "loading", "setLoading", "error", "setError", "showModal", "setShowModal", "modalMode", "setModalMode", "selectedClient", "setSelectedClient", "formData", "setFormData", "name", "description", "contactEmail", "contactPhone", "address", "currentUser", "JSON", "parse", "sessionStorage", "getItem", "fetchClients", "response", "get", "params", "user_role", "role", "user_id", "id", "data", "success", "message", "err", "_err$response", "_err$response$data", "handleShowModal", "mode", "client", "handleCloseModal", "handleInputChange", "e", "value", "target", "prev", "handleSubmit", "preventDefault", "payload", "post", "put", "_id", "_err$response2", "_err$response2$data", "handleDelete", "clientId", "window", "confirm", "delete", "_err$response3", "_err$response3$data", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fluid", "variant", "onClick", "dismissible", "onClose", "Body", "responsive", "striped", "hover", "length", "colSpan", "map", "bg", "supervisorCount", "fieldOfficerCount", "projectCount", "size", "show", "onHide", "Header", "closeButton", "Title", "onSubmit", "md", "Group", "Label", "Control", "type", "onChange", "required", "disabled", "as", "rows", "Footer", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Deep Learning/LTAGIT/LTA/frontend/src/pages/Clients.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Con<PERSON><PERSON>, <PERSON>, Col, Card, Button, Table, Modal, Form, Alert, Badge } from 'react-bootstrap';\nimport { FaPlus, FaEdit, FaTrash, FaEye, FaBuilding, FaUsers, FaProjectDiagram } from 'react-icons/fa';\nimport axios from 'axios';\n\nconst Clients = () => {\n  const [clients, setClients] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [showModal, setShowModal] = useState(false);\n  const [modalMode, setModalMode] = useState('create'); // 'create', 'edit', 'view'\n  const [selectedClient, setSelectedClient] = useState(null);\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    contactEmail: '',\n    contactPhone: '',\n    address: ''\n  });\n\n  const currentUser = JSON.parse(sessionStorage.getItem('currentUser') || '{}');\n\n  useEffect(() => {\n    fetchClients();\n  }, []);\n\n  const fetchClients = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get('/api/clients', {\n        params: {\n          user_role: currentUser.role,\n          user_id: currentUser.id\n        }\n      });\n\n      if (response.data.success) {\n        setClients(response.data.clients);\n      } else {\n        setError(response.data.message || 'Failed to fetch clients');\n      }\n    } catch (err) {\n      setError('Error fetching clients: ' + (err.response?.data?.message || err.message));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleShowModal = (mode, client = null) => {\n    setModalMode(mode);\n    setSelectedClient(client);\n    \n    if (mode === 'create') {\n      setFormData({\n        name: '',\n        description: '',\n        contactEmail: '',\n        contactPhone: '',\n        address: ''\n      });\n    } else if (client) {\n      setFormData({\n        name: client.name || '',\n        description: client.description || '',\n        contactEmail: client.contactEmail || '',\n        contactPhone: client.contactPhone || '',\n        address: client.address || ''\n      });\n    }\n    \n    setShowModal(true);\n  };\n\n  const handleCloseModal = () => {\n    setShowModal(false);\n    setSelectedClient(null);\n    setError('');\n  };\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    try {\n      const payload = {\n        ...formData,\n        user_id: currentUser.id,\n        user_role: currentUser.role\n      };\n\n      let response;\n      if (modalMode === 'create') {\n        response = await axios.post('/api/clients', payload);\n      } else if (modalMode === 'edit') {\n        response = await axios.put(`/api/clients/${selectedClient._id}`, payload);\n      }\n\n      if (response.data.success) {\n        await fetchClients();\n        handleCloseModal();\n      } else {\n        setError(response.data.message || 'Operation failed');\n      }\n    } catch (err) {\n      setError('Error: ' + (err.response?.data?.message || err.message));\n    }\n  };\n\n  const handleDelete = async (clientId) => {\n    if (!window.confirm('Are you sure you want to delete this client? This action cannot be undone.')) {\n      return;\n    }\n\n    try {\n      const response = await axios.delete(`/api/clients/${clientId}`, {\n        params: {\n          user_id: currentUser.id,\n          user_role: currentUser.role\n        }\n      });\n\n      if (response.data.success) {\n        await fetchClients();\n      } else {\n        setError(response.data.message || 'Failed to delete client');\n      }\n    } catch (err) {\n      setError('Error deleting client: ' + (err.response?.data?.message || err.message));\n    }\n  };\n\n  if (loading) {\n    return (\n      <Container className=\"mt-4\">\n        <div className=\"text-center\">\n          <div className=\"spinner-border\" role=\"status\">\n            <span className=\"visually-hidden\">Loading...</span>\n          </div>\n        </div>\n      </Container>\n    );\n  }\n\n  return (\n    <Container fluid className=\"mt-4\">\n      <Row>\n        <Col>\n          <div className=\"d-flex justify-content-between align-items-center mb-4\">\n            <h2><FaBuilding className=\"me-2\" />Client Management</h2>\n            {currentUser.role === 'Admin' && (\n              <Button variant=\"primary\" onClick={() => handleShowModal('create')}>\n                <FaPlus className=\"me-2\" />Add New Client\n              </Button>\n            )}\n          </div>\n\n          {error && (\n            <Alert variant=\"danger\" dismissible onClose={() => setError('')}>\n              {error}\n            </Alert>\n          )}\n\n          <Card>\n            <Card.Body>\n              <Table responsive striped hover>\n                <thead>\n                  <tr>\n                    <th>Client Name</th>\n                    <th>Contact Email</th>\n                    <th>Contact Phone</th>\n                    <th>Supervisors</th>\n                    <th>Field Officers</th>\n                    <th>Projects</th>\n                    <th>Actions</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  {clients.length === 0 ? (\n                    <tr>\n                      <td colSpan=\"7\" className=\"text-center\">No clients found</td>\n                    </tr>\n                  ) : (\n                    clients.map((client) => (\n                      <tr key={client._id}>\n                        <td>\n                          <strong>{client.name}</strong>\n                          {client.description && (\n                            <div className=\"text-muted small\">{client.description}</div>\n                          )}\n                        </td>\n                        <td>{client.contactEmail}</td>\n                        <td>{client.contactPhone || 'N/A'}</td>\n                        <td>\n                          <Badge bg=\"info\">{client.supervisorCount || 0}</Badge>\n                        </td>\n                        <td>\n                          <Badge bg=\"success\">{client.fieldOfficerCount || 0}</Badge>\n                        </td>\n                        <td>\n                          <Badge bg=\"warning\">{client.projectCount || 0}</Badge>\n                        </td>\n                        <td>\n                          <div className=\"d-flex gap-2\">\n                            <Button\n                              variant=\"outline-info\"\n                              size=\"sm\"\n                              onClick={() => handleShowModal('view', client)}\n                            >\n                              <FaEye />\n                            </Button>\n                            {currentUser.role === 'Admin' && (\n                              <>\n                                <Button\n                                  variant=\"outline-warning\"\n                                  size=\"sm\"\n                                  onClick={() => handleShowModal('edit', client)}\n                                >\n                                  <FaEdit />\n                                </Button>\n                                <Button\n                                  variant=\"outline-danger\"\n                                  size=\"sm\"\n                                  onClick={() => handleDelete(client._id)}\n                                >\n                                  <FaTrash />\n                                </Button>\n                              </>\n                            )}\n                          </div>\n                        </td>\n                      </tr>\n                    ))\n                  )}\n                </tbody>\n              </Table>\n            </Card.Body>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* Client Modal */}\n      <Modal show={showModal} onHide={handleCloseModal} size=\"lg\">\n        <Modal.Header closeButton>\n          <Modal.Title>\n            {modalMode === 'create' && 'Add New Client'}\n            {modalMode === 'edit' && 'Edit Client'}\n            {modalMode === 'view' && 'Client Details'}\n          </Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          {error && (\n            <Alert variant=\"danger\" dismissible onClose={() => setError('')}>\n              {error}\n            </Alert>\n          )}\n          \n          <Form onSubmit={handleSubmit}>\n            <Row>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Client Name *</Form.Label>\n                  <Form.Control\n                    type=\"text\"\n                    name=\"name\"\n                    value={formData.name}\n                    onChange={handleInputChange}\n                    required\n                    disabled={modalMode === 'view'}\n                  />\n                </Form.Group>\n              </Col>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Contact Email *</Form.Label>\n                  <Form.Control\n                    type=\"email\"\n                    name=\"contactEmail\"\n                    value={formData.contactEmail}\n                    onChange={handleInputChange}\n                    required\n                    disabled={modalMode === 'view'}\n                  />\n                </Form.Group>\n              </Col>\n            </Row>\n            \n            <Row>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Contact Phone</Form.Label>\n                  <Form.Control\n                    type=\"tel\"\n                    name=\"contactPhone\"\n                    value={formData.contactPhone}\n                    onChange={handleInputChange}\n                    disabled={modalMode === 'view'}\n                  />\n                </Form.Group>\n              </Col>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Address</Form.Label>\n                  <Form.Control\n                    type=\"text\"\n                    name=\"address\"\n                    value={formData.address}\n                    onChange={handleInputChange}\n                    disabled={modalMode === 'view'}\n                  />\n                </Form.Group>\n              </Col>\n            </Row>\n            \n            <Form.Group className=\"mb-3\">\n              <Form.Label>Description</Form.Label>\n              <Form.Control\n                as=\"textarea\"\n                rows={3}\n                name=\"description\"\n                value={formData.description}\n                onChange={handleInputChange}\n                disabled={modalMode === 'view'}\n              />\n            </Form.Group>\n          </Form>\n        </Modal.Body>\n        <Modal.Footer>\n          <Button variant=\"secondary\" onClick={handleCloseModal}>\n            {modalMode === 'view' ? 'Close' : 'Cancel'}\n          </Button>\n          {modalMode !== 'view' && (\n            <Button variant=\"primary\" onClick={handleSubmit}>\n              {modalMode === 'create' ? 'Create Client' : 'Update Client'}\n            </Button>\n          )}\n        </Modal.Footer>\n      </Modal>\n    </Container>\n  );\n};\n\nexport default Clients;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,QAAQ,iBAAiB;AACrG,SAASC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,KAAK,EAAEC,UAAU,EAAEC,OAAO,EAAEC,gBAAgB,QAAQ,gBAAgB;AACtG,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1B,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC4B,OAAO,EAAEC,UAAU,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC8B,KAAK,EAAEC,QAAQ,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACgC,SAAS,EAAEC,YAAY,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACkC,SAAS,EAAEC,YAAY,CAAC,GAAGnC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;EACtD,MAAM,CAACoC,cAAc,EAAEC,iBAAiB,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACsC,QAAQ,EAAEC,WAAW,CAAC,GAAGvC,QAAQ,CAAC;IACvCwC,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,YAAY,EAAE,EAAE;IAChBC,YAAY,EAAE,EAAE;IAChBC,OAAO,EAAE;EACX,CAAC,CAAC;EAEF,MAAMC,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC;EAE7EhD,SAAS,CAAC,MAAM;IACdiD,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFrB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMsB,QAAQ,GAAG,MAAMhC,KAAK,CAACiC,GAAG,CAAC,cAAc,EAAE;QAC/CC,MAAM,EAAE;UACNC,SAAS,EAAET,WAAW,CAACU,IAAI;UAC3BC,OAAO,EAAEX,WAAW,CAACY;QACvB;MACF,CAAC,CAAC;MAEF,IAAIN,QAAQ,CAACO,IAAI,CAACC,OAAO,EAAE;QACzBhC,UAAU,CAACwB,QAAQ,CAACO,IAAI,CAAChC,OAAO,CAAC;MACnC,CAAC,MAAM;QACLK,QAAQ,CAACoB,QAAQ,CAACO,IAAI,CAACE,OAAO,IAAI,yBAAyB,CAAC;MAC9D;IACF,CAAC,CAAC,OAAOC,GAAG,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACZhC,QAAQ,CAAC,0BAA0B,IAAI,EAAA+B,aAAA,GAAAD,GAAG,CAACV,QAAQ,cAAAW,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcJ,IAAI,cAAAK,kBAAA,uBAAlBA,kBAAA,CAAoBH,OAAO,KAAIC,GAAG,CAACD,OAAO,CAAC,CAAC;IACrF,CAAC,SAAS;MACR/B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMmC,eAAe,GAAGA,CAACC,IAAI,EAAEC,MAAM,GAAG,IAAI,KAAK;IAC/C/B,YAAY,CAAC8B,IAAI,CAAC;IAClB5B,iBAAiB,CAAC6B,MAAM,CAAC;IAEzB,IAAID,IAAI,KAAK,QAAQ,EAAE;MACrB1B,WAAW,CAAC;QACVC,IAAI,EAAE,EAAE;QACRC,WAAW,EAAE,EAAE;QACfC,YAAY,EAAE,EAAE;QAChBC,YAAY,EAAE,EAAE;QAChBC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIsB,MAAM,EAAE;MACjB3B,WAAW,CAAC;QACVC,IAAI,EAAE0B,MAAM,CAAC1B,IAAI,IAAI,EAAE;QACvBC,WAAW,EAAEyB,MAAM,CAACzB,WAAW,IAAI,EAAE;QACrCC,YAAY,EAAEwB,MAAM,CAACxB,YAAY,IAAI,EAAE;QACvCC,YAAY,EAAEuB,MAAM,CAACvB,YAAY,IAAI,EAAE;QACvCC,OAAO,EAAEsB,MAAM,CAACtB,OAAO,IAAI;MAC7B,CAAC,CAAC;IACJ;IAEAX,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMkC,gBAAgB,GAAGA,CAAA,KAAM;IAC7BlC,YAAY,CAAC,KAAK,CAAC;IACnBI,iBAAiB,CAAC,IAAI,CAAC;IACvBN,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;EAED,MAAMqC,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAE7B,IAAI;MAAE8B;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChChC,WAAW,CAACiC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAAChC,IAAI,GAAG8B;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMG,YAAY,GAAG,MAAOJ,CAAC,IAAK;IAChCA,CAAC,CAACK,cAAc,CAAC,CAAC;IAElB,IAAI;MACF,MAAMC,OAAO,GAAG;QACd,GAAGrC,QAAQ;QACXkB,OAAO,EAAEX,WAAW,CAACY,EAAE;QACvBH,SAAS,EAAET,WAAW,CAACU;MACzB,CAAC;MAED,IAAIJ,QAAQ;MACZ,IAAIjB,SAAS,KAAK,QAAQ,EAAE;QAC1BiB,QAAQ,GAAG,MAAMhC,KAAK,CAACyD,IAAI,CAAC,cAAc,EAAED,OAAO,CAAC;MACtD,CAAC,MAAM,IAAIzC,SAAS,KAAK,MAAM,EAAE;QAC/BiB,QAAQ,GAAG,MAAMhC,KAAK,CAAC0D,GAAG,CAAC,gBAAgBzC,cAAc,CAAC0C,GAAG,EAAE,EAAEH,OAAO,CAAC;MAC3E;MAEA,IAAIxB,QAAQ,CAACO,IAAI,CAACC,OAAO,EAAE;QACzB,MAAMT,YAAY,CAAC,CAAC;QACpBiB,gBAAgB,CAAC,CAAC;MACpB,CAAC,MAAM;QACLpC,QAAQ,CAACoB,QAAQ,CAACO,IAAI,CAACE,OAAO,IAAI,kBAAkB,CAAC;MACvD;IACF,CAAC,CAAC,OAAOC,GAAG,EAAE;MAAA,IAAAkB,cAAA,EAAAC,mBAAA;MACZjD,QAAQ,CAAC,SAAS,IAAI,EAAAgD,cAAA,GAAAlB,GAAG,CAACV,QAAQ,cAAA4B,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcrB,IAAI,cAAAsB,mBAAA,uBAAlBA,mBAAA,CAAoBpB,OAAO,KAAIC,GAAG,CAACD,OAAO,CAAC,CAAC;IACpE;EACF,CAAC;EAED,MAAMqB,YAAY,GAAG,MAAOC,QAAQ,IAAK;IACvC,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,4EAA4E,CAAC,EAAE;MACjG;IACF;IAEA,IAAI;MACF,MAAMjC,QAAQ,GAAG,MAAMhC,KAAK,CAACkE,MAAM,CAAC,gBAAgBH,QAAQ,EAAE,EAAE;QAC9D7B,MAAM,EAAE;UACNG,OAAO,EAAEX,WAAW,CAACY,EAAE;UACvBH,SAAS,EAAET,WAAW,CAACU;QACzB;MACF,CAAC,CAAC;MAEF,IAAIJ,QAAQ,CAACO,IAAI,CAACC,OAAO,EAAE;QACzB,MAAMT,YAAY,CAAC,CAAC;MACtB,CAAC,MAAM;QACLnB,QAAQ,CAACoB,QAAQ,CAACO,IAAI,CAACE,OAAO,IAAI,yBAAyB,CAAC;MAC9D;IACF,CAAC,CAAC,OAAOC,GAAG,EAAE;MAAA,IAAAyB,cAAA,EAAAC,mBAAA;MACZxD,QAAQ,CAAC,yBAAyB,IAAI,EAAAuD,cAAA,GAAAzB,GAAG,CAACV,QAAQ,cAAAmC,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAc5B,IAAI,cAAA6B,mBAAA,uBAAlBA,mBAAA,CAAoB3B,OAAO,KAAIC,GAAG,CAACD,OAAO,CAAC,CAAC;IACpF;EACF,CAAC;EAED,IAAIhC,OAAO,EAAE;IACX,oBACEP,OAAA,CAACnB,SAAS;MAACsF,SAAS,EAAC,MAAM;MAAAC,QAAA,eACzBpE,OAAA;QAAKmE,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1BpE,OAAA;UAAKmE,SAAS,EAAC,gBAAgB;UAACjC,IAAI,EAAC,QAAQ;UAAAkC,QAAA,eAC3CpE,OAAA;YAAMmE,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAEhB;EAEA,oBACExE,OAAA,CAACnB,SAAS;IAAC4F,KAAK;IAACN,SAAS,EAAC,MAAM;IAAAC,QAAA,gBAC/BpE,OAAA,CAAClB,GAAG;MAAAsF,QAAA,eACFpE,OAAA,CAACjB,GAAG;QAAAqF,QAAA,gBACFpE,OAAA;UAAKmE,SAAS,EAAC,wDAAwD;UAAAC,QAAA,gBACrEpE,OAAA;YAAAoE,QAAA,gBAAIpE,OAAA,CAACL,UAAU;cAACwE,SAAS,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,qBAAiB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACxDhD,WAAW,CAACU,IAAI,KAAK,OAAO,iBAC3BlC,OAAA,CAACf,MAAM;YAACyF,OAAO,EAAC,SAAS;YAACC,OAAO,EAAEA,CAAA,KAAMhC,eAAe,CAAC,QAAQ,CAAE;YAAAyB,QAAA,gBACjEpE,OAAA,CAACT,MAAM;cAAC4E,SAAS,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,kBAC7B;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAEL/D,KAAK,iBACJT,OAAA,CAACX,KAAK;UAACqF,OAAO,EAAC,QAAQ;UAACE,WAAW;UAACC,OAAO,EAAEA,CAAA,KAAMnE,QAAQ,CAAC,EAAE,CAAE;UAAA0D,QAAA,EAC7D3D;QAAK;UAAA4D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR,eAEDxE,OAAA,CAAChB,IAAI;UAAAoF,QAAA,eACHpE,OAAA,CAAChB,IAAI,CAAC8F,IAAI;YAAAV,QAAA,eACRpE,OAAA,CAACd,KAAK;cAAC6F,UAAU;cAACC,OAAO;cAACC,KAAK;cAAAb,QAAA,gBAC7BpE,OAAA;gBAAAoE,QAAA,eACEpE,OAAA;kBAAAoE,QAAA,gBACEpE,OAAA;oBAAAoE,QAAA,EAAI;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpBxE,OAAA;oBAAAoE,QAAA,EAAI;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtBxE,OAAA;oBAAAoE,QAAA,EAAI;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtBxE,OAAA;oBAAAoE,QAAA,EAAI;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpBxE,OAAA;oBAAAoE,QAAA,EAAI;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvBxE,OAAA;oBAAAoE,QAAA,EAAI;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjBxE,OAAA;oBAAAoE,QAAA,EAAI;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACRxE,OAAA;gBAAAoE,QAAA,EACG/D,OAAO,CAAC6E,MAAM,KAAK,CAAC,gBACnBlF,OAAA;kBAAAoE,QAAA,eACEpE,OAAA;oBAAImF,OAAO,EAAC,GAAG;oBAAChB,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D,CAAC,GAELnE,OAAO,CAAC+E,GAAG,CAAEvC,MAAM,iBACjB7C,OAAA;kBAAAoE,QAAA,gBACEpE,OAAA;oBAAAoE,QAAA,gBACEpE,OAAA;sBAAAoE,QAAA,EAASvB,MAAM,CAAC1B;oBAAI;sBAAAkD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS,CAAC,EAC7B3B,MAAM,CAACzB,WAAW,iBACjBpB,OAAA;sBAAKmE,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAEvB,MAAM,CAACzB;oBAAW;sBAAAiD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAC5D;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACLxE,OAAA;oBAAAoE,QAAA,EAAKvB,MAAM,CAACxB;kBAAY;oBAAAgD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC9BxE,OAAA;oBAAAoE,QAAA,EAAKvB,MAAM,CAACvB,YAAY,IAAI;kBAAK;oBAAA+C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACvCxE,OAAA;oBAAAoE,QAAA,eACEpE,OAAA,CAACV,KAAK;sBAAC+F,EAAE,EAAC,MAAM;sBAAAjB,QAAA,EAAEvB,MAAM,CAACyC,eAAe,IAAI;oBAAC;sBAAAjB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CAAC,eACLxE,OAAA;oBAAAoE,QAAA,eACEpE,OAAA,CAACV,KAAK;sBAAC+F,EAAE,EAAC,SAAS;sBAAAjB,QAAA,EAAEvB,MAAM,CAAC0C,iBAAiB,IAAI;oBAAC;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD,CAAC,eACLxE,OAAA;oBAAAoE,QAAA,eACEpE,OAAA,CAACV,KAAK;sBAAC+F,EAAE,EAAC,SAAS;sBAAAjB,QAAA,EAAEvB,MAAM,CAAC2C,YAAY,IAAI;oBAAC;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CAAC,eACLxE,OAAA;oBAAAoE,QAAA,eACEpE,OAAA;sBAAKmE,SAAS,EAAC,cAAc;sBAAAC,QAAA,gBAC3BpE,OAAA,CAACf,MAAM;wBACLyF,OAAO,EAAC,cAAc;wBACtBe,IAAI,EAAC,IAAI;wBACTd,OAAO,EAAEA,CAAA,KAAMhC,eAAe,CAAC,MAAM,EAAEE,MAAM,CAAE;wBAAAuB,QAAA,eAE/CpE,OAAA,CAACN,KAAK;0BAAA2E,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,EACRhD,WAAW,CAACU,IAAI,KAAK,OAAO,iBAC3BlC,OAAA,CAAAE,SAAA;wBAAAkE,QAAA,gBACEpE,OAAA,CAACf,MAAM;0BACLyF,OAAO,EAAC,iBAAiB;0BACzBe,IAAI,EAAC,IAAI;0BACTd,OAAO,EAAEA,CAAA,KAAMhC,eAAe,CAAC,MAAM,EAAEE,MAAM,CAAE;0BAAAuB,QAAA,eAE/CpE,OAAA,CAACR,MAAM;4BAAA6E,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC,eACTxE,OAAA,CAACf,MAAM;0BACLyF,OAAO,EAAC,gBAAgB;0BACxBe,IAAI,EAAC,IAAI;0BACTd,OAAO,EAAEA,CAAA,KAAMf,YAAY,CAACf,MAAM,CAACY,GAAG,CAAE;0BAAAW,QAAA,eAExCpE,OAAA,CAACP,OAAO;4BAAA4E,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC;sBAAA,eACT,CACH;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA,GA9CE3B,MAAM,CAACY,GAAG;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA+Cf,CACL;cACF;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNxE,OAAA,CAACb,KAAK;MAACuG,IAAI,EAAE/E,SAAU;MAACgF,MAAM,EAAE7C,gBAAiB;MAAC2C,IAAI,EAAC,IAAI;MAAArB,QAAA,gBACzDpE,OAAA,CAACb,KAAK,CAACyG,MAAM;QAACC,WAAW;QAAAzB,QAAA,eACvBpE,OAAA,CAACb,KAAK,CAAC2G,KAAK;UAAA1B,QAAA,GACTvD,SAAS,KAAK,QAAQ,IAAI,gBAAgB,EAC1CA,SAAS,KAAK,MAAM,IAAI,aAAa,EACrCA,SAAS,KAAK,MAAM,IAAI,gBAAgB;QAAA;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACfxE,OAAA,CAACb,KAAK,CAAC2F,IAAI;QAAAV,QAAA,GACR3D,KAAK,iBACJT,OAAA,CAACX,KAAK;UAACqF,OAAO,EAAC,QAAQ;UAACE,WAAW;UAACC,OAAO,EAAEA,CAAA,KAAMnE,QAAQ,CAAC,EAAE,CAAE;UAAA0D,QAAA,EAC7D3D;QAAK;UAAA4D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR,eAEDxE,OAAA,CAACZ,IAAI;UAAC2G,QAAQ,EAAE3C,YAAa;UAAAgB,QAAA,gBAC3BpE,OAAA,CAAClB,GAAG;YAAAsF,QAAA,gBACFpE,OAAA,CAACjB,GAAG;cAACiH,EAAE,EAAE,CAAE;cAAA5B,QAAA,eACTpE,OAAA,CAACZ,IAAI,CAAC6G,KAAK;gBAAC9B,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBAC1BpE,OAAA,CAACZ,IAAI,CAAC8G,KAAK;kBAAA9B,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACtCxE,OAAA,CAACZ,IAAI,CAAC+G,OAAO;kBACXC,IAAI,EAAC,MAAM;kBACXjF,IAAI,EAAC,MAAM;kBACX8B,KAAK,EAAEhC,QAAQ,CAACE,IAAK;kBACrBkF,QAAQ,EAAEtD,iBAAkB;kBAC5BuD,QAAQ;kBACRC,QAAQ,EAAE1F,SAAS,KAAK;gBAAO;kBAAAwD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNxE,OAAA,CAACjB,GAAG;cAACiH,EAAE,EAAE,CAAE;cAAA5B,QAAA,eACTpE,OAAA,CAACZ,IAAI,CAAC6G,KAAK;gBAAC9B,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBAC1BpE,OAAA,CAACZ,IAAI,CAAC8G,KAAK;kBAAA9B,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACxCxE,OAAA,CAACZ,IAAI,CAAC+G,OAAO;kBACXC,IAAI,EAAC,OAAO;kBACZjF,IAAI,EAAC,cAAc;kBACnB8B,KAAK,EAAEhC,QAAQ,CAACI,YAAa;kBAC7BgF,QAAQ,EAAEtD,iBAAkB;kBAC5BuD,QAAQ;kBACRC,QAAQ,EAAE1F,SAAS,KAAK;gBAAO;kBAAAwD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENxE,OAAA,CAAClB,GAAG;YAAAsF,QAAA,gBACFpE,OAAA,CAACjB,GAAG;cAACiH,EAAE,EAAE,CAAE;cAAA5B,QAAA,eACTpE,OAAA,CAACZ,IAAI,CAAC6G,KAAK;gBAAC9B,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBAC1BpE,OAAA,CAACZ,IAAI,CAAC8G,KAAK;kBAAA9B,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACtCxE,OAAA,CAACZ,IAAI,CAAC+G,OAAO;kBACXC,IAAI,EAAC,KAAK;kBACVjF,IAAI,EAAC,cAAc;kBACnB8B,KAAK,EAAEhC,QAAQ,CAACK,YAAa;kBAC7B+E,QAAQ,EAAEtD,iBAAkB;kBAC5BwD,QAAQ,EAAE1F,SAAS,KAAK;gBAAO;kBAAAwD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNxE,OAAA,CAACjB,GAAG;cAACiH,EAAE,EAAE,CAAE;cAAA5B,QAAA,eACTpE,OAAA,CAACZ,IAAI,CAAC6G,KAAK;gBAAC9B,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBAC1BpE,OAAA,CAACZ,IAAI,CAAC8G,KAAK;kBAAA9B,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAChCxE,OAAA,CAACZ,IAAI,CAAC+G,OAAO;kBACXC,IAAI,EAAC,MAAM;kBACXjF,IAAI,EAAC,SAAS;kBACd8B,KAAK,EAAEhC,QAAQ,CAACM,OAAQ;kBACxB8E,QAAQ,EAAEtD,iBAAkB;kBAC5BwD,QAAQ,EAAE1F,SAAS,KAAK;gBAAO;kBAAAwD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENxE,OAAA,CAACZ,IAAI,CAAC6G,KAAK;YAAC9B,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAC1BpE,OAAA,CAACZ,IAAI,CAAC8G,KAAK;cAAA9B,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACpCxE,OAAA,CAACZ,IAAI,CAAC+G,OAAO;cACXK,EAAE,EAAC,UAAU;cACbC,IAAI,EAAE,CAAE;cACRtF,IAAI,EAAC,aAAa;cAClB8B,KAAK,EAAEhC,QAAQ,CAACG,WAAY;cAC5BiF,QAAQ,EAAEtD,iBAAkB;cAC5BwD,QAAQ,EAAE1F,SAAS,KAAK;YAAO;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACbxE,OAAA,CAACb,KAAK,CAACuH,MAAM;QAAAtC,QAAA,gBACXpE,OAAA,CAACf,MAAM;UAACyF,OAAO,EAAC,WAAW;UAACC,OAAO,EAAE7B,gBAAiB;UAAAsB,QAAA,EACnDvD,SAAS,KAAK,MAAM,GAAG,OAAO,GAAG;QAAQ;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,EACR3D,SAAS,KAAK,MAAM,iBACnBb,OAAA,CAACf,MAAM;UAACyF,OAAO,EAAC,SAAS;UAACC,OAAO,EAAEvB,YAAa;UAAAgB,QAAA,EAC7CvD,SAAS,KAAK,QAAQ,GAAG,eAAe,GAAG;QAAe;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEhB,CAAC;AAACpE,EAAA,CArVID,OAAO;AAAAwG,EAAA,GAAPxG,OAAO;AAuVb,eAAeA,OAAO;AAAC,IAAAwG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}