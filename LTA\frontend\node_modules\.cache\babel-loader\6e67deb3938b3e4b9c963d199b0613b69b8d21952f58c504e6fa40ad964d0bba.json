{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Deep Learning\\\\LTAGIT\\\\LTA\\\\frontend\\\\src\\\\pages\\\\UserManagement\\\\RouteManagement.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Table, Button, Modal, Form, Alert, Badge, Spinner, Dropdown } from 'react-bootstrap';\nimport { FaRoute, FaPlus, FaEdit, FaTrash, FaEye, FaUserPlus, FaProjectDiagram, FaShare } from 'react-icons/fa';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst RouteManagement = ({\n  user\n}) => {\n  _s();\n  const [routes, setRoutes] = useState([]);\n  const [projects, setProjects] = useState([]);\n  const [fieldOfficers, setFieldOfficers] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [showModal, setShowModal] = useState(false);\n  const [showAssignModal, setShowAssignModal] = useState(false);\n  const [modalMode, setModalMode] = useState('create');\n  const [selectedRoute, setSelectedRoute] = useState(null);\n  const [selectedProject, setSelectedProject] = useState('');\n  const [formData, setFormData] = useState({\n    routeName: '',\n    description: '',\n    projectId: '',\n    routeType: 'Road Safety',\n    priority: 'Medium',\n    isShared: false\n  });\n  const [assignmentData, setAssignmentData] = useState({\n    fieldOfficerIds: [],\n    startDate: '',\n    endDate: '',\n    priority: 'Medium',\n    notes: ''\n  });\n  const [alert, setAlert] = useState({\n    show: false,\n    message: '',\n    type: 'success'\n  });\n  useEffect(() => {\n    fetchData();\n  }, []);\n  const fetchData = async () => {\n    try {\n      setLoading(true);\n      await Promise.all([fetchRoutes(), fetchProjects(), fetchFieldOfficers()]);\n    } catch (error) {\n      console.error('Error fetching data:', error);\n      showAlert('Error fetching data', 'danger');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchRoutes = async () => {\n    try {\n      const response = await axios.get('/api/route-management/routes', {\n        params: {\n          user_role: user.role,\n          user_id: user.userId || user.username,\n          client_id: user.clientId,\n          project_id: selectedProject\n        }\n      });\n      if (response.data.success) {\n        setRoutes(response.data.routes);\n      }\n    } catch (error) {\n      console.error('Error fetching routes:', error);\n    }\n  };\n  const fetchProjects = async () => {\n    try {\n      const response = await axios.get('/api/route-management/projects', {\n        params: {\n          user_role: user.role,\n          user_id: user.userId || user.username,\n          client_id: user.clientId\n        }\n      });\n      if (response.data.success) {\n        setProjects(response.data.projects);\n      }\n    } catch (error) {\n      console.error('Error fetching projects:', error);\n    }\n  };\n  const fetchFieldOfficers = async () => {\n    try {\n      const response = await axios.get('/api/route-management/field-officers/available', {\n        params: {\n          user_role: user.role,\n          client_id: user.clientId,\n          supervisor_id: user.role === 'Supervisor' ? user.userId : undefined\n        }\n      });\n      if (response.data.success) {\n        setFieldOfficers(response.data.fieldOfficers);\n      }\n    } catch (error) {\n      console.error('Error fetching field officers:', error);\n    }\n  };\n  const showAlert = (message, type = 'success') => {\n    setAlert({\n      show: true,\n      message,\n      type\n    });\n    setTimeout(() => setAlert({\n      show: false,\n      message: '',\n      type: 'success'\n    }), 5000);\n  };\n  const handleShowModal = (mode, route = null) => {\n    setModalMode(mode);\n    setSelectedRoute(route);\n    if (mode === 'create') {\n      setFormData({\n        routeName: '',\n        description: '',\n        projectId: '',\n        routeType: 'Road Safety',\n        priority: 'Medium',\n        isShared: false\n      });\n    } else if (route) {\n      setFormData({\n        routeName: route.routeName || '',\n        description: route.description || '',\n        projectId: route.projectId || '',\n        routeType: route.routeType || 'Road Safety',\n        priority: route.priority || 'Medium',\n        isShared: route.isShared || false\n      });\n    }\n    setShowModal(true);\n  };\n  const handleShowAssignModal = route => {\n    setSelectedRoute(route);\n    setAssignmentData({\n      fieldOfficerIds: [],\n      startDate: new Date().toISOString().split('T')[0],\n      endDate: '',\n      priority: 'Medium',\n      notes: ''\n    });\n    setShowAssignModal(true);\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value,\n      type,\n      checked\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: type === 'checkbox' ? checked : value\n    });\n  };\n  const handleAssignmentChange = e => {\n    const {\n      name,\n      value,\n      type,\n      checked\n    } = e.target;\n    if (name === 'fieldOfficerIds') {\n      const selectedIds = [...assignmentData.fieldOfficerIds];\n      if (checked) {\n        selectedIds.push(value);\n      } else {\n        const index = selectedIds.indexOf(value);\n        if (index > -1) selectedIds.splice(index, 1);\n      }\n      setAssignmentData({\n        ...assignmentData,\n        fieldOfficerIds: selectedIds\n      });\n    } else {\n      setAssignmentData({\n        ...assignmentData,\n        [name]: value\n      });\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    try {\n      let response;\n      const submitData = {\n        ...formData,\n        clientId: user.clientId || 'client_001',\n        createdBy: user.userId || user.username\n      };\n      if (modalMode === 'create') {\n        response = await axios.post('/api/route-management/routes', submitData);\n      } else if (modalMode === 'edit') {\n        response = await axios.put(`/api/route-management/routes/${selectedRoute.routeId}`, submitData);\n      }\n      if (response.data.success) {\n        showAlert(modalMode === 'create' ? 'Route created successfully' : 'Route updated successfully', 'success');\n        fetchRoutes();\n        setShowModal(false);\n      } else {\n        showAlert(response.data.message || 'Operation failed', 'danger');\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Error saving route:', error);\n      showAlert(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Error saving route', 'danger');\n    }\n  };\n  const handleAssignRoute = async e => {\n    e.preventDefault();\n    if (assignmentData.fieldOfficerIds.length === 0) {\n      showAlert('Please select at least one field officer', 'warning');\n      return;\n    }\n    try {\n      const response = await axios.post(`/api/route-management/routes/${selectedRoute.routeId}/assign`, {\n        ...assignmentData,\n        assignedBy: user.userId || user.username,\n        supervisorId: user.role === 'Supervisor' ? user.userId : undefined\n      });\n      if (response.data.success) {\n        showAlert('Route assigned successfully', 'success');\n        fetchRoutes();\n        setShowAssignModal(false);\n      } else {\n        showAlert(response.data.message || 'Assignment failed', 'danger');\n      }\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      console.error('Error assigning route:', error);\n      showAlert(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'Error assigning route', 'danger');\n    }\n  };\n  const getPriorityBadge = priority => {\n    const colors = {\n      'High': 'danger',\n      'Medium': 'warning',\n      'Low': 'success'\n    };\n    return /*#__PURE__*/_jsxDEV(Badge, {\n      bg: colors[priority] || 'secondary',\n      children: priority\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 247,\n      columnNumber: 12\n    }, this);\n  };\n  const getTypeBadge = type => {\n    const colors = {\n      'Road Safety': 'primary',\n      'Blind Spot': 'info',\n      'Mixed': 'secondary'\n    };\n    return /*#__PURE__*/_jsxDEV(Badge, {\n      bg: colors[type] || 'secondary',\n      children: type\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 256,\n      columnNumber: 12\n    }, this);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Container, {\n      className: \"d-flex justify-content-center align-items-center\",\n      style: {\n        minHeight: '400px'\n      },\n      children: /*#__PURE__*/_jsxDEV(Spinner, {\n        animation: \"border\",\n        role: \"status\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"visually-hidden\",\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Container, {\n    fluid: true,\n    className: \"p-4\",\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-4\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-between align-items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: [/*#__PURE__*/_jsxDEV(FaRoute, {\n                className: \"me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 19\n              }, this), \"Route Management\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted\",\n              children: \"Manage routes and assignments\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Select, {\n              value: selectedProject,\n              onChange: e => {\n                setSelectedProject(e.target.value);\n                fetchRoutes();\n              },\n              style: {\n                width: '200px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"All Projects\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 17\n              }, this), projects.map(project => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: project.projectId,\n                children: project.projectName\n              }, project.projectId, false, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 15\n            }, this), ['Admin', 'Client'].includes(user.role) && /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"primary\",\n              onClick: () => handleShowModal('create'),\n              children: [/*#__PURE__*/_jsxDEV(FaPlus, {\n                className: \"me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 19\n              }, this), \"Add Route\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 271,\n      columnNumber: 7\n    }, this), alert.show && /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-3\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(Alert, {\n          variant: alert.type,\n          dismissible: true,\n          onClose: () => setAlert({\n            show: false,\n            message: '',\n            type: 'success'\n          }),\n          children: alert.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 306,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 305,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: [\"Routes (\", routes.length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: routes.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center py-4\",\n              children: [/*#__PURE__*/_jsxDEV(FaRoute, {\n                size: 48,\n                className: \"text-muted mb-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-muted\",\n                children: \"No routes found\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Table, {\n              responsive: true,\n              hover: true,\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Route Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 330,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Project\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 331,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Type\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 332,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Priority\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 333,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Assignments\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 334,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 335,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Actions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 336,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: routes.map(route => /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex align-items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: route.routeName\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 344,\n                        columnNumber: 29\n                      }, this), route.isShared && /*#__PURE__*/_jsxDEV(FaShare, {\n                        className: \"ms-2 text-info\",\n                        title: \"Shared Route\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 346,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 343,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                      className: \"text-muted\",\n                      children: route.routeId\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 349,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 342,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(Badge, {\n                      bg: \"secondary\",\n                      children: [/*#__PURE__*/_jsxDEV(FaProjectDiagram, {\n                        className: \"me-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 353,\n                        columnNumber: 29\n                      }, this), route.projectName]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 352,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 351,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: getTypeBadge(route.routeType)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 357,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: getPriorityBadge(route.priority)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 358,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(Badge, {\n                      bg: \"info\",\n                      children: [route.assignedCount || 0, \" assigned\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 360,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 359,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(Badge, {\n                      bg: route.isActive ? 'success' : 'secondary',\n                      children: route.isActive ? 'Active' : 'Inactive'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 365,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 364,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"btn-group\",\n                      role: \"group\",\n                      children: [/*#__PURE__*/_jsxDEV(Button, {\n                        variant: \"outline-info\",\n                        size: \"sm\",\n                        onClick: () => handleShowModal('view', route),\n                        title: \"View Details\",\n                        children: /*#__PURE__*/_jsxDEV(FaEye, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 377,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 371,\n                        columnNumber: 29\n                      }, this), ['Admin', 'Client', 'Supervisor'].includes(user.role) && /*#__PURE__*/_jsxDEV(Button, {\n                        variant: \"outline-success\",\n                        size: \"sm\",\n                        onClick: () => handleShowAssignModal(route),\n                        title: \"Assign to Field Officers\",\n                        children: /*#__PURE__*/_jsxDEV(FaUserPlus, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 386,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 380,\n                        columnNumber: 31\n                      }, this), ['Admin', 'Client'].includes(user.role) && /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(Button, {\n                          variant: \"outline-warning\",\n                          size: \"sm\",\n                          onClick: () => handleShowModal('edit', route),\n                          title: \"Edit Route\",\n                          children: /*#__PURE__*/_jsxDEV(FaEdit, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 397,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 391,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(Button, {\n                          variant: \"outline-danger\",\n                          size: \"sm\",\n                          onClick: () => {/* handleDelete(route.routeId) */},\n                          title: \"Delete Route\",\n                          children: /*#__PURE__*/_jsxDEV(FaTrash, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 405,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 399,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 370,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 369,\n                    columnNumber: 25\n                  }, this)]\n                }, route.routeId, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 315,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 314,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showModal,\n      onHide: () => setShowModal(false),\n      size: \"lg\",\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: [modalMode === 'create' && 'Add New Route', modalMode === 'edit' && 'Edit Route', modalMode === 'view' && 'Route Details']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 424,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 423,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: /*#__PURE__*/_jsxDEV(Form, {\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Route Name *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 435,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"text\",\n                  name: \"routeName\",\n                  value: formData.routeName,\n                  onChange: handleInputChange,\n                  required: true,\n                  disabled: modalMode === 'view'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 436,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 433,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Project *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 448,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  name: \"projectId\",\n                  value: formData.projectId,\n                  onChange: handleInputChange,\n                  required: true,\n                  disabled: modalMode === 'view',\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Select Project\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 456,\n                    columnNumber: 21\n                  }, this), projects.map(project => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: project.projectId,\n                    children: project.projectName\n                  }, project.projectId, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 458,\n                    columnNumber: 23\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 449,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 447,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 446,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 432,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 468,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              as: \"textarea\",\n              rows: 3,\n              name: \"description\",\n              value: formData.description,\n              onChange: handleInputChange,\n              disabled: modalMode === 'view'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 469,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 467,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 4,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Route Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 482,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  name: \"routeType\",\n                  value: formData.routeType,\n                  onChange: handleInputChange,\n                  disabled: modalMode === 'view',\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Road Safety\",\n                    children: \"Road Safety\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 489,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Blind Spot\",\n                    children: \"Blind Spot\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 490,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Mixed\",\n                    children: \"Mixed\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 491,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 483,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 481,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 480,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 4,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Priority\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 497,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  name: \"priority\",\n                  value: formData.priority,\n                  onChange: handleInputChange,\n                  disabled: modalMode === 'view',\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"High\",\n                    children: \"High\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 504,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Medium\",\n                    children: \"Medium\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 505,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Low\",\n                    children: \"Low\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 506,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 498,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 496,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 495,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 4,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: /*#__PURE__*/_jsxDEV(Form.Check, {\n                  type: \"checkbox\",\n                  name: \"isShared\",\n                  label: \"Shared Route\",\n                  checked: formData.isShared,\n                  onChange: handleInputChange,\n                  disabled: modalMode === 'view',\n                  className: \"mt-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 512,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 511,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 510,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 479,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 431,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 430,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => setShowModal(false),\n          children: modalMode === 'view' ? 'Close' : 'Cancel'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 527,\n          columnNumber: 11\n        }, this), modalMode !== 'view' && /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: handleSubmit,\n          children: modalMode === 'create' ? 'Create Route' : 'Update Route'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 531,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 526,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 422,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showAssignModal,\n      onHide: () => setShowAssignModal(false),\n      size: \"lg\",\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: [\"Assign Route: \", selectedRoute === null || selectedRoute === void 0 ? void 0 : selectedRoute.routeName]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 541,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 540,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: /*#__PURE__*/_jsxDEV(Form, {\n          onSubmit: handleAssignRoute,\n          children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Select Field Officers *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 546,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"border rounded p-3\",\n              style: {\n                maxHeight: '200px',\n                overflowY: 'auto'\n              },\n              children: fieldOfficers.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-muted\",\n                children: \"No field officers available\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 549,\n                columnNumber: 19\n              }, this) : fieldOfficers.map(fo => /*#__PURE__*/_jsxDEV(Form.Check, {\n                type: \"checkbox\",\n                id: `fo-${fo.userId}`,\n                label: /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: fo.fullName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 558,\n                    columnNumber: 27\n                  }, this), \" (\", fo.username, \")\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 559,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: [\"Active assignments: \", fo.activeAssignments]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 560,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 557,\n                  columnNumber: 25\n                }, this),\n                value: fo.userId,\n                checked: assignmentData.fieldOfficerIds.includes(fo.userId),\n                onChange: handleAssignmentChange,\n                name: \"fieldOfficerIds\",\n                className: \"mb-2\"\n              }, fo.userId, false, {\n                fileName: _jsxFileName,\n                lineNumber: 552,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 547,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 545,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Start Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 579,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"date\",\n                  name: \"startDate\",\n                  value: assignmentData.startDate,\n                  onChange: handleAssignmentChange\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 580,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 578,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 577,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"End Date (Optional)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 590,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"date\",\n                  name: \"endDate\",\n                  value: assignmentData.endDate,\n                  onChange: handleAssignmentChange\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 591,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 589,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 588,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 576,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Priority\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 602,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n              name: \"priority\",\n              value: assignmentData.priority,\n              onChange: handleAssignmentChange,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"High\",\n                children: \"High\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 608,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Medium\",\n                children: \"Medium\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 609,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Low\",\n                children: \"Low\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 610,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 603,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 601,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Notes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 615,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              as: \"textarea\",\n              rows: 3,\n              name: \"notes\",\n              value: assignmentData.notes,\n              onChange: handleAssignmentChange,\n              placeholder: \"Assignment instructions or notes...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 616,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 614,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 544,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 543,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => setShowAssignModal(false),\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 628,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: handleAssignRoute,\n          children: \"Assign Route\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 631,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 627,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 539,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 270,\n    columnNumber: 5\n  }, this);\n};\n_s(RouteManagement, \"NYUJK7FrnOMKK7lAakrEbthI5mw=\");\n_c = RouteManagement;\nexport default RouteManagement;\nvar _c;\n$RefreshReg$(_c, \"RouteManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "Table", "<PERSON><PERSON>", "Modal", "Form", "<PERSON><PERSON>", "Badge", "Spinner", "Dropdown", "FaRoute", "FaPlus", "FaEdit", "FaTrash", "FaEye", "FaUserPlus", "FaProjectDiagram", "FaShare", "axios", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "RouteManagement", "user", "_s", "routes", "setRoutes", "projects", "setProjects", "fieldOfficers", "setFieldOfficers", "loading", "setLoading", "showModal", "setShowModal", "showAssignModal", "setShowAssignModal", "modalMode", "setModalMode", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedRoute", "selectedProject", "setSelectedProject", "formData", "setFormData", "routeName", "description", "projectId", "routeType", "priority", "isShared", "assignmentData", "setAssignmentData", "fieldOfficerIds", "startDate", "endDate", "notes", "alert", "<PERSON><PERSON><PERSON><PERSON>", "show", "message", "type", "fetchData", "Promise", "all", "fetchRoutes", "fetchProjects", "fetchFieldOfficers", "error", "console", "show<PERSON><PERSON><PERSON>", "response", "get", "params", "user_role", "role", "user_id", "userId", "username", "client_id", "clientId", "project_id", "data", "success", "supervisor_id", "undefined", "setTimeout", "handleShowModal", "mode", "route", "handleShowAssignModal", "Date", "toISOString", "split", "handleInputChange", "e", "name", "value", "checked", "target", "handleAssignmentChange", "selectedIds", "push", "index", "indexOf", "splice", "handleSubmit", "preventDefault", "submitData", "created<PERSON>y", "post", "put", "routeId", "_error$response", "_error$response$data", "handleAssignRoute", "length", "assignedBy", "supervisorId", "_error$response2", "_error$response2$data", "getPriorityBadge", "colors", "bg", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getTypeBadge", "className", "style", "minHeight", "animation", "fluid", "Select", "onChange", "width", "map", "project", "projectName", "includes", "variant", "onClick", "dismissible", "onClose", "Header", "Body", "size", "responsive", "hover", "title", "assignedCount", "isActive", "onHide", "closeButton", "Title", "onSubmit", "md", "Group", "Label", "Control", "required", "disabled", "as", "rows", "Check", "label", "Footer", "maxHeight", "overflowY", "fo", "id", "fullName", "activeAssignments", "placeholder", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Deep Learning/LTAGIT/LTA/frontend/src/pages/UserManagement/RouteManagement.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Table, Button, Modal, Form, Alert, Badge, Spinner, Dropdown } from 'react-bootstrap';\nimport { FaRoute, FaPlus, FaEdit, FaTrash, FaEye, FaUserPlus, FaProjectDiagram, FaShare } from 'react-icons/fa';\nimport axios from 'axios';\n\nconst RouteManagement = ({ user }) => {\n  const [routes, setRoutes] = useState([]);\n  const [projects, setProjects] = useState([]);\n  const [fieldOfficers, setFieldOfficers] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [showModal, setShowModal] = useState(false);\n  const [showAssignModal, setShowAssignModal] = useState(false);\n  const [modalMode, setModalMode] = useState('create');\n  const [selectedRoute, setSelectedRoute] = useState(null);\n  const [selectedProject, setSelectedProject] = useState('');\n  const [formData, setFormData] = useState({\n    routeName: '',\n    description: '',\n    projectId: '',\n    routeType: 'Road Safety',\n    priority: 'Medium',\n    isShared: false\n  });\n  const [assignmentData, setAssignmentData] = useState({\n    fieldOfficerIds: [],\n    startDate: '',\n    endDate: '',\n    priority: 'Medium',\n    notes: ''\n  });\n  const [alert, setAlert] = useState({ show: false, message: '', type: 'success' });\n\n  useEffect(() => {\n    fetchData();\n  }, []);\n\n  const fetchData = async () => {\n    try {\n      setLoading(true);\n      await Promise.all([\n        fetchRoutes(),\n        fetchProjects(),\n        fetchFieldOfficers()\n      ]);\n    } catch (error) {\n      console.error('Error fetching data:', error);\n      showAlert('Error fetching data', 'danger');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchRoutes = async () => {\n    try {\n      const response = await axios.get('/api/route-management/routes', {\n        params: { \n          user_role: user.role,\n          user_id: user.userId || user.username,\n          client_id: user.clientId,\n          project_id: selectedProject\n        }\n      });\n      \n      if (response.data.success) {\n        setRoutes(response.data.routes);\n      }\n    } catch (error) {\n      console.error('Error fetching routes:', error);\n    }\n  };\n\n  const fetchProjects = async () => {\n    try {\n      const response = await axios.get('/api/route-management/projects', {\n        params: { \n          user_role: user.role,\n          user_id: user.userId || user.username,\n          client_id: user.clientId\n        }\n      });\n      \n      if (response.data.success) {\n        setProjects(response.data.projects);\n      }\n    } catch (error) {\n      console.error('Error fetching projects:', error);\n    }\n  };\n\n  const fetchFieldOfficers = async () => {\n    try {\n      const response = await axios.get('/api/route-management/field-officers/available', {\n        params: { \n          user_role: user.role,\n          client_id: user.clientId,\n          supervisor_id: user.role === 'Supervisor' ? user.userId : undefined\n        }\n      });\n      \n      if (response.data.success) {\n        setFieldOfficers(response.data.fieldOfficers);\n      }\n    } catch (error) {\n      console.error('Error fetching field officers:', error);\n    }\n  };\n\n  const showAlert = (message, type = 'success') => {\n    setAlert({ show: true, message, type });\n    setTimeout(() => setAlert({ show: false, message: '', type: 'success' }), 5000);\n  };\n\n  const handleShowModal = (mode, route = null) => {\n    setModalMode(mode);\n    setSelectedRoute(route);\n    \n    if (mode === 'create') {\n      setFormData({\n        routeName: '',\n        description: '',\n        projectId: '',\n        routeType: 'Road Safety',\n        priority: 'Medium',\n        isShared: false\n      });\n    } else if (route) {\n      setFormData({\n        routeName: route.routeName || '',\n        description: route.description || '',\n        projectId: route.projectId || '',\n        routeType: route.routeType || 'Road Safety',\n        priority: route.priority || 'Medium',\n        isShared: route.isShared || false\n      });\n    }\n    \n    setShowModal(true);\n  };\n\n  const handleShowAssignModal = (route) => {\n    setSelectedRoute(route);\n    setAssignmentData({\n      fieldOfficerIds: [],\n      startDate: new Date().toISOString().split('T')[0],\n      endDate: '',\n      priority: 'Medium',\n      notes: ''\n    });\n    setShowAssignModal(true);\n  };\n\n  const handleInputChange = (e) => {\n    const { name, value, type, checked } = e.target;\n    setFormData({\n      ...formData,\n      [name]: type === 'checkbox' ? checked : value\n    });\n  };\n\n  const handleAssignmentChange = (e) => {\n    const { name, value, type, checked } = e.target;\n    \n    if (name === 'fieldOfficerIds') {\n      const selectedIds = [...assignmentData.fieldOfficerIds];\n      if (checked) {\n        selectedIds.push(value);\n      } else {\n        const index = selectedIds.indexOf(value);\n        if (index > -1) selectedIds.splice(index, 1);\n      }\n      setAssignmentData({ ...assignmentData, fieldOfficerIds: selectedIds });\n    } else {\n      setAssignmentData({\n        ...assignmentData,\n        [name]: value\n      });\n    }\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    try {\n      let response;\n      const submitData = {\n        ...formData,\n        clientId: user.clientId || 'client_001',\n        createdBy: user.userId || user.username\n      };\n      \n      if (modalMode === 'create') {\n        response = await axios.post('/api/route-management/routes', submitData);\n      } else if (modalMode === 'edit') {\n        response = await axios.put(`/api/route-management/routes/${selectedRoute.routeId}`, submitData);\n      }\n      \n      if (response.data.success) {\n        showAlert(\n          modalMode === 'create' ? 'Route created successfully' : 'Route updated successfully',\n          'success'\n        );\n        fetchRoutes();\n        setShowModal(false);\n      } else {\n        showAlert(response.data.message || 'Operation failed', 'danger');\n      }\n    } catch (error) {\n      console.error('Error saving route:', error);\n      showAlert(error.response?.data?.message || 'Error saving route', 'danger');\n    }\n  };\n\n  const handleAssignRoute = async (e) => {\n    e.preventDefault();\n    \n    if (assignmentData.fieldOfficerIds.length === 0) {\n      showAlert('Please select at least one field officer', 'warning');\n      return;\n    }\n    \n    try {\n      const response = await axios.post(`/api/route-management/routes/${selectedRoute.routeId}/assign`, {\n        ...assignmentData,\n        assignedBy: user.userId || user.username,\n        supervisorId: user.role === 'Supervisor' ? user.userId : undefined\n      });\n      \n      if (response.data.success) {\n        showAlert('Route assigned successfully', 'success');\n        fetchRoutes();\n        setShowAssignModal(false);\n      } else {\n        showAlert(response.data.message || 'Assignment failed', 'danger');\n      }\n    } catch (error) {\n      console.error('Error assigning route:', error);\n      showAlert(error.response?.data?.message || 'Error assigning route', 'danger');\n    }\n  };\n\n  const getPriorityBadge = (priority) => {\n    const colors = {\n      'High': 'danger',\n      'Medium': 'warning',\n      'Low': 'success'\n    };\n    return <Badge bg={colors[priority] || 'secondary'}>{priority}</Badge>;\n  };\n\n  const getTypeBadge = (type) => {\n    const colors = {\n      'Road Safety': 'primary',\n      'Blind Spot': 'info',\n      'Mixed': 'secondary'\n    };\n    return <Badge bg={colors[type] || 'secondary'}>{type}</Badge>;\n  };\n\n  if (loading) {\n    return (\n      <Container className=\"d-flex justify-content-center align-items-center\" style={{ minHeight: '400px' }}>\n        <Spinner animation=\"border\" role=\"status\">\n          <span className=\"visually-hidden\">Loading...</span>\n        </Spinner>\n      </Container>\n    );\n  }\n\n  return (\n    <Container fluid className=\"p-4\">\n      <Row className=\"mb-4\">\n        <Col>\n          <div className=\"d-flex justify-content-between align-items-center\">\n            <div>\n              <h2><FaRoute className=\"me-2\" />Route Management</h2>\n              <p className=\"text-muted\">Manage routes and assignments</p>\n            </div>\n            <div className=\"d-flex gap-2\">\n              <Form.Select\n                value={selectedProject}\n                onChange={(e) => {\n                  setSelectedProject(e.target.value);\n                  fetchRoutes();\n                }}\n                style={{ width: '200px' }}\n              >\n                <option value=\"\">All Projects</option>\n                {projects.map((project) => (\n                  <option key={project.projectId} value={project.projectId}>\n                    {project.projectName}\n                  </option>\n                ))}\n              </Form.Select>\n              {['Admin', 'Client'].includes(user.role) && (\n                <Button variant=\"primary\" onClick={() => handleShowModal('create')}>\n                  <FaPlus className=\"me-2\" />Add Route\n                </Button>\n              )}\n            </div>\n          </div>\n        </Col>\n      </Row>\n\n      {alert.show && (\n        <Row className=\"mb-3\">\n          <Col>\n            <Alert variant={alert.type} dismissible onClose={() => setAlert({ show: false, message: '', type: 'success' })}>\n              {alert.message}\n            </Alert>\n          </Col>\n        </Row>\n      )}\n\n      <Row>\n        <Col>\n          <Card>\n            <Card.Header>\n              <h5 className=\"mb-0\">Routes ({routes.length})</h5>\n            </Card.Header>\n            <Card.Body>\n              {routes.length === 0 ? (\n                <div className=\"text-center py-4\">\n                  <FaRoute size={48} className=\"text-muted mb-3\" />\n                  <p className=\"text-muted\">No routes found</p>\n                </div>\n              ) : (\n                <Table responsive hover>\n                  <thead>\n                    <tr>\n                      <th>Route Name</th>\n                      <th>Project</th>\n                      <th>Type</th>\n                      <th>Priority</th>\n                      <th>Assignments</th>\n                      <th>Status</th>\n                      <th>Actions</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    {routes.map((route) => (\n                      <tr key={route.routeId}>\n                        <td>\n                          <div className=\"d-flex align-items-center\">\n                            <strong>{route.routeName}</strong>\n                            {route.isShared && (\n                              <FaShare className=\"ms-2 text-info\" title=\"Shared Route\" />\n                            )}\n                          </div>\n                          <small className=\"text-muted\">{route.routeId}</small>\n                        </td>\n                        <td>\n                          <Badge bg=\"secondary\">\n                            <FaProjectDiagram className=\"me-1\" />\n                            {route.projectName}\n                          </Badge>\n                        </td>\n                        <td>{getTypeBadge(route.routeType)}</td>\n                        <td>{getPriorityBadge(route.priority)}</td>\n                        <td>\n                          <Badge bg=\"info\">\n                            {route.assignedCount || 0} assigned\n                          </Badge>\n                        </td>\n                        <td>\n                          <Badge bg={route.isActive ? 'success' : 'secondary'}>\n                            {route.isActive ? 'Active' : 'Inactive'}\n                          </Badge>\n                        </td>\n                        <td>\n                          <div className=\"btn-group\" role=\"group\">\n                            <Button\n                              variant=\"outline-info\"\n                              size=\"sm\"\n                              onClick={() => handleShowModal('view', route)}\n                              title=\"View Details\"\n                            >\n                              <FaEye />\n                            </Button>\n                            {['Admin', 'Client', 'Supervisor'].includes(user.role) && (\n                              <Button\n                                variant=\"outline-success\"\n                                size=\"sm\"\n                                onClick={() => handleShowAssignModal(route)}\n                                title=\"Assign to Field Officers\"\n                              >\n                                <FaUserPlus />\n                              </Button>\n                            )}\n                            {['Admin', 'Client'].includes(user.role) && (\n                              <>\n                                <Button\n                                  variant=\"outline-warning\"\n                                  size=\"sm\"\n                                  onClick={() => handleShowModal('edit', route)}\n                                  title=\"Edit Route\"\n                                >\n                                  <FaEdit />\n                                </Button>\n                                <Button\n                                  variant=\"outline-danger\"\n                                  size=\"sm\"\n                                  onClick={() => {/* handleDelete(route.routeId) */}}\n                                  title=\"Delete Route\"\n                                >\n                                  <FaTrash />\n                                </Button>\n                              </>\n                            )}\n                          </div>\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </Table>\n              )}\n            </Card.Body>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* Route Modal */}\n      <Modal show={showModal} onHide={() => setShowModal(false)} size=\"lg\">\n        <Modal.Header closeButton>\n          <Modal.Title>\n            {modalMode === 'create' && 'Add New Route'}\n            {modalMode === 'edit' && 'Edit Route'}\n            {modalMode === 'view' && 'Route Details'}\n          </Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          <Form onSubmit={handleSubmit}>\n            <Row>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Route Name *</Form.Label>\n                  <Form.Control\n                    type=\"text\"\n                    name=\"routeName\"\n                    value={formData.routeName}\n                    onChange={handleInputChange}\n                    required\n                    disabled={modalMode === 'view'}\n                  />\n                </Form.Group>\n              </Col>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Project *</Form.Label>\n                  <Form.Select\n                    name=\"projectId\"\n                    value={formData.projectId}\n                    onChange={handleInputChange}\n                    required\n                    disabled={modalMode === 'view'}\n                  >\n                    <option value=\"\">Select Project</option>\n                    {projects.map((project) => (\n                      <option key={project.projectId} value={project.projectId}>\n                        {project.projectName}\n                      </option>\n                    ))}\n                  </Form.Select>\n                </Form.Group>\n              </Col>\n            </Row>\n            \n            <Form.Group className=\"mb-3\">\n              <Form.Label>Description</Form.Label>\n              <Form.Control\n                as=\"textarea\"\n                rows={3}\n                name=\"description\"\n                value={formData.description}\n                onChange={handleInputChange}\n                disabled={modalMode === 'view'}\n              />\n            </Form.Group>\n            \n            <Row>\n              <Col md={4}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Route Type</Form.Label>\n                  <Form.Select\n                    name=\"routeType\"\n                    value={formData.routeType}\n                    onChange={handleInputChange}\n                    disabled={modalMode === 'view'}\n                  >\n                    <option value=\"Road Safety\">Road Safety</option>\n                    <option value=\"Blind Spot\">Blind Spot</option>\n                    <option value=\"Mixed\">Mixed</option>\n                  </Form.Select>\n                </Form.Group>\n              </Col>\n              <Col md={4}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Priority</Form.Label>\n                  <Form.Select\n                    name=\"priority\"\n                    value={formData.priority}\n                    onChange={handleInputChange}\n                    disabled={modalMode === 'view'}\n                  >\n                    <option value=\"High\">High</option>\n                    <option value=\"Medium\">Medium</option>\n                    <option value=\"Low\">Low</option>\n                  </Form.Select>\n                </Form.Group>\n              </Col>\n              <Col md={4}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Check\n                    type=\"checkbox\"\n                    name=\"isShared\"\n                    label=\"Shared Route\"\n                    checked={formData.isShared}\n                    onChange={handleInputChange}\n                    disabled={modalMode === 'view'}\n                    className=\"mt-4\"\n                  />\n                </Form.Group>\n              </Col>\n            </Row>\n          </Form>\n        </Modal.Body>\n        <Modal.Footer>\n          <Button variant=\"secondary\" onClick={() => setShowModal(false)}>\n            {modalMode === 'view' ? 'Close' : 'Cancel'}\n          </Button>\n          {modalMode !== 'view' && (\n            <Button variant=\"primary\" onClick={handleSubmit}>\n              {modalMode === 'create' ? 'Create Route' : 'Update Route'}\n            </Button>\n          )}\n        </Modal.Footer>\n      </Modal>\n\n      {/* Assignment Modal */}\n      <Modal show={showAssignModal} onHide={() => setShowAssignModal(false)} size=\"lg\">\n        <Modal.Header closeButton>\n          <Modal.Title>Assign Route: {selectedRoute?.routeName}</Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          <Form onSubmit={handleAssignRoute}>\n            <Form.Group className=\"mb-3\">\n              <Form.Label>Select Field Officers *</Form.Label>\n              <div className=\"border rounded p-3\" style={{ maxHeight: '200px', overflowY: 'auto' }}>\n                {fieldOfficers.length === 0 ? (\n                  <p className=\"text-muted\">No field officers available</p>\n                ) : (\n                  fieldOfficers.map((fo) => (\n                    <Form.Check\n                      key={fo.userId}\n                      type=\"checkbox\"\n                      id={`fo-${fo.userId}`}\n                      label={\n                        <div>\n                          <strong>{fo.fullName}</strong> ({fo.username})\n                          <br />\n                          <small className=\"text-muted\">\n                            Active assignments: {fo.activeAssignments}\n                          </small>\n                        </div>\n                      }\n                      value={fo.userId}\n                      checked={assignmentData.fieldOfficerIds.includes(fo.userId)}\n                      onChange={handleAssignmentChange}\n                      name=\"fieldOfficerIds\"\n                      className=\"mb-2\"\n                    />\n                  ))\n                )}\n              </div>\n            </Form.Group>\n            \n            <Row>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Start Date</Form.Label>\n                  <Form.Control\n                    type=\"date\"\n                    name=\"startDate\"\n                    value={assignmentData.startDate}\n                    onChange={handleAssignmentChange}\n                  />\n                </Form.Group>\n              </Col>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>End Date (Optional)</Form.Label>\n                  <Form.Control\n                    type=\"date\"\n                    name=\"endDate\"\n                    value={assignmentData.endDate}\n                    onChange={handleAssignmentChange}\n                  />\n                </Form.Group>\n              </Col>\n            </Row>\n            \n            <Form.Group className=\"mb-3\">\n              <Form.Label>Priority</Form.Label>\n              <Form.Select\n                name=\"priority\"\n                value={assignmentData.priority}\n                onChange={handleAssignmentChange}\n              >\n                <option value=\"High\">High</option>\n                <option value=\"Medium\">Medium</option>\n                <option value=\"Low\">Low</option>\n              </Form.Select>\n            </Form.Group>\n            \n            <Form.Group className=\"mb-3\">\n              <Form.Label>Notes</Form.Label>\n              <Form.Control\n                as=\"textarea\"\n                rows={3}\n                name=\"notes\"\n                value={assignmentData.notes}\n                onChange={handleAssignmentChange}\n                placeholder=\"Assignment instructions or notes...\"\n              />\n            </Form.Group>\n          </Form>\n        </Modal.Body>\n        <Modal.Footer>\n          <Button variant=\"secondary\" onClick={() => setShowAssignModal(false)}>\n            Cancel\n          </Button>\n          <Button variant=\"primary\" onClick={handleAssignRoute}>\n            Assign Route\n          </Button>\n        </Modal.Footer>\n      </Modal>\n    </Container>\n  );\n};\n\nexport default RouteManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAEC,QAAQ,QAAQ,iBAAiB;AACxH,SAASC,OAAO,EAAEC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,KAAK,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,OAAO,QAAQ,gBAAgB;AAC/G,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1B,MAAMC,eAAe,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EACpC,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACgC,QAAQ,EAAEC,WAAW,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACkC,aAAa,EAAEC,gBAAgB,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACoC,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsC,SAAS,EAAEC,YAAY,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACwC,eAAe,EAAEC,kBAAkB,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC0C,SAAS,EAAEC,YAAY,CAAC,GAAG3C,QAAQ,CAAC,QAAQ,CAAC;EACpD,MAAM,CAAC4C,aAAa,EAAEC,gBAAgB,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC8C,eAAe,EAAEC,kBAAkB,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACgD,QAAQ,EAAEC,WAAW,CAAC,GAAGjD,QAAQ,CAAC;IACvCkD,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE,aAAa;IACxBC,QAAQ,EAAE,QAAQ;IAClBC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGzD,QAAQ,CAAC;IACnD0D,eAAe,EAAE,EAAE;IACnBC,SAAS,EAAE,EAAE;IACbC,OAAO,EAAE,EAAE;IACXN,QAAQ,EAAE,QAAQ;IAClBO,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG/D,QAAQ,CAAC;IAAEgE,IAAI,EAAE,KAAK;IAAEC,OAAO,EAAE,EAAE;IAAEC,IAAI,EAAE;EAAU,CAAC,CAAC;EAEjFjE,SAAS,CAAC,MAAM;IACdkE,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACF9B,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM+B,OAAO,CAACC,GAAG,CAAC,CAChBC,WAAW,CAAC,CAAC,EACbC,aAAa,CAAC,CAAC,EACfC,kBAAkB,CAAC,CAAC,CACrB,CAAC;IACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CE,SAAS,CAAC,qBAAqB,EAAE,QAAQ,CAAC;IAC5C,CAAC,SAAS;MACRtC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMiC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACF,MAAMM,QAAQ,GAAG,MAAMtD,KAAK,CAACuD,GAAG,CAAC,8BAA8B,EAAE;QAC/DC,MAAM,EAAE;UACNC,SAAS,EAAEnD,IAAI,CAACoD,IAAI;UACpBC,OAAO,EAAErD,IAAI,CAACsD,MAAM,IAAItD,IAAI,CAACuD,QAAQ;UACrCC,SAAS,EAAExD,IAAI,CAACyD,QAAQ;UACxBC,UAAU,EAAExC;QACd;MACF,CAAC,CAAC;MAEF,IAAI8B,QAAQ,CAACW,IAAI,CAACC,OAAO,EAAE;QACzBzD,SAAS,CAAC6C,QAAQ,CAACW,IAAI,CAACzD,MAAM,CAAC;MACjC;IACF,CAAC,CAAC,OAAO2C,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC;EAED,MAAMF,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMK,QAAQ,GAAG,MAAMtD,KAAK,CAACuD,GAAG,CAAC,gCAAgC,EAAE;QACjEC,MAAM,EAAE;UACNC,SAAS,EAAEnD,IAAI,CAACoD,IAAI;UACpBC,OAAO,EAAErD,IAAI,CAACsD,MAAM,IAAItD,IAAI,CAACuD,QAAQ;UACrCC,SAAS,EAAExD,IAAI,CAACyD;QAClB;MACF,CAAC,CAAC;MAEF,IAAIT,QAAQ,CAACW,IAAI,CAACC,OAAO,EAAE;QACzBvD,WAAW,CAAC2C,QAAQ,CAACW,IAAI,CAACvD,QAAQ,CAAC;MACrC;IACF,CAAC,CAAC,OAAOyC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAClD;EACF,CAAC;EAED,MAAMD,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF,MAAMI,QAAQ,GAAG,MAAMtD,KAAK,CAACuD,GAAG,CAAC,gDAAgD,EAAE;QACjFC,MAAM,EAAE;UACNC,SAAS,EAAEnD,IAAI,CAACoD,IAAI;UACpBI,SAAS,EAAExD,IAAI,CAACyD,QAAQ;UACxBI,aAAa,EAAE7D,IAAI,CAACoD,IAAI,KAAK,YAAY,GAAGpD,IAAI,CAACsD,MAAM,GAAGQ;QAC5D;MACF,CAAC,CAAC;MAEF,IAAId,QAAQ,CAACW,IAAI,CAACC,OAAO,EAAE;QACzBrD,gBAAgB,CAACyC,QAAQ,CAACW,IAAI,CAACrD,aAAa,CAAC;MAC/C;IACF,CAAC,CAAC,OAAOuC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACxD;EACF,CAAC;EAED,MAAME,SAAS,GAAGA,CAACV,OAAO,EAAEC,IAAI,GAAG,SAAS,KAAK;IAC/CH,QAAQ,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO;MAAEC;IAAK,CAAC,CAAC;IACvCyB,UAAU,CAAC,MAAM5B,QAAQ,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE,EAAE;MAAEC,IAAI,EAAE;IAAU,CAAC,CAAC,EAAE,IAAI,CAAC;EACjF,CAAC;EAED,MAAM0B,eAAe,GAAGA,CAACC,IAAI,EAAEC,KAAK,GAAG,IAAI,KAAK;IAC9CnD,YAAY,CAACkD,IAAI,CAAC;IAClBhD,gBAAgB,CAACiD,KAAK,CAAC;IAEvB,IAAID,IAAI,KAAK,QAAQ,EAAE;MACrB5C,WAAW,CAAC;QACVC,SAAS,EAAE,EAAE;QACbC,WAAW,EAAE,EAAE;QACfC,SAAS,EAAE,EAAE;QACbC,SAAS,EAAE,aAAa;QACxBC,QAAQ,EAAE,QAAQ;QAClBC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIuC,KAAK,EAAE;MAChB7C,WAAW,CAAC;QACVC,SAAS,EAAE4C,KAAK,CAAC5C,SAAS,IAAI,EAAE;QAChCC,WAAW,EAAE2C,KAAK,CAAC3C,WAAW,IAAI,EAAE;QACpCC,SAAS,EAAE0C,KAAK,CAAC1C,SAAS,IAAI,EAAE;QAChCC,SAAS,EAAEyC,KAAK,CAACzC,SAAS,IAAI,aAAa;QAC3CC,QAAQ,EAAEwC,KAAK,CAACxC,QAAQ,IAAI,QAAQ;QACpCC,QAAQ,EAAEuC,KAAK,CAACvC,QAAQ,IAAI;MAC9B,CAAC,CAAC;IACJ;IAEAhB,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMwD,qBAAqB,GAAID,KAAK,IAAK;IACvCjD,gBAAgB,CAACiD,KAAK,CAAC;IACvBrC,iBAAiB,CAAC;MAChBC,eAAe,EAAE,EAAE;MACnBC,SAAS,EAAE,IAAIqC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACjDtC,OAAO,EAAE,EAAE;MACXN,QAAQ,EAAE,QAAQ;MAClBO,KAAK,EAAE;IACT,CAAC,CAAC;IACFpB,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAM0D,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC,KAAK;MAAEpC,IAAI;MAAEqC;IAAQ,CAAC,GAAGH,CAAC,CAACI,MAAM;IAC/CvD,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACqD,IAAI,GAAGnC,IAAI,KAAK,UAAU,GAAGqC,OAAO,GAAGD;IAC1C,CAAC,CAAC;EACJ,CAAC;EAED,MAAMG,sBAAsB,GAAIL,CAAC,IAAK;IACpC,MAAM;MAAEC,IAAI;MAAEC,KAAK;MAAEpC,IAAI;MAAEqC;IAAQ,CAAC,GAAGH,CAAC,CAACI,MAAM;IAE/C,IAAIH,IAAI,KAAK,iBAAiB,EAAE;MAC9B,MAAMK,WAAW,GAAG,CAAC,GAAGlD,cAAc,CAACE,eAAe,CAAC;MACvD,IAAI6C,OAAO,EAAE;QACXG,WAAW,CAACC,IAAI,CAACL,KAAK,CAAC;MACzB,CAAC,MAAM;QACL,MAAMM,KAAK,GAAGF,WAAW,CAACG,OAAO,CAACP,KAAK,CAAC;QACxC,IAAIM,KAAK,GAAG,CAAC,CAAC,EAAEF,WAAW,CAACI,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MAC9C;MACAnD,iBAAiB,CAAC;QAAE,GAAGD,cAAc;QAAEE,eAAe,EAAEgD;MAAY,CAAC,CAAC;IACxE,CAAC,MAAM;MACLjD,iBAAiB,CAAC;QAChB,GAAGD,cAAc;QACjB,CAAC6C,IAAI,GAAGC;MACV,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMS,YAAY,GAAG,MAAOX,CAAC,IAAK;IAChCA,CAAC,CAACY,cAAc,CAAC,CAAC;IAElB,IAAI;MACF,IAAIpC,QAAQ;MACZ,MAAMqC,UAAU,GAAG;QACjB,GAAGjE,QAAQ;QACXqC,QAAQ,EAAEzD,IAAI,CAACyD,QAAQ,IAAI,YAAY;QACvC6B,SAAS,EAAEtF,IAAI,CAACsD,MAAM,IAAItD,IAAI,CAACuD;MACjC,CAAC;MAED,IAAIzC,SAAS,KAAK,QAAQ,EAAE;QAC1BkC,QAAQ,GAAG,MAAMtD,KAAK,CAAC6F,IAAI,CAAC,8BAA8B,EAAEF,UAAU,CAAC;MACzE,CAAC,MAAM,IAAIvE,SAAS,KAAK,MAAM,EAAE;QAC/BkC,QAAQ,GAAG,MAAMtD,KAAK,CAAC8F,GAAG,CAAC,gCAAgCxE,aAAa,CAACyE,OAAO,EAAE,EAAEJ,UAAU,CAAC;MACjG;MAEA,IAAIrC,QAAQ,CAACW,IAAI,CAACC,OAAO,EAAE;QACzBb,SAAS,CACPjC,SAAS,KAAK,QAAQ,GAAG,4BAA4B,GAAG,4BAA4B,EACpF,SACF,CAAC;QACD4B,WAAW,CAAC,CAAC;QACb/B,YAAY,CAAC,KAAK,CAAC;MACrB,CAAC,MAAM;QACLoC,SAAS,CAACC,QAAQ,CAACW,IAAI,CAACtB,OAAO,IAAI,kBAAkB,EAAE,QAAQ,CAAC;MAClE;IACF,CAAC,CAAC,OAAOQ,KAAK,EAAE;MAAA,IAAA6C,eAAA,EAAAC,oBAAA;MACd7C,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3CE,SAAS,CAAC,EAAA2C,eAAA,GAAA7C,KAAK,CAACG,QAAQ,cAAA0C,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgB/B,IAAI,cAAAgC,oBAAA,uBAApBA,oBAAA,CAAsBtD,OAAO,KAAI,oBAAoB,EAAE,QAAQ,CAAC;IAC5E;EACF,CAAC;EAED,MAAMuD,iBAAiB,GAAG,MAAOpB,CAAC,IAAK;IACrCA,CAAC,CAACY,cAAc,CAAC,CAAC;IAElB,IAAIxD,cAAc,CAACE,eAAe,CAAC+D,MAAM,KAAK,CAAC,EAAE;MAC/C9C,SAAS,CAAC,0CAA0C,EAAE,SAAS,CAAC;MAChE;IACF;IAEA,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMtD,KAAK,CAAC6F,IAAI,CAAC,gCAAgCvE,aAAa,CAACyE,OAAO,SAAS,EAAE;QAChG,GAAG7D,cAAc;QACjBkE,UAAU,EAAE9F,IAAI,CAACsD,MAAM,IAAItD,IAAI,CAACuD,QAAQ;QACxCwC,YAAY,EAAE/F,IAAI,CAACoD,IAAI,KAAK,YAAY,GAAGpD,IAAI,CAACsD,MAAM,GAAGQ;MAC3D,CAAC,CAAC;MAEF,IAAId,QAAQ,CAACW,IAAI,CAACC,OAAO,EAAE;QACzBb,SAAS,CAAC,6BAA6B,EAAE,SAAS,CAAC;QACnDL,WAAW,CAAC,CAAC;QACb7B,kBAAkB,CAAC,KAAK,CAAC;MAC3B,CAAC,MAAM;QACLkC,SAAS,CAACC,QAAQ,CAACW,IAAI,CAACtB,OAAO,IAAI,mBAAmB,EAAE,QAAQ,CAAC;MACnE;IACF,CAAC,CAAC,OAAOQ,KAAK,EAAE;MAAA,IAAAmD,gBAAA,EAAAC,qBAAA;MACdnD,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CE,SAAS,CAAC,EAAAiD,gBAAA,GAAAnD,KAAK,CAACG,QAAQ,cAAAgD,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBrC,IAAI,cAAAsC,qBAAA,uBAApBA,qBAAA,CAAsB5D,OAAO,KAAI,uBAAuB,EAAE,QAAQ,CAAC;IAC/E;EACF,CAAC;EAED,MAAM6D,gBAAgB,GAAIxE,QAAQ,IAAK;IACrC,MAAMyE,MAAM,GAAG;MACb,MAAM,EAAE,QAAQ;MAChB,QAAQ,EAAE,SAAS;MACnB,KAAK,EAAE;IACT,CAAC;IACD,oBAAOvG,OAAA,CAACb,KAAK;MAACqH,EAAE,EAAED,MAAM,CAACzE,QAAQ,CAAC,IAAI,WAAY;MAAA2E,QAAA,EAAE3E;IAAQ;MAAA4E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EACvE,CAAC;EAED,MAAMC,YAAY,GAAIpE,IAAI,IAAK;IAC7B,MAAM6D,MAAM,GAAG;MACb,aAAa,EAAE,SAAS;MACxB,YAAY,EAAE,MAAM;MACpB,OAAO,EAAE;IACX,CAAC;IACD,oBAAOvG,OAAA,CAACb,KAAK;MAACqH,EAAE,EAAED,MAAM,CAAC7D,IAAI,CAAC,IAAI,WAAY;MAAA+D,QAAA,EAAE/D;IAAI;MAAAgE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAC/D,CAAC;EAED,IAAIjG,OAAO,EAAE;IACX,oBACEZ,OAAA,CAACtB,SAAS;MAACqI,SAAS,EAAC,kDAAkD;MAACC,KAAK,EAAE;QAAEC,SAAS,EAAE;MAAQ,CAAE;MAAAR,QAAA,eACpGzG,OAAA,CAACZ,OAAO;QAAC8H,SAAS,EAAC,QAAQ;QAAC1D,IAAI,EAAC,QAAQ;QAAAiD,QAAA,eACvCzG,OAAA;UAAM+G,SAAS,EAAC,iBAAiB;UAAAN,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAEhB;EAEA,oBACE7G,OAAA,CAACtB,SAAS;IAACyI,KAAK;IAACJ,SAAS,EAAC,KAAK;IAAAN,QAAA,gBAC9BzG,OAAA,CAACrB,GAAG;MAACoI,SAAS,EAAC,MAAM;MAAAN,QAAA,eACnBzG,OAAA,CAACpB,GAAG;QAAA6H,QAAA,eACFzG,OAAA;UAAK+G,SAAS,EAAC,mDAAmD;UAAAN,QAAA,gBAChEzG,OAAA;YAAAyG,QAAA,gBACEzG,OAAA;cAAAyG,QAAA,gBAAIzG,OAAA,CAACV,OAAO;gBAACyH,SAAS,EAAC;cAAM;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,oBAAgB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrD7G,OAAA;cAAG+G,SAAS,EAAC,YAAY;cAAAN,QAAA,EAAC;YAA6B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,eACN7G,OAAA;YAAK+G,SAAS,EAAC,cAAc;YAAAN,QAAA,gBAC3BzG,OAAA,CAACf,IAAI,CAACmI,MAAM;cACVtC,KAAK,EAAExD,eAAgB;cACvB+F,QAAQ,EAAGzC,CAAC,IAAK;gBACfrD,kBAAkB,CAACqD,CAAC,CAACI,MAAM,CAACF,KAAK,CAAC;gBAClChC,WAAW,CAAC,CAAC;cACf,CAAE;cACFkE,KAAK,EAAE;gBAAEM,KAAK,EAAE;cAAQ,CAAE;cAAAb,QAAA,gBAE1BzG,OAAA;gBAAQ8E,KAAK,EAAC,EAAE;gBAAA2B,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACrCrG,QAAQ,CAAC+G,GAAG,CAAEC,OAAO,iBACpBxH,OAAA;gBAAgC8E,KAAK,EAAE0C,OAAO,CAAC5F,SAAU;gBAAA6E,QAAA,EACtDe,OAAO,CAACC;cAAW,GADTD,OAAO,CAAC5F,SAAS;gBAAA8E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEtB,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS,CAAC,EACb,CAAC,OAAO,EAAE,QAAQ,CAAC,CAACa,QAAQ,CAACtH,IAAI,CAACoD,IAAI,CAAC,iBACtCxD,OAAA,CAACjB,MAAM;cAAC4I,OAAO,EAAC,SAAS;cAACC,OAAO,EAAEA,CAAA,KAAMxD,eAAe,CAAC,QAAQ,CAAE;cAAAqC,QAAA,gBACjEzG,OAAA,CAACT,MAAM;gBAACwH,SAAS,EAAC;cAAM;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,aAC7B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELvE,KAAK,CAACE,IAAI,iBACTxC,OAAA,CAACrB,GAAG;MAACoI,SAAS,EAAC,MAAM;MAAAN,QAAA,eACnBzG,OAAA,CAACpB,GAAG;QAAA6H,QAAA,eACFzG,OAAA,CAACd,KAAK;UAACyI,OAAO,EAAErF,KAAK,CAACI,IAAK;UAACmF,WAAW;UAACC,OAAO,EAAEA,CAAA,KAAMvF,QAAQ,CAAC;YAAEC,IAAI,EAAE,KAAK;YAAEC,OAAO,EAAE,EAAE;YAAEC,IAAI,EAAE;UAAU,CAAC,CAAE;UAAA+D,QAAA,EAC5GnE,KAAK,CAACG;QAAO;UAAAiE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAED7G,OAAA,CAACrB,GAAG;MAAA8H,QAAA,eACFzG,OAAA,CAACpB,GAAG;QAAA6H,QAAA,eACFzG,OAAA,CAACnB,IAAI;UAAA4H,QAAA,gBACHzG,OAAA,CAACnB,IAAI,CAACkJ,MAAM;YAAAtB,QAAA,eACVzG,OAAA;cAAI+G,SAAS,EAAC,MAAM;cAAAN,QAAA,GAAC,UAAQ,EAACnG,MAAM,CAAC2F,MAAM,EAAC,GAAC;YAAA;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACd7G,OAAA,CAACnB,IAAI,CAACmJ,IAAI;YAAAvB,QAAA,EACPnG,MAAM,CAAC2F,MAAM,KAAK,CAAC,gBAClBjG,OAAA;cAAK+G,SAAS,EAAC,kBAAkB;cAAAN,QAAA,gBAC/BzG,OAAA,CAACV,OAAO;gBAAC2I,IAAI,EAAE,EAAG;gBAAClB,SAAS,EAAC;cAAiB;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACjD7G,OAAA;gBAAG+G,SAAS,EAAC,YAAY;gBAAAN,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC,gBAEN7G,OAAA,CAAClB,KAAK;cAACoJ,UAAU;cAACC,KAAK;cAAA1B,QAAA,gBACrBzG,OAAA;gBAAAyG,QAAA,eACEzG,OAAA;kBAAAyG,QAAA,gBACEzG,OAAA;oBAAAyG,QAAA,EAAI;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnB7G,OAAA;oBAAAyG,QAAA,EAAI;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChB7G,OAAA;oBAAAyG,QAAA,EAAI;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACb7G,OAAA;oBAAAyG,QAAA,EAAI;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjB7G,OAAA;oBAAAyG,QAAA,EAAI;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpB7G,OAAA;oBAAAyG,QAAA,EAAI;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACf7G,OAAA;oBAAAyG,QAAA,EAAI;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACR7G,OAAA;gBAAAyG,QAAA,EACGnG,MAAM,CAACiH,GAAG,CAAEjD,KAAK,iBAChBtE,OAAA;kBAAAyG,QAAA,gBACEzG,OAAA;oBAAAyG,QAAA,gBACEzG,OAAA;sBAAK+G,SAAS,EAAC,2BAA2B;sBAAAN,QAAA,gBACxCzG,OAAA;wBAAAyG,QAAA,EAASnC,KAAK,CAAC5C;sBAAS;wBAAAgF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAS,CAAC,EACjCvC,KAAK,CAACvC,QAAQ,iBACb/B,OAAA,CAACH,OAAO;wBAACkH,SAAS,EAAC,gBAAgB;wBAACqB,KAAK,EAAC;sBAAc;wBAAA1B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAC3D;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC,eACN7G,OAAA;sBAAO+G,SAAS,EAAC,YAAY;sBAAAN,QAAA,EAAEnC,KAAK,CAACuB;oBAAO;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC,eACL7G,OAAA;oBAAAyG,QAAA,eACEzG,OAAA,CAACb,KAAK;sBAACqH,EAAE,EAAC,WAAW;sBAAAC,QAAA,gBACnBzG,OAAA,CAACJ,gBAAgB;wBAACmH,SAAS,EAAC;sBAAM;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,EACpCvC,KAAK,CAACmD,WAAW;oBAAA;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACb;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACL7G,OAAA;oBAAAyG,QAAA,EAAKK,YAAY,CAACxC,KAAK,CAACzC,SAAS;kBAAC;oBAAA6E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACxC7G,OAAA;oBAAAyG,QAAA,EAAKH,gBAAgB,CAAChC,KAAK,CAACxC,QAAQ;kBAAC;oBAAA4E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC3C7G,OAAA;oBAAAyG,QAAA,eACEzG,OAAA,CAACb,KAAK;sBAACqH,EAAE,EAAC,MAAM;sBAAAC,QAAA,GACbnC,KAAK,CAAC+D,aAAa,IAAI,CAAC,EAAC,WAC5B;oBAAA;sBAAA3B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACL7G,OAAA;oBAAAyG,QAAA,eACEzG,OAAA,CAACb,KAAK;sBAACqH,EAAE,EAAElC,KAAK,CAACgE,QAAQ,GAAG,SAAS,GAAG,WAAY;sBAAA7B,QAAA,EACjDnC,KAAK,CAACgE,QAAQ,GAAG,QAAQ,GAAG;oBAAU;sBAAA5B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACL7G,OAAA;oBAAAyG,QAAA,eACEzG,OAAA;sBAAK+G,SAAS,EAAC,WAAW;sBAACvD,IAAI,EAAC,OAAO;sBAAAiD,QAAA,gBACrCzG,OAAA,CAACjB,MAAM;wBACL4I,OAAO,EAAC,cAAc;wBACtBM,IAAI,EAAC,IAAI;wBACTL,OAAO,EAAEA,CAAA,KAAMxD,eAAe,CAAC,MAAM,EAAEE,KAAK,CAAE;wBAC9C8D,KAAK,EAAC,cAAc;wBAAA3B,QAAA,eAEpBzG,OAAA,CAACN,KAAK;0BAAAgH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,EACR,CAAC,OAAO,EAAE,QAAQ,EAAE,YAAY,CAAC,CAACa,QAAQ,CAACtH,IAAI,CAACoD,IAAI,CAAC,iBACpDxD,OAAA,CAACjB,MAAM;wBACL4I,OAAO,EAAC,iBAAiB;wBACzBM,IAAI,EAAC,IAAI;wBACTL,OAAO,EAAEA,CAAA,KAAMrD,qBAAqB,CAACD,KAAK,CAAE;wBAC5C8D,KAAK,EAAC,0BAA0B;wBAAA3B,QAAA,eAEhCzG,OAAA,CAACL,UAAU;0BAAA+G,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACR,CACT,EACA,CAAC,OAAO,EAAE,QAAQ,CAAC,CAACa,QAAQ,CAACtH,IAAI,CAACoD,IAAI,CAAC,iBACtCxD,OAAA,CAAAE,SAAA;wBAAAuG,QAAA,gBACEzG,OAAA,CAACjB,MAAM;0BACL4I,OAAO,EAAC,iBAAiB;0BACzBM,IAAI,EAAC,IAAI;0BACTL,OAAO,EAAEA,CAAA,KAAMxD,eAAe,CAAC,MAAM,EAAEE,KAAK,CAAE;0BAC9C8D,KAAK,EAAC,YAAY;0BAAA3B,QAAA,eAElBzG,OAAA,CAACR,MAAM;4BAAAkH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC,eACT7G,OAAA,CAACjB,MAAM;0BACL4I,OAAO,EAAC,gBAAgB;0BACxBM,IAAI,EAAC,IAAI;0BACTL,OAAO,EAAEA,CAAA,KAAM,CAAC,kCAAmC;0BACnDQ,KAAK,EAAC,cAAc;0BAAA3B,QAAA,eAEpBzG,OAAA,CAACP,OAAO;4BAAAiH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC;sBAAA,eACT,CACH;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA,GArEEvC,KAAK,CAACuB,OAAO;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAsElB,CACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UACR;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7G,OAAA,CAAChB,KAAK;MAACwD,IAAI,EAAE1B,SAAU;MAACyH,MAAM,EAAEA,CAAA,KAAMxH,YAAY,CAAC,KAAK,CAAE;MAACkH,IAAI,EAAC,IAAI;MAAAxB,QAAA,gBAClEzG,OAAA,CAAChB,KAAK,CAAC+I,MAAM;QAACS,WAAW;QAAA/B,QAAA,eACvBzG,OAAA,CAAChB,KAAK,CAACyJ,KAAK;UAAAhC,QAAA,GACTvF,SAAS,KAAK,QAAQ,IAAI,eAAe,EACzCA,SAAS,KAAK,MAAM,IAAI,YAAY,EACpCA,SAAS,KAAK,MAAM,IAAI,eAAe;QAAA;UAAAwF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACf7G,OAAA,CAAChB,KAAK,CAACgJ,IAAI;QAAAvB,QAAA,eACTzG,OAAA,CAACf,IAAI;UAACyJ,QAAQ,EAAEnD,YAAa;UAAAkB,QAAA,gBAC3BzG,OAAA,CAACrB,GAAG;YAAA8H,QAAA,gBACFzG,OAAA,CAACpB,GAAG;cAAC+J,EAAE,EAAE,CAAE;cAAAlC,QAAA,eACTzG,OAAA,CAACf,IAAI,CAAC2J,KAAK;gBAAC7B,SAAS,EAAC,MAAM;gBAAAN,QAAA,gBAC1BzG,OAAA,CAACf,IAAI,CAAC4J,KAAK;kBAAApC,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrC7G,OAAA,CAACf,IAAI,CAAC6J,OAAO;kBACXpG,IAAI,EAAC,MAAM;kBACXmC,IAAI,EAAC,WAAW;kBAChBC,KAAK,EAAEtD,QAAQ,CAACE,SAAU;kBAC1B2F,QAAQ,EAAE1C,iBAAkB;kBAC5BoE,QAAQ;kBACRC,QAAQ,EAAE9H,SAAS,KAAK;gBAAO;kBAAAwF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN7G,OAAA,CAACpB,GAAG;cAAC+J,EAAE,EAAE,CAAE;cAAAlC,QAAA,eACTzG,OAAA,CAACf,IAAI,CAAC2J,KAAK;gBAAC7B,SAAS,EAAC,MAAM;gBAAAN,QAAA,gBAC1BzG,OAAA,CAACf,IAAI,CAAC4J,KAAK;kBAAApC,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAClC7G,OAAA,CAACf,IAAI,CAACmI,MAAM;kBACVvC,IAAI,EAAC,WAAW;kBAChBC,KAAK,EAAEtD,QAAQ,CAACI,SAAU;kBAC1ByF,QAAQ,EAAE1C,iBAAkB;kBAC5BoE,QAAQ;kBACRC,QAAQ,EAAE9H,SAAS,KAAK,MAAO;kBAAAuF,QAAA,gBAE/BzG,OAAA;oBAAQ8E,KAAK,EAAC,EAAE;oBAAA2B,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACvCrG,QAAQ,CAAC+G,GAAG,CAAEC,OAAO,iBACpBxH,OAAA;oBAAgC8E,KAAK,EAAE0C,OAAO,CAAC5F,SAAU;oBAAA6E,QAAA,EACtDe,OAAO,CAACC;kBAAW,GADTD,OAAO,CAAC5F,SAAS;oBAAA8E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEtB,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN7G,OAAA,CAACf,IAAI,CAAC2J,KAAK;YAAC7B,SAAS,EAAC,MAAM;YAAAN,QAAA,gBAC1BzG,OAAA,CAACf,IAAI,CAAC4J,KAAK;cAAApC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACpC7G,OAAA,CAACf,IAAI,CAAC6J,OAAO;cACXG,EAAE,EAAC,UAAU;cACbC,IAAI,EAAE,CAAE;cACRrE,IAAI,EAAC,aAAa;cAClBC,KAAK,EAAEtD,QAAQ,CAACG,WAAY;cAC5B0F,QAAQ,EAAE1C,iBAAkB;cAC5BqE,QAAQ,EAAE9H,SAAS,KAAK;YAAO;cAAAwF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC,eAEb7G,OAAA,CAACrB,GAAG;YAAA8H,QAAA,gBACFzG,OAAA,CAACpB,GAAG;cAAC+J,EAAE,EAAE,CAAE;cAAAlC,QAAA,eACTzG,OAAA,CAACf,IAAI,CAAC2J,KAAK;gBAAC7B,SAAS,EAAC,MAAM;gBAAAN,QAAA,gBAC1BzG,OAAA,CAACf,IAAI,CAAC4J,KAAK;kBAAApC,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACnC7G,OAAA,CAACf,IAAI,CAACmI,MAAM;kBACVvC,IAAI,EAAC,WAAW;kBAChBC,KAAK,EAAEtD,QAAQ,CAACK,SAAU;kBAC1BwF,QAAQ,EAAE1C,iBAAkB;kBAC5BqE,QAAQ,EAAE9H,SAAS,KAAK,MAAO;kBAAAuF,QAAA,gBAE/BzG,OAAA;oBAAQ8E,KAAK,EAAC,aAAa;oBAAA2B,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAChD7G,OAAA;oBAAQ8E,KAAK,EAAC,YAAY;oBAAA2B,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9C7G,OAAA;oBAAQ8E,KAAK,EAAC,OAAO;oBAAA2B,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN7G,OAAA,CAACpB,GAAG;cAAC+J,EAAE,EAAE,CAAE;cAAAlC,QAAA,eACTzG,OAAA,CAACf,IAAI,CAAC2J,KAAK;gBAAC7B,SAAS,EAAC,MAAM;gBAAAN,QAAA,gBAC1BzG,OAAA,CAACf,IAAI,CAAC4J,KAAK;kBAAApC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACjC7G,OAAA,CAACf,IAAI,CAACmI,MAAM;kBACVvC,IAAI,EAAC,UAAU;kBACfC,KAAK,EAAEtD,QAAQ,CAACM,QAAS;kBACzBuF,QAAQ,EAAE1C,iBAAkB;kBAC5BqE,QAAQ,EAAE9H,SAAS,KAAK,MAAO;kBAAAuF,QAAA,gBAE/BzG,OAAA;oBAAQ8E,KAAK,EAAC,MAAM;oBAAA2B,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAClC7G,OAAA;oBAAQ8E,KAAK,EAAC,QAAQ;oBAAA2B,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtC7G,OAAA;oBAAQ8E,KAAK,EAAC,KAAK;oBAAA2B,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN7G,OAAA,CAACpB,GAAG;cAAC+J,EAAE,EAAE,CAAE;cAAAlC,QAAA,eACTzG,OAAA,CAACf,IAAI,CAAC2J,KAAK;gBAAC7B,SAAS,EAAC,MAAM;gBAAAN,QAAA,eAC1BzG,OAAA,CAACf,IAAI,CAACkK,KAAK;kBACTzG,IAAI,EAAC,UAAU;kBACfmC,IAAI,EAAC,UAAU;kBACfuE,KAAK,EAAC,cAAc;kBACpBrE,OAAO,EAAEvD,QAAQ,CAACO,QAAS;kBAC3BsF,QAAQ,EAAE1C,iBAAkB;kBAC5BqE,QAAQ,EAAE9H,SAAS,KAAK,MAAO;kBAC/B6F,SAAS,EAAC;gBAAM;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACb7G,OAAA,CAAChB,KAAK,CAACqK,MAAM;QAAA5C,QAAA,gBACXzG,OAAA,CAACjB,MAAM;UAAC4I,OAAO,EAAC,WAAW;UAACC,OAAO,EAAEA,CAAA,KAAM7G,YAAY,CAAC,KAAK,CAAE;UAAA0F,QAAA,EAC5DvF,SAAS,KAAK,MAAM,GAAG,OAAO,GAAG;QAAQ;UAAAwF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,EACR3F,SAAS,KAAK,MAAM,iBACnBlB,OAAA,CAACjB,MAAM;UAAC4I,OAAO,EAAC,SAAS;UAACC,OAAO,EAAErC,YAAa;UAAAkB,QAAA,EAC7CvF,SAAS,KAAK,QAAQ,GAAG,cAAc,GAAG;QAAc;UAAAwF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGR7G,OAAA,CAAChB,KAAK;MAACwD,IAAI,EAAExB,eAAgB;MAACuH,MAAM,EAAEA,CAAA,KAAMtH,kBAAkB,CAAC,KAAK,CAAE;MAACgH,IAAI,EAAC,IAAI;MAAAxB,QAAA,gBAC9EzG,OAAA,CAAChB,KAAK,CAAC+I,MAAM;QAACS,WAAW;QAAA/B,QAAA,eACvBzG,OAAA,CAAChB,KAAK,CAACyJ,KAAK;UAAAhC,QAAA,GAAC,gBAAc,EAACrF,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEM,SAAS;QAAA;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC,eACf7G,OAAA,CAAChB,KAAK,CAACgJ,IAAI;QAAAvB,QAAA,eACTzG,OAAA,CAACf,IAAI;UAACyJ,QAAQ,EAAE1C,iBAAkB;UAAAS,QAAA,gBAChCzG,OAAA,CAACf,IAAI,CAAC2J,KAAK;YAAC7B,SAAS,EAAC,MAAM;YAAAN,QAAA,gBAC1BzG,OAAA,CAACf,IAAI,CAAC4J,KAAK;cAAApC,QAAA,EAAC;YAAuB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAChD7G,OAAA;cAAK+G,SAAS,EAAC,oBAAoB;cAACC,KAAK,EAAE;gBAAEsC,SAAS,EAAE,OAAO;gBAAEC,SAAS,EAAE;cAAO,CAAE;cAAA9C,QAAA,EAClF/F,aAAa,CAACuF,MAAM,KAAK,CAAC,gBACzBjG,OAAA;gBAAG+G,SAAS,EAAC,YAAY;gBAAAN,QAAA,EAAC;cAA2B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,GAEzDnG,aAAa,CAAC6G,GAAG,CAAEiC,EAAE,iBACnBxJ,OAAA,CAACf,IAAI,CAACkK,KAAK;gBAETzG,IAAI,EAAC,UAAU;gBACf+G,EAAE,EAAE,MAAMD,EAAE,CAAC9F,MAAM,EAAG;gBACtB0F,KAAK,eACHpJ,OAAA;kBAAAyG,QAAA,gBACEzG,OAAA;oBAAAyG,QAAA,EAAS+C,EAAE,CAACE;kBAAQ;oBAAAhD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC,MAAE,EAAC2C,EAAE,CAAC7F,QAAQ,EAAC,GAC7C,eAAA3D,OAAA;oBAAA0G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACN7G,OAAA;oBAAO+G,SAAS,EAAC,YAAY;oBAAAN,QAAA,GAAC,sBACR,EAAC+C,EAAE,CAACG,iBAAiB;kBAAA;oBAAAjD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CACN;gBACD/B,KAAK,EAAE0E,EAAE,CAAC9F,MAAO;gBACjBqB,OAAO,EAAE/C,cAAc,CAACE,eAAe,CAACwF,QAAQ,CAAC8B,EAAE,CAAC9F,MAAM,CAAE;gBAC5D2D,QAAQ,EAAEpC,sBAAuB;gBACjCJ,IAAI,EAAC,iBAAiB;gBACtBkC,SAAS,EAAC;cAAM,GAhBXyC,EAAE,CAAC9F,MAAM;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAiBf,CACF;YACF;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAEb7G,OAAA,CAACrB,GAAG;YAAA8H,QAAA,gBACFzG,OAAA,CAACpB,GAAG;cAAC+J,EAAE,EAAE,CAAE;cAAAlC,QAAA,eACTzG,OAAA,CAACf,IAAI,CAAC2J,KAAK;gBAAC7B,SAAS,EAAC,MAAM;gBAAAN,QAAA,gBAC1BzG,OAAA,CAACf,IAAI,CAAC4J,KAAK;kBAAApC,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACnC7G,OAAA,CAACf,IAAI,CAAC6J,OAAO;kBACXpG,IAAI,EAAC,MAAM;kBACXmC,IAAI,EAAC,WAAW;kBAChBC,KAAK,EAAE9C,cAAc,CAACG,SAAU;kBAChCkF,QAAQ,EAAEpC;gBAAuB;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN7G,OAAA,CAACpB,GAAG;cAAC+J,EAAE,EAAE,CAAE;cAAAlC,QAAA,eACTzG,OAAA,CAACf,IAAI,CAAC2J,KAAK;gBAAC7B,SAAS,EAAC,MAAM;gBAAAN,QAAA,gBAC1BzG,OAAA,CAACf,IAAI,CAAC4J,KAAK;kBAAApC,QAAA,EAAC;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC5C7G,OAAA,CAACf,IAAI,CAAC6J,OAAO;kBACXpG,IAAI,EAAC,MAAM;kBACXmC,IAAI,EAAC,SAAS;kBACdC,KAAK,EAAE9C,cAAc,CAACI,OAAQ;kBAC9BiF,QAAQ,EAAEpC;gBAAuB;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN7G,OAAA,CAACf,IAAI,CAAC2J,KAAK;YAAC7B,SAAS,EAAC,MAAM;YAAAN,QAAA,gBAC1BzG,OAAA,CAACf,IAAI,CAAC4J,KAAK;cAAApC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACjC7G,OAAA,CAACf,IAAI,CAACmI,MAAM;cACVvC,IAAI,EAAC,UAAU;cACfC,KAAK,EAAE9C,cAAc,CAACF,QAAS;cAC/BuF,QAAQ,EAAEpC,sBAAuB;cAAAwB,QAAA,gBAEjCzG,OAAA;gBAAQ8E,KAAK,EAAC,MAAM;gBAAA2B,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAClC7G,OAAA;gBAAQ8E,KAAK,EAAC,QAAQ;gBAAA2B,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtC7G,OAAA;gBAAQ8E,KAAK,EAAC,KAAK;gBAAA2B,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAEb7G,OAAA,CAACf,IAAI,CAAC2J,KAAK;YAAC7B,SAAS,EAAC,MAAM;YAAAN,QAAA,gBAC1BzG,OAAA,CAACf,IAAI,CAAC4J,KAAK;cAAApC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC9B7G,OAAA,CAACf,IAAI,CAAC6J,OAAO;cACXG,EAAE,EAAC,UAAU;cACbC,IAAI,EAAE,CAAE;cACRrE,IAAI,EAAC,OAAO;cACZC,KAAK,EAAE9C,cAAc,CAACK,KAAM;cAC5BgF,QAAQ,EAAEpC,sBAAuB;cACjC2E,WAAW,EAAC;YAAqC;cAAAlD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACb7G,OAAA,CAAChB,KAAK,CAACqK,MAAM;QAAA5C,QAAA,gBACXzG,OAAA,CAACjB,MAAM;UAAC4I,OAAO,EAAC,WAAW;UAACC,OAAO,EAAEA,CAAA,KAAM3G,kBAAkB,CAAC,KAAK,CAAE;UAAAwF,QAAA,EAAC;QAEtE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT7G,OAAA,CAACjB,MAAM;UAAC4I,OAAO,EAAC,SAAS;UAACC,OAAO,EAAE5B,iBAAkB;UAAAS,QAAA,EAAC;QAEtD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEhB,CAAC;AAACxG,EAAA,CAxnBIF,eAAe;AAAA0J,EAAA,GAAf1J,eAAe;AA0nBrB,eAAeA,eAAe;AAAC,IAAA0J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}