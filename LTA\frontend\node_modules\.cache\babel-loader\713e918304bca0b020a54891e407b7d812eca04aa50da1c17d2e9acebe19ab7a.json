{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Deep Learning\\\\LTAGIT\\\\LTA\\\\frontend\\\\src\\\\pages\\\\UserManagement\\\\Routes.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport './UserManagement.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Routes = () => {\n  _s();\n  const [routes, setRoutes] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    fetchRoutes();\n  }, []);\n  const fetchRoutes = async () => {\n    try {\n      setLoading(true);\n      // TODO: Replace with actual API endpoint\n      const response = await axios.get('/api/user-management/routes');\n      setRoutes(response.data.routes || []);\n    } catch (err) {\n      setError('Failed to fetch routes');\n      console.error('Error fetching routes:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"user-management-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading\",\n        children: \"Loading routes...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"user-management-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"user-management-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"user-management-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Route Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn-primary\",\n        children: \"Create New Route\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"user-management-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hierarchy-view\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Route Assignment\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hierarchy-tree\",\n          children: routes.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"no-data\",\n            children: \"No routes found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 15\n          }, this) : routes.map(route => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"route-node\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"route-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"route-name\",\n                children: route.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 61,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"route-type\",\n                children: route.type\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 62,\n                columnNumber: 21\n              }, this), route.isShared && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"route-shared\",\n                children: \"Shared\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 63,\n                columnNumber: 40\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"route-assignments\",\n              children: route.assignedTo && route.assignedTo.map(assignment => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"assignment-node\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"assignment-name\",\n                  children: assignment.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 68,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"assignment-role\",\n                  children: assignment.role\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 69,\n                  columnNumber: 25\n                }, this)]\n              }, assignment.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 67,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 19\n            }, this)]\n          }, route.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"management-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Quick Actions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"action-buttons\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-secondary\",\n            children: \"Assign to Field Officers\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-secondary\",\n            children: \"Mark as Shared\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-secondary\",\n            children: \"View Route Details\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 45,\n    columnNumber: 5\n  }, this);\n};\n_s(Routes, \"W3EiAcyipnRce5wU6wG5GQ5jT9I=\");\n_c = Routes;\nexport default Routes;\nvar _c;\n$RefreshReg$(_c, \"Routes\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "jsxDEV", "_jsxDEV", "Routes", "_s", "routes", "setRoutes", "loading", "setLoading", "error", "setError", "fetchRoutes", "response", "get", "data", "err", "console", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "map", "route", "name", "type", "isShared", "assignedTo", "assignment", "role", "id", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Deep Learning/LTAGIT/LTA/frontend/src/pages/UserManagement/Routes.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport './UserManagement.css';\n\nconst Routes = () => {\n  const [routes, setRoutes] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  useEffect(() => {\n    fetchRoutes();\n  }, []);\n\n  const fetchRoutes = async () => {\n    try {\n      setLoading(true);\n      // TODO: Replace with actual API endpoint\n      const response = await axios.get('/api/user-management/routes');\n      setRoutes(response.data.routes || []);\n    } catch (err) {\n      setError('Failed to fetch routes');\n      console.error('Error fetching routes:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"user-management-container\">\n        <div className=\"loading\">Loading routes...</div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"user-management-container\">\n        <div className=\"error\">{error}</div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"user-management-container\">\n      <div className=\"user-management-header\">\n        <h1>Route Management</h1>\n        <button className=\"btn-primary\">Create New Route</button>\n      </div>\n      \n      <div className=\"user-management-content\">\n        <div className=\"hierarchy-view\">\n          <h3>Route Assignment</h3>\n          <div className=\"hierarchy-tree\">\n            {routes.length === 0 ? (\n              <div className=\"no-data\">No routes found</div>\n            ) : (\n              routes.map((route) => (\n                <div key={route.id} className=\"route-node\">\n                  <div className=\"route-info\">\n                    <span className=\"route-name\">{route.name}</span>\n                    <span className=\"route-type\">{route.type}</span>\n                    {route.isShared && <span className=\"route-shared\">Shared</span>}\n                  </div>\n                  <div className=\"route-assignments\">\n                    {route.assignedTo && route.assignedTo.map((assignment) => (\n                      <div key={assignment.id} className=\"assignment-node\">\n                        <span className=\"assignment-name\">{assignment.name}</span>\n                        <span className=\"assignment-role\">{assignment.role}</span>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              ))\n            )}\n          </div>\n        </div>\n        \n        <div className=\"management-actions\">\n          <h3>Quick Actions</h3>\n          <div className=\"action-buttons\">\n            <button className=\"btn-secondary\">Assign to Field Officers</button>\n            <button className=\"btn-secondary\">Mark as Shared</button>\n            <button className=\"btn-secondary\">View Route Details</button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Routes;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACS,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACW,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAExCC,SAAS,CAAC,MAAM;IACdY,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACFH,UAAU,CAAC,IAAI,CAAC;MAChB;MACA,MAAMI,QAAQ,GAAG,MAAMZ,KAAK,CAACa,GAAG,CAAC,6BAA6B,CAAC;MAC/DP,SAAS,CAACM,QAAQ,CAACE,IAAI,CAACT,MAAM,IAAI,EAAE,CAAC;IACvC,CAAC,CAAC,OAAOU,GAAG,EAAE;MACZL,QAAQ,CAAC,wBAAwB,CAAC;MAClCM,OAAO,CAACP,KAAK,CAAC,wBAAwB,EAAEM,GAAG,CAAC;IAC9C,CAAC,SAAS;MACRP,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,IAAID,OAAO,EAAE;IACX,oBACEL,OAAA;MAAKe,SAAS,EAAC,2BAA2B;MAAAC,QAAA,eACxChB,OAAA;QAAKe,SAAS,EAAC,SAAS;QAAAC,QAAA,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7C,CAAC;EAEV;EAEA,IAAIb,KAAK,EAAE;IACT,oBACEP,OAAA;MAAKe,SAAS,EAAC,2BAA2B;MAAAC,QAAA,eACxChB,OAAA;QAAKe,SAAS,EAAC,OAAO;QAAAC,QAAA,EAAET;MAAK;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC,CAAC;EAEV;EAEA,oBACEpB,OAAA;IAAKe,SAAS,EAAC,2BAA2B;IAAAC,QAAA,gBACxChB,OAAA;MAAKe,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBACrChB,OAAA;QAAAgB,QAAA,EAAI;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACzBpB,OAAA;QAAQe,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtD,CAAC,eAENpB,OAAA;MAAKe,SAAS,EAAC,yBAAyB;MAAAC,QAAA,gBACtChB,OAAA;QAAKe,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BhB,OAAA;UAAAgB,QAAA,EAAI;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzBpB,OAAA;UAAKe,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAC5Bb,MAAM,CAACkB,MAAM,KAAK,CAAC,gBAClBrB,OAAA;YAAKe,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,GAE9CjB,MAAM,CAACmB,GAAG,CAAEC,KAAK,iBACfvB,OAAA;YAAoBe,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACxChB,OAAA;cAAKe,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBhB,OAAA;gBAAMe,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAEO,KAAK,CAACC;cAAI;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChDpB,OAAA;gBAAMe,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAEO,KAAK,CAACE;cAAI;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EAC/CG,KAAK,CAACG,QAAQ,iBAAI1B,OAAA;gBAAMe,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC,eACNpB,OAAA;cAAKe,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAC/BO,KAAK,CAACI,UAAU,IAAIJ,KAAK,CAACI,UAAU,CAACL,GAAG,CAAEM,UAAU,iBACnD5B,OAAA;gBAAyBe,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAClDhB,OAAA;kBAAMe,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAEY,UAAU,CAACJ;gBAAI;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1DpB,OAAA;kBAAMe,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAEY,UAAU,CAACC;gBAAI;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA,GAFlDQ,UAAU,CAACE,EAAE;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGlB,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA,GAbEG,KAAK,CAACO,EAAE;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAcb,CACN;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpB,OAAA;QAAKe,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjChB,OAAA;UAAAgB,QAAA,EAAI;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtBpB,OAAA;UAAKe,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BhB,OAAA;YAAQe,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACnEpB,OAAA;YAAQe,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACzDpB,OAAA;YAAQe,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClB,EAAA,CArFID,MAAM;AAAA8B,EAAA,GAAN9B,MAAM;AAuFZ,eAAeA,MAAM;AAAC,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}