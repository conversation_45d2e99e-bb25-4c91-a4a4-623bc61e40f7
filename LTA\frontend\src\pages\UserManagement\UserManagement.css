.user-management-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.user-management-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e0e0e0;
}

.user-management-header h1 {
  color: #2c3e50;
  margin: 0;
  font-size: 2rem;
  font-weight: 600;
}

.btn-primary {
  background-color: #FF9B33;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.btn-primary:hover {
  background-color: #e8851f;
}

.btn-secondary {
  background-color: #34495e;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
  margin: 5px;
  transition: background-color 0.3s ease;
}

.btn-secondary:hover {
  background-color: #2c3e50;
}

.user-management-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 30px;
  margin-top: 20px;
}

.hierarchy-view {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.hierarchy-view h3 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 1.3rem;
  font-weight: 600;
}

.hierarchy-tree {
  max-height: 600px;
  overflow-y: auto;
}

.management-actions {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  height: fit-content;
}

.management-actions h3 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 1.3rem;
  font-weight: 600;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

/* Node Styles */
.client-node, .supervisor-node, .field-officer-node, .project-node, .route-node {
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  margin-bottom: 15px;
  padding: 15px;
  background: #f8f9fa;
  transition: box-shadow 0.3s ease;
}

.client-node:hover, .supervisor-node:hover, .field-officer-node:hover, 
.project-node:hover, .route-node:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.client-info, .supervisor-info, .field-officer-info, .project-info, .route-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.client-name, .supervisor-name, .field-officer-name, .project-name, .route-name {
  font-weight: 600;
  color: #2c3e50;
  font-size: 1.1rem;
}

.client-role, .supervisor-role, .field-officer-role, .project-status, .route-type {
  background-color: #FF9B33;
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.route-shared {
  background-color: #27ae60;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 500;
  margin-left: 8px;
}

.client-projects, .supervisor-field-officers, .field-officer-routes, 
.project-routes, .route-assignments {
  margin-left: 20px;
  padding-left: 15px;
  border-left: 2px solid #e0e0e0;
}

.assignment-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  margin: 5px 0;
  background: white;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
}

.assignment-name {
  font-weight: 500;
  color: #34495e;
}

.assignment-role {
  background-color: #3498db;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
}

/* Loading and Error States */
.loading, .error {
  text-align: center;
  padding: 40px;
  font-size: 1.1rem;
}

.loading {
  color: #7f8c8d;
}

.error {
  color: #e74c3c;
  background-color: #fdf2f2;
  border: 1px solid #f5c6cb;
  border-radius: 6px;
}

.no-data {
  text-align: center;
  color: #7f8c8d;
  font-style: italic;
  padding: 40px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .user-management-content {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .user-management-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .user-management-header h1 {
    text-align: center;
  }
}
