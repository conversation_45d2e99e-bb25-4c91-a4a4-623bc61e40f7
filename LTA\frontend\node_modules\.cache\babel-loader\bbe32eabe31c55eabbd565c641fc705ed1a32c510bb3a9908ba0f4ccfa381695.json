{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Deep Learning\\\\LTAGIT\\\\LTA\\\\frontend\\\\src\\\\pages\\\\UserManagement\\\\FieldOfficers.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport './UserManagement.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FieldOfficers = () => {\n  _s();\n  const [fieldOfficers, setFieldOfficers] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    fetchFieldOfficers();\n  }, []);\n  const fetchFieldOfficers = async () => {\n    try {\n      setLoading(true);\n      // TODO: Replace with actual API endpoint\n      const response = await axios.get('/api/user-management/field-officers');\n      setFieldOfficers(response.data.fieldOfficers || []);\n    } catch (err) {\n      setError('Failed to fetch field officers');\n      console.error('Error fetching field officers:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"user-management-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading\",\n        children: \"Loading field officers...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"user-management-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"user-management-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"user-management-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Field Officer Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn-primary\",\n        children: \"Add New Field Officer\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"user-management-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hierarchy-view\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Field Officer List\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hierarchy-tree\",\n          children: fieldOfficers.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"no-data\",\n            children: \"No field officers found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 15\n          }, this) : fieldOfficers.map(fo => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"field-officer-node\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"field-officer-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"field-officer-name\",\n                children: fo.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 61,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"field-officer-role\",\n                children: \"Field Officer\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 62,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"field-officer-routes\",\n              children: fo.assignedRoutes && fo.assignedRoutes.map(route => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"route-node\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"route-name\",\n                  children: route.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 67,\n                  columnNumber: 25\n                }, this)\n              }, route.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 66,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 19\n            }, this)]\n          }, fo.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"management-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Quick Actions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"action-buttons\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-secondary\",\n            children: \"Assign Routes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-secondary\",\n            children: \"View Reports\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-secondary\",\n            children: \"Update Profile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 45,\n    columnNumber: 5\n  }, this);\n};\n_s(FieldOfficers, \"QmbypVnl8aYwqVPqxfEZgHyU22g=\");\n_c = FieldOfficers;\nexport default FieldOfficers;\nvar _c;\n$RefreshReg$(_c, \"FieldOfficers\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "jsxDEV", "_jsxDEV", "FieldOfficers", "_s", "fieldOfficers", "setFieldOfficers", "loading", "setLoading", "error", "setError", "fetchFieldOfficers", "response", "get", "data", "err", "console", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "map", "fo", "name", "assignedRoutes", "route", "id", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Deep Learning/LTAGIT/LTA/frontend/src/pages/UserManagement/FieldOfficers.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport './UserManagement.css';\n\nconst FieldOfficers = () => {\n  const [fieldOfficers, setFieldOfficers] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  useEffect(() => {\n    fetchFieldOfficers();\n  }, []);\n\n  const fetchFieldOfficers = async () => {\n    try {\n      setLoading(true);\n      // TODO: Replace with actual API endpoint\n      const response = await axios.get('/api/user-management/field-officers');\n      setFieldOfficers(response.data.fieldOfficers || []);\n    } catch (err) {\n      setError('Failed to fetch field officers');\n      console.error('Error fetching field officers:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"user-management-container\">\n        <div className=\"loading\">Loading field officers...</div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"user-management-container\">\n        <div className=\"error\">{error}</div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"user-management-container\">\n      <div className=\"user-management-header\">\n        <h1>Field Officer Management</h1>\n        <button className=\"btn-primary\">Add New Field Officer</button>\n      </div>\n      \n      <div className=\"user-management-content\">\n        <div className=\"hierarchy-view\">\n          <h3>Field Officer List</h3>\n          <div className=\"hierarchy-tree\">\n            {fieldOfficers.length === 0 ? (\n              <div className=\"no-data\">No field officers found</div>\n            ) : (\n              fieldOfficers.map((fo) => (\n                <div key={fo.id} className=\"field-officer-node\">\n                  <div className=\"field-officer-info\">\n                    <span className=\"field-officer-name\">{fo.name}</span>\n                    <span className=\"field-officer-role\">Field Officer</span>\n                  </div>\n                  <div className=\"field-officer-routes\">\n                    {fo.assignedRoutes && fo.assignedRoutes.map((route) => (\n                      <div key={route.id} className=\"route-node\">\n                        <span className=\"route-name\">{route.name}</span>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              ))\n            )}\n          </div>\n        </div>\n        \n        <div className=\"management-actions\">\n          <h3>Quick Actions</h3>\n          <div className=\"action-buttons\">\n            <button className=\"btn-secondary\">Assign Routes</button>\n            <button className=\"btn-secondary\">View Reports</button>\n            <button className=\"btn-secondary\">Update Profile</button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default FieldOfficers;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACS,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACW,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAExCC,SAAS,CAAC,MAAM;IACdY,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACFH,UAAU,CAAC,IAAI,CAAC;MAChB;MACA,MAAMI,QAAQ,GAAG,MAAMZ,KAAK,CAACa,GAAG,CAAC,qCAAqC,CAAC;MACvEP,gBAAgB,CAACM,QAAQ,CAACE,IAAI,CAACT,aAAa,IAAI,EAAE,CAAC;IACrD,CAAC,CAAC,OAAOU,GAAG,EAAE;MACZL,QAAQ,CAAC,gCAAgC,CAAC;MAC1CM,OAAO,CAACP,KAAK,CAAC,gCAAgC,EAAEM,GAAG,CAAC;IACtD,CAAC,SAAS;MACRP,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,IAAID,OAAO,EAAE;IACX,oBACEL,OAAA;MAAKe,SAAS,EAAC,2BAA2B;MAAAC,QAAA,eACxChB,OAAA;QAAKe,SAAS,EAAC,SAAS;QAAAC,QAAA,EAAC;MAAyB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrD,CAAC;EAEV;EAEA,IAAIb,KAAK,EAAE;IACT,oBACEP,OAAA;MAAKe,SAAS,EAAC,2BAA2B;MAAAC,QAAA,eACxChB,OAAA;QAAKe,SAAS,EAAC,OAAO;QAAAC,QAAA,EAAET;MAAK;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC,CAAC;EAEV;EAEA,oBACEpB,OAAA;IAAKe,SAAS,EAAC,2BAA2B;IAAAC,QAAA,gBACxChB,OAAA;MAAKe,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBACrChB,OAAA;QAAAgB,QAAA,EAAI;MAAwB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACjCpB,OAAA;QAAQe,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3D,CAAC,eAENpB,OAAA;MAAKe,SAAS,EAAC,yBAAyB;MAAAC,QAAA,gBACtChB,OAAA;QAAKe,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BhB,OAAA;UAAAgB,QAAA,EAAI;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3BpB,OAAA;UAAKe,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAC5Bb,aAAa,CAACkB,MAAM,KAAK,CAAC,gBACzBrB,OAAA;YAAKe,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,GAEtDjB,aAAa,CAACmB,GAAG,CAAEC,EAAE,iBACnBvB,OAAA;YAAiBe,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBAC7ChB,OAAA;cAAKe,SAAS,EAAC,oBAAoB;cAAAC,QAAA,gBACjChB,OAAA;gBAAMe,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAEO,EAAE,CAACC;cAAI;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrDpB,OAAA;gBAAMe,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC,eACNpB,OAAA;cAAKe,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAClCO,EAAE,CAACE,cAAc,IAAIF,EAAE,CAACE,cAAc,CAACH,GAAG,CAAEI,KAAK,iBAChD1B,OAAA;gBAAoBe,SAAS,EAAC,YAAY;gBAAAC,QAAA,eACxChB,OAAA;kBAAMe,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAEU,KAAK,CAACF;gBAAI;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC,GADxCM,KAAK,CAACC,EAAE;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEb,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA,GAXEG,EAAE,CAACI,EAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAYV,CACN;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpB,OAAA;QAAKe,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjChB,OAAA;UAAAgB,QAAA,EAAI;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtBpB,OAAA;UAAKe,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BhB,OAAA;YAAQe,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACxDpB,OAAA;YAAQe,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACvDpB,OAAA;YAAQe,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClB,EAAA,CAnFID,aAAa;AAAA2B,EAAA,GAAb3B,aAAa;AAqFnB,eAAeA,aAAa;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}