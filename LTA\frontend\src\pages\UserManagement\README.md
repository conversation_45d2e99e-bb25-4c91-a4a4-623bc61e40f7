# User Management System

This module implements a hierarchical user management system for the LTA application, supporting the organizational structure outlined in the project requirements.

## Features

### Sidebar Integration
- **Collapsible Menu**: Added "User Management" as a collapsible menu item in the sidebar
- **Responsive Design**: Works seamlessly on both desktop and mobile devices
- **Visual Indicators**: Uses chevron icons to show expand/collapse state

### User Hierarchy Support
The system supports the following user hierarchy:
```
Admin (YNM)
├── Client A
│   ├── Project 1
│   │   ├── Route 1 → Supervisor X → Field Officer M
│   │   ├── Route 2 → Supervisor Y → Field Officer N
│   │   └── Route 3 → Supervisor X → Field Officer M
│   └── Project 2
│       ├── Route 2 (shared)
│       └── Route 4 → Supervisor Y → Field Officer O
├── Supervisors
│   ├── Supervisor X
│   └── Supervisor Y
└── Field Officers
    ├── Field Officer M
    ├── Field Officer N
    └── Field Officer O
```

### Pages Included

1. **Clients** (`/user-management/clients`)
   - View and manage client hierarchy
   - Assign projects, supervisors, and field officers
   - Quick actions for client management

2. **Supervisors** (`/user-management/supervisors`)
   - Manage supervisor assignments
   - View field officers under each supervisor
   - Access to assigned routes and reports

3. **Field Officers** (`/user-management/field-officers`)
   - Manage field officer profiles
   - View assigned routes
   - Track report submissions

4. **Projects** (`/user-management/projects`)
   - Create and manage projects
   - Assign routes to projects
   - Support for shared routes across projects

5. **Routes** (`/user-management/routes`)
   - Route creation and management
   - Assignment to field officers
   - Support for shared routes with proper labeling

## Technical Implementation

### Frontend Components
- **Sidebar.js**: Enhanced with collapsible menu functionality
- **UserManagement Pages**: Individual pages for each management area
- **Responsive CSS**: Mobile-first design with proper breakpoints

### Styling
- **Consistent Design**: Follows the existing application theme
- **Hierarchy Visualization**: Tree-like structure for displaying relationships
- **Interactive Elements**: Hover effects and active states
- **Mobile Optimization**: Touch-friendly interface for mobile devices

### Navigation
- **React Router**: Proper routing for all user management pages
- **Protected Routes**: Authentication required for all management pages
- **Breadcrumb Support**: Easy navigation within the management system

## Future Enhancements

### Backend Integration
- RESTful APIs for CRUD operations
- Role-based access control (RBAC)
- Real-time updates for hierarchy changes

### Advanced Features
- Drag-and-drop route assignment
- Bulk user operations
- Advanced filtering and search
- Audit trail for user actions
- Export functionality for reports

### UI/UX Improvements
- Interactive hierarchy tree with expand/collapse
- Real-time notifications
- Advanced data visualization
- Keyboard shortcuts for power users

## Usage

1. **Access**: Navigate to the sidebar and click on "User Management"
2. **Expand**: The menu will expand to show all available options
3. **Navigate**: Click on any sub-item to access that management area
4. **Mobile**: On mobile devices, the menu works seamlessly with touch interactions

## Dependencies

- React 18.2.0
- React Router DOM 6.13.0
- React Icons 4.9.0
- CSS3 with Flexbox and Grid support

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+
- Mobile browsers (iOS Safari, Chrome Mobile)
