[{"C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\App.js": "3", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\Login.js": "4", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\Home.js": "5", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\RoadInfrastructure.js": "6", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\Recommendation.js": "7", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\Pavement.js": "8", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\DefectDetail.js": "9", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\Dashboard.js": "10", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\Header.js": "11", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\Sidebar.js": "12", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\ResponsiveCheckboxList.js": "13", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\ChartContainer.js": "14", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\VideoDefectDetection.js": "15", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\DefectMap.js": "16", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\hooks\\useResponsive.js": "17"}, {"size": 633, "mtime": 1753938470359, "results": "18", "hashOfConfig": "19"}, {"size": 376, "mtime": 1753938470372, "results": "20", "hashOfConfig": "19"}, {"size": 4259, "mtime": 1754568061089, "results": "21", "hashOfConfig": "19"}, {"size": 4500, "mtime": 1753963770147, "results": "22", "hashOfConfig": "19"}, {"size": 2994, "mtime": 1753938470363, "results": "23", "hashOfConfig": "19"}, {"size": 37851, "mtime": 1753938470369, "results": "24", "hashOfConfig": "19"}, {"size": 27373, "mtime": 1753938470368, "results": "25", "hashOfConfig": "19"}, {"size": 89420, "mtime": 1754565974357, "results": "26", "hashOfConfig": "19"}, {"size": 15619, "mtime": 1753938470361, "results": "27", "hashOfConfig": "19"}, {"size": 43573, "mtime": 1754128768623, "results": "28", "hashOfConfig": "19"}, {"size": 659, "mtime": 1753938470351, "results": "29", "hashOfConfig": "19"}, {"size": 7009, "mtime": 1754564428151, "results": "30", "hashOfConfig": "19"}, {"size": 4005, "mtime": 1753938470352, "results": "31", "hashOfConfig": "19"}, {"size": 3414, "mtime": 1753938470350, "results": "32", "hashOfConfig": "19"}, {"size": 37926, "mtime": 1753938470356, "results": "33", "hashOfConfig": "19"}, {"size": 14715, "mtime": 1753938470350, "results": "34", "hashOfConfig": "19"}, {"size": 1018, "mtime": 1753938470357, "results": "35", "hashOfConfig": "19"}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1djdb2s", {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\App.js", ["87"], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\Login.js", ["88", "89", "90", "91"], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\Home.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\RoadInfrastructure.js", ["92", "93", "94", "95", "96", "97", "98"], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\Recommendation.js", ["99", "100", "101", "102", "103", "104", "105", "106", "107", "108"], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\Pavement.js", ["109", "110", "111", "112", "113", "114", "115", "116", "117", "118"], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\DefectDetail.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\Dashboard.js", ["119", "120"], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\Header.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\Sidebar.js", ["121", "122"], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\ResponsiveCheckboxList.js", ["123"], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\ChartContainer.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\VideoDefectDetection.js", ["124", "125", "126", "127", "128", "129", "130", "131", "132"], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\DefectMap.js", ["133", "134", "135"], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\hooks\\useResponsive.js", [], [], {"ruleId": null, "fatal": true, "severity": 2, "message": "136", "line": 21, "column": 7, "nodeType": null}, {"ruleId": "137", "severity": 1, "message": "138", "line": 3, "column": 10, "nodeType": "139", "messageId": "140", "endLine": 3, "endColumn": 19}, {"ruleId": "137", "severity": 1, "message": "141", "line": 3, "column": 21, "nodeType": "139", "messageId": "140", "endLine": 3, "endColumn": 24}, {"ruleId": "137", "severity": 1, "message": "142", "line": 3, "column": 26, "nodeType": "139", "messageId": "140", "endLine": 3, "endColumn": 29}, {"ruleId": "143", "severity": 1, "message": "144", "line": 52, "column": 29, "nodeType": "145", "messageId": "146", "endLine": 52, "endColumn": 78}, {"ruleId": "137", "severity": 1, "message": "147", "line": 4, "column": 10, "nodeType": "139", "messageId": "140", "endLine": 4, "endColumn": 22}, {"ruleId": "137", "severity": 1, "message": "148", "line": 4, "column": 24, "nodeType": "139", "messageId": "140", "endLine": 4, "endColumn": 33}, {"ruleId": "137", "severity": 1, "message": "149", "line": 4, "column": 35, "nodeType": "139", "messageId": "140", "endLine": 4, "endColumn": 41}, {"ruleId": "137", "severity": 1, "message": "150", "line": 4, "column": 43, "nodeType": "139", "messageId": "140", "endLine": 4, "endColumn": 48}, {"ruleId": "137", "severity": 1, "message": "151", "line": 23, "column": 10, "nodeType": "139", "messageId": "140", "endLine": 23, "endColumn": 19}, {"ruleId": "137", "severity": 1, "message": "152", "line": 70, "column": 9, "nodeType": "139", "messageId": "140", "endLine": 70, "endColumn": 25}, {"ruleId": "153", "severity": 1, "message": "154", "line": 528, "column": 19, "nodeType": "155", "endLine": 532, "endColumn": 21}, {"ruleId": "137", "severity": 1, "message": "156", "line": 3, "column": 58, "nodeType": "139", "messageId": "140", "endLine": 3, "endColumn": 63}, {"ruleId": "137", "severity": 1, "message": "157", "line": 3, "column": 65, "nodeType": "139", "messageId": "140", "endLine": 3, "endColumn": 69}, {"ruleId": "137", "severity": 1, "message": "158", "line": 3, "column": 71, "nodeType": "139", "messageId": "140", "endLine": 3, "endColumn": 74}, {"ruleId": "137", "severity": 1, "message": "159", "line": 4, "column": 8, "nodeType": "139", "messageId": "140", "endLine": 4, "endColumn": 12}, {"ruleId": "137", "severity": 1, "message": "160", "line": 76, "column": 10, "nodeType": "139", "messageId": "140", "endLine": 76, "endColumn": 25}, {"ruleId": "137", "severity": 1, "message": "161", "line": 79, "column": 10, "nodeType": "139", "messageId": "140", "endLine": 79, "endColumn": 19}, {"ruleId": "137", "severity": 1, "message": "162", "line": 277, "column": 9, "nodeType": "139", "messageId": "140", "endLine": 277, "endColumn": 22}, {"ruleId": "137", "severity": 1, "message": "163", "line": 291, "column": 9, "nodeType": "139", "messageId": "140", "endLine": 291, "endColumn": 21}, {"ruleId": "137", "severity": 1, "message": "164", "line": 305, "column": 9, "nodeType": "139", "messageId": "140", "endLine": 305, "endColumn": 30}, {"ruleId": "137", "severity": 1, "message": "165", "line": 318, "column": 9, "nodeType": "139", "messageId": "140", "endLine": 318, "endColumn": 28}, {"ruleId": "137", "severity": 1, "message": "166", "line": 18, "column": 10, "nodeType": "139", "messageId": "140", "endLine": 18, "endColumn": 24}, {"ruleId": "137", "severity": 1, "message": "167", "line": 19, "column": 10, "nodeType": "139", "messageId": "140", "endLine": 19, "endColumn": 17}, {"ruleId": "137", "severity": 1, "message": "168", "line": 38, "column": 10, "nodeType": "139", "messageId": "140", "endLine": 38, "endColumn": 33}, {"ruleId": "137", "severity": 1, "message": "169", "line": 39, "column": 10, "nodeType": "139", "messageId": "140", "endLine": 39, "endColumn": 29}, {"ruleId": "170", "severity": 1, "message": "171", "line": 866, "column": 6, "nodeType": "172", "endLine": 866, "endColumn": 33, "suggestions": "173"}, {"ruleId": "170", "severity": 1, "message": "174", "line": 875, "column": 6, "nodeType": "172", "endLine": 875, "endColumn": 20, "suggestions": "175"}, {"ruleId": "170", "severity": 1, "message": "176", "line": 912, "column": 6, "nodeType": "172", "endLine": 912, "endColumn": 33, "suggestions": "177"}, {"ruleId": "153", "severity": 1, "message": "154", "line": 958, "column": 21, "nodeType": "155", "endLine": 962, "endColumn": 23}, {"ruleId": "153", "severity": 1, "message": "154", "line": 2061, "column": 17, "nodeType": "155", "endLine": 2071, "endColumn": 19}, {"ruleId": "153", "severity": 1, "message": "154", "line": 2079, "column": 19, "nodeType": "155", "endLine": 2089, "endColumn": 21}, {"ruleId": "137", "severity": 1, "message": "159", "line": 4, "column": 8, "nodeType": "139", "messageId": "140", "endLine": 4, "endColumn": 12}, {"ruleId": "170", "severity": 1, "message": "178", "line": 193, "column": 6, "nodeType": "172", "endLine": 193, "endColumn": 8, "suggestions": "179"}, {"ruleId": "137", "severity": 1, "message": "180", "line": 27, "column": 10, "nodeType": "139", "messageId": "140", "endLine": 27, "endColumn": 20}, {"ruleId": "181", "severity": 1, "message": "182", "line": 116, "column": 11, "nodeType": "155", "endLine": 116, "endColumn": 15}, {"ruleId": "137", "severity": 1, "message": "183", "line": 35, "column": 11, "nodeType": "139", "messageId": "140", "endLine": 35, "endColumn": 16}, {"ruleId": "137", "severity": 1, "message": "184", "line": 12, "column": 10, "nodeType": "139", "messageId": "140", "endLine": 12, "endColumn": 24}, {"ruleId": "137", "severity": 1, "message": "185", "line": 16, "column": 10, "nodeType": "139", "messageId": "140", "endLine": 16, "endColumn": 20}, {"ruleId": "137", "severity": 1, "message": "186", "line": 23, "column": 10, "nodeType": "139", "messageId": "140", "endLine": 23, "endColumn": 24}, {"ruleId": "137", "severity": 1, "message": "187", "line": 36, "column": 10, "nodeType": "139", "messageId": "140", "endLine": 36, "endColumn": 27}, {"ruleId": "137", "severity": 1, "message": "188", "line": 46, "column": 9, "nodeType": "139", "messageId": "140", "endLine": 46, "endColumn": 20}, {"ruleId": "170", "severity": 1, "message": "189", "line": 103, "column": 6, "nodeType": "172", "endLine": 103, "endColumn": 19, "suggestions": "190"}, {"ruleId": "137", "severity": 1, "message": "191", "line": 544, "column": 9, "nodeType": "139", "messageId": "140", "endLine": 544, "endColumn": 24}, {"ruleId": "137", "severity": 1, "message": "192", "line": 545, "column": 9, "nodeType": "139", "messageId": "140", "endLine": 545, "endColumn": 21}, {"ruleId": "137", "severity": 1, "message": "193", "line": 546, "column": 9, "nodeType": "139", "messageId": "140", "endLine": 546, "endColumn": 22}, {"ruleId": "137", "severity": 1, "message": "194", "line": 42, "column": 18, "nodeType": "139", "messageId": "140", "endLine": 42, "endColumn": 27}, {"ruleId": "137", "severity": 1, "message": "195", "line": 43, "column": 16, "nodeType": "139", "messageId": "140", "endLine": 43, "endColumn": 23}, {"ruleId": "170", "severity": 1, "message": "196", "line": 141, "column": 6, "nodeType": "172", "endLine": 141, "endColumn": 12, "suggestions": "197"}, "Parsing error: Identifier 'Routes' has already been declared. (21:7)", "no-unused-vars", "'Container' is defined but never used.", "Identifier", "unusedVar", "'Row' is defined but never used.", "'Col' is defined but never used.", "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'retryCount'.", "ArrowFunctionExpression", "unsafeRefs", "'MapContainer' is defined but never used.", "'TileLayer' is defined but never used.", "'Marker' is defined but never used.", "'Popup' is defined but never used.", "'imageFile' is assigned a value but never used.", "'roadInfraClasses' is assigned a value but never used.", "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", "'Badge' is defined but never used.", "'Tabs' is defined but never used.", "'Tab' is defined but never used.", "'Plot' is defined but never used.", "'recommendations' is assigned a value but never used.", "'chartData' is assigned a value but never used.", "'handleApprove' is assigned a value but never used.", "'handleReject' is assigned a value but never used.", "'getPriorityBadgeClass' is assigned a value but never used.", "'getStatusBadgeClass' is assigned a value but never used.", "'processedImage' is assigned a value but never used.", "'results' is assigned a value but never used.", "'showClassificationModal' is assigned a value but never used.", "'classificationError' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'batchResults.length'. Either include it or remove the dependency array.", "ArrayExpression", ["198"], "React Hook useEffect has missing dependencies: 'handleLocationRequest' and 'locationPermission'. Either include them or remove the dependency array.", ["199"], "React Hook useEffect has a missing dependency: 'handleLocationRequest'. Either include it or remove the dependency array.", ["200"], "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["201"], "'activePage' is assigned a value but never used.", "jsx-a11y/heading-has-content", "Headings must have content and the content must be accessible by a screen reader.", "'value' is assigned a value but never used.", "'processedVideo' is assigned a value but never used.", "'shouldStop' is assigned a value but never used.", "'recordedChunks' is assigned a value but never used.", "'currentDetections' is assigned a value but never used.", "'BUFFER_SIZE' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'handleStopRecording'. Either include it or remove the dependency array.", ["202"], "'handlePlayPause' is assigned a value but never used.", "'handleRewind' is assigned a value but never used.", "'handleForward' is assigned a value but never used.", "'setCenter' is assigned a value but never used.", "'setZoom' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchDefectData' and 'fetchUsers'. Either include them or remove the dependency array.", ["203"], {"desc": "204", "fix": "205"}, {"desc": "206", "fix": "207"}, {"desc": "208", "fix": "209"}, {"desc": "210", "fix": "211"}, {"desc": "212", "fix": "213"}, {"desc": "214", "fix": "215"}, "Update the dependencies array to be: [batchResults.length, roadClassificationEnabled]", {"range": "216", "text": "217"}, "Update the dependencies array to be: [cameraActive, handleLocationRequest, locationPermission]", {"range": "218", "text": "219"}, "Update the dependencies array to be: [cameraActive, coordinates, handleLocationRequest]", {"range": "220", "text": "221"}, "Update the dependencies array to be: [fetchData]", {"range": "222", "text": "223"}, "Update the dependencies array to be: [handleStopRecording, isRecording]", {"range": "224", "text": "225"}, "Update the dependencies array to be: [fetchDefectData, fetchUsers, user]", {"range": "226", "text": "227"}, [29440, 29467], "[batchResults.length, roadClassificationEnabled]", [29694, 29708], "[cameraActive, handleLocationRequest, locationPermission]", [30878, 30905], "[cameraActive, coordinates, handleLocationRequest]", [6950, 6952], "[fetchData]", [3893, 3906], "[handleStopRecording, isRecording]", [5311, 5317], "[fetchDefectData, fetchUsers, user]"]