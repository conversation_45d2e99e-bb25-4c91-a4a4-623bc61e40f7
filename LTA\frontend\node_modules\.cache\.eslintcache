[{"C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\App.js": "3", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\Login.js": "4", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\Home.js": "5", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\RoadInfrastructure.js": "6", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\Recommendation.js": "7", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\Pavement.js": "8", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\DefectDetail.js": "9", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\Dashboard.js": "10", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\Header.js": "11", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\Sidebar.js": "12", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\ResponsiveCheckboxList.js": "13", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\ChartContainer.js": "14", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\VideoDefectDetection.js": "15", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\DefectMap.js": "16", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\hooks\\useResponsive.js": "17", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\UserManagement\\FieldOfficers.js": "18", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\UserManagement\\Supervisors.js": "19", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\UserManagement\\Clients.js": "20", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\UserManagement\\Routes.js": "21", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\UserManagement\\Projects.js": "22"}, {"size": 633, "mtime": 1753938470359, "results": "23", "hashOfConfig": "24"}, {"size": 376, "mtime": 1753938470372, "results": "25", "hashOfConfig": "24"}, {"size": 4277, "mtime": 1754568463310, "results": "26", "hashOfConfig": "24"}, {"size": 4500, "mtime": 1753963770147, "results": "27", "hashOfConfig": "24"}, {"size": 2994, "mtime": 1753938470363, "results": "28", "hashOfConfig": "24"}, {"size": 37851, "mtime": 1753938470369, "results": "29", "hashOfConfig": "24"}, {"size": 27373, "mtime": 1753938470368, "results": "30", "hashOfConfig": "24"}, {"size": 89420, "mtime": 1754568543238, "results": "31", "hashOfConfig": "24"}, {"size": 15619, "mtime": 1753938470361, "results": "32", "hashOfConfig": "24"}, {"size": 43573, "mtime": 1754128768623, "results": "33", "hashOfConfig": "24"}, {"size": 659, "mtime": 1753938470351, "results": "34", "hashOfConfig": "24"}, {"size": 6841, "mtime": 1754567907137, "results": "35", "hashOfConfig": "24"}, {"size": 4005, "mtime": 1753938470352, "results": "36", "hashOfConfig": "24"}, {"size": 3414, "mtime": 1753938470350, "results": "37", "hashOfConfig": "24"}, {"size": 37926, "mtime": 1753938470356, "results": "38", "hashOfConfig": "24"}, {"size": 14715, "mtime": 1753938470350, "results": "39", "hashOfConfig": "24"}, {"size": 1018, "mtime": 1753938470357, "results": "40", "hashOfConfig": "24"}, {"size": 2862, "mtime": 1754567968737, "results": "41", "hashOfConfig": "24"}, {"size": 2876, "mtime": 1754567957502, "results": "42", "hashOfConfig": "24"}, {"size": 2735, "mtime": 1754567944383, "results": "43", "hashOfConfig": "24"}, {"size": 2918, "mtime": 1754567990888, "results": "44", "hashOfConfig": "24"}, {"size": 2851, "mtime": 1754567979703, "results": "45", "hashOfConfig": "24"}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1djdb2s", {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\Login.js", ["112", "113", "114", "115"], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\Home.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\RoadInfrastructure.js", ["116", "117", "118", "119", "120", "121", "122"], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\Recommendation.js", ["123", "124", "125", "126", "127", "128", "129", "130", "131", "132"], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\Pavement.js", ["133", "134", "135", "136", "137", "138", "139", "140", "141", "142"], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\DefectDetail.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\Dashboard.js", ["143", "144"], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\Header.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\Sidebar.js", ["145", "146"], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\ResponsiveCheckboxList.js", ["147"], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\ChartContainer.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\VideoDefectDetection.js", ["148", "149", "150", "151", "152", "153", "154", "155", "156"], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\DefectMap.js", ["157", "158", "159"], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\hooks\\useResponsive.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\UserManagement\\FieldOfficers.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\UserManagement\\Supervisors.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\UserManagement\\Clients.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\UserManagement\\Routes.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\UserManagement\\Projects.js", [], [], {"ruleId": "160", "severity": 1, "message": "161", "line": 3, "column": 10, "nodeType": "162", "messageId": "163", "endLine": 3, "endColumn": 19}, {"ruleId": "160", "severity": 1, "message": "164", "line": 3, "column": 21, "nodeType": "162", "messageId": "163", "endLine": 3, "endColumn": 24}, {"ruleId": "160", "severity": 1, "message": "165", "line": 3, "column": 26, "nodeType": "162", "messageId": "163", "endLine": 3, "endColumn": 29}, {"ruleId": "166", "severity": 1, "message": "167", "line": 52, "column": 29, "nodeType": "168", "messageId": "169", "endLine": 52, "endColumn": 78}, {"ruleId": "160", "severity": 1, "message": "170", "line": 4, "column": 10, "nodeType": "162", "messageId": "163", "endLine": 4, "endColumn": 22}, {"ruleId": "160", "severity": 1, "message": "171", "line": 4, "column": 24, "nodeType": "162", "messageId": "163", "endLine": 4, "endColumn": 33}, {"ruleId": "160", "severity": 1, "message": "172", "line": 4, "column": 35, "nodeType": "162", "messageId": "163", "endLine": 4, "endColumn": 41}, {"ruleId": "160", "severity": 1, "message": "173", "line": 4, "column": 43, "nodeType": "162", "messageId": "163", "endLine": 4, "endColumn": 48}, {"ruleId": "160", "severity": 1, "message": "174", "line": 23, "column": 10, "nodeType": "162", "messageId": "163", "endLine": 23, "endColumn": 19}, {"ruleId": "160", "severity": 1, "message": "175", "line": 70, "column": 9, "nodeType": "162", "messageId": "163", "endLine": 70, "endColumn": 25}, {"ruleId": "176", "severity": 1, "message": "177", "line": 528, "column": 19, "nodeType": "178", "endLine": 532, "endColumn": 21}, {"ruleId": "160", "severity": 1, "message": "179", "line": 3, "column": 58, "nodeType": "162", "messageId": "163", "endLine": 3, "endColumn": 63}, {"ruleId": "160", "severity": 1, "message": "180", "line": 3, "column": 65, "nodeType": "162", "messageId": "163", "endLine": 3, "endColumn": 69}, {"ruleId": "160", "severity": 1, "message": "181", "line": 3, "column": 71, "nodeType": "162", "messageId": "163", "endLine": 3, "endColumn": 74}, {"ruleId": "160", "severity": 1, "message": "182", "line": 4, "column": 8, "nodeType": "162", "messageId": "163", "endLine": 4, "endColumn": 12}, {"ruleId": "160", "severity": 1, "message": "183", "line": 76, "column": 10, "nodeType": "162", "messageId": "163", "endLine": 76, "endColumn": 25}, {"ruleId": "160", "severity": 1, "message": "184", "line": 79, "column": 10, "nodeType": "162", "messageId": "163", "endLine": 79, "endColumn": 19}, {"ruleId": "160", "severity": 1, "message": "185", "line": 277, "column": 9, "nodeType": "162", "messageId": "163", "endLine": 277, "endColumn": 22}, {"ruleId": "160", "severity": 1, "message": "186", "line": 291, "column": 9, "nodeType": "162", "messageId": "163", "endLine": 291, "endColumn": 21}, {"ruleId": "160", "severity": 1, "message": "187", "line": 305, "column": 9, "nodeType": "162", "messageId": "163", "endLine": 305, "endColumn": 30}, {"ruleId": "160", "severity": 1, "message": "188", "line": 318, "column": 9, "nodeType": "162", "messageId": "163", "endLine": 318, "endColumn": 28}, {"ruleId": "160", "severity": 1, "message": "189", "line": 18, "column": 10, "nodeType": "162", "messageId": "163", "endLine": 18, "endColumn": 24}, {"ruleId": "160", "severity": 1, "message": "190", "line": 19, "column": 10, "nodeType": "162", "messageId": "163", "endLine": 19, "endColumn": 17}, {"ruleId": "160", "severity": 1, "message": "191", "line": 38, "column": 10, "nodeType": "162", "messageId": "163", "endLine": 38, "endColumn": 33}, {"ruleId": "160", "severity": 1, "message": "192", "line": 39, "column": 10, "nodeType": "162", "messageId": "163", "endLine": 39, "endColumn": 29}, {"ruleId": "193", "severity": 1, "message": "194", "line": 866, "column": 6, "nodeType": "195", "endLine": 866, "endColumn": 33, "suggestions": "196"}, {"ruleId": "193", "severity": 1, "message": "197", "line": 875, "column": 6, "nodeType": "195", "endLine": 875, "endColumn": 20, "suggestions": "198"}, {"ruleId": "193", "severity": 1, "message": "199", "line": 912, "column": 6, "nodeType": "195", "endLine": 912, "endColumn": 33, "suggestions": "200"}, {"ruleId": "176", "severity": 1, "message": "177", "line": 958, "column": 21, "nodeType": "178", "endLine": 962, "endColumn": 23}, {"ruleId": "176", "severity": 1, "message": "177", "line": 2061, "column": 17, "nodeType": "178", "endLine": 2071, "endColumn": 19}, {"ruleId": "176", "severity": 1, "message": "177", "line": 2079, "column": 19, "nodeType": "178", "endLine": 2089, "endColumn": 21}, {"ruleId": "160", "severity": 1, "message": "182", "line": 4, "column": 8, "nodeType": "162", "messageId": "163", "endLine": 4, "endColumn": 12}, {"ruleId": "193", "severity": 1, "message": "201", "line": 193, "column": 6, "nodeType": "195", "endLine": 193, "endColumn": 8, "suggestions": "202"}, {"ruleId": "160", "severity": 1, "message": "203", "line": 26, "column": 10, "nodeType": "162", "messageId": "163", "endLine": 26, "endColumn": 20}, {"ruleId": "204", "severity": 1, "message": "205", "line": 114, "column": 11, "nodeType": "178", "endLine": 114, "endColumn": 15}, {"ruleId": "160", "severity": 1, "message": "206", "line": 35, "column": 11, "nodeType": "162", "messageId": "163", "endLine": 35, "endColumn": 16}, {"ruleId": "160", "severity": 1, "message": "207", "line": 12, "column": 10, "nodeType": "162", "messageId": "163", "endLine": 12, "endColumn": 24}, {"ruleId": "160", "severity": 1, "message": "208", "line": 16, "column": 10, "nodeType": "162", "messageId": "163", "endLine": 16, "endColumn": 20}, {"ruleId": "160", "severity": 1, "message": "209", "line": 23, "column": 10, "nodeType": "162", "messageId": "163", "endLine": 23, "endColumn": 24}, {"ruleId": "160", "severity": 1, "message": "210", "line": 36, "column": 10, "nodeType": "162", "messageId": "163", "endLine": 36, "endColumn": 27}, {"ruleId": "160", "severity": 1, "message": "211", "line": 46, "column": 9, "nodeType": "162", "messageId": "163", "endLine": 46, "endColumn": 20}, {"ruleId": "193", "severity": 1, "message": "212", "line": 103, "column": 6, "nodeType": "195", "endLine": 103, "endColumn": 19, "suggestions": "213"}, {"ruleId": "160", "severity": 1, "message": "214", "line": 544, "column": 9, "nodeType": "162", "messageId": "163", "endLine": 544, "endColumn": 24}, {"ruleId": "160", "severity": 1, "message": "215", "line": 545, "column": 9, "nodeType": "162", "messageId": "163", "endLine": 545, "endColumn": 21}, {"ruleId": "160", "severity": 1, "message": "216", "line": 546, "column": 9, "nodeType": "162", "messageId": "163", "endLine": 546, "endColumn": 22}, {"ruleId": "160", "severity": 1, "message": "217", "line": 42, "column": 18, "nodeType": "162", "messageId": "163", "endLine": 42, "endColumn": 27}, {"ruleId": "160", "severity": 1, "message": "218", "line": 43, "column": 16, "nodeType": "162", "messageId": "163", "endLine": 43, "endColumn": 23}, {"ruleId": "193", "severity": 1, "message": "219", "line": 141, "column": 6, "nodeType": "195", "endLine": 141, "endColumn": 12, "suggestions": "220"}, "no-unused-vars", "'Container' is defined but never used.", "Identifier", "unusedVar", "'Row' is defined but never used.", "'Col' is defined but never used.", "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'retryCount'.", "ArrowFunctionExpression", "unsafeRefs", "'MapContainer' is defined but never used.", "'TileLayer' is defined but never used.", "'Marker' is defined but never used.", "'Popup' is defined but never used.", "'imageFile' is assigned a value but never used.", "'roadInfraClasses' is assigned a value but never used.", "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", "'Badge' is defined but never used.", "'Tabs' is defined but never used.", "'Tab' is defined but never used.", "'Plot' is defined but never used.", "'recommendations' is assigned a value but never used.", "'chartData' is assigned a value but never used.", "'handleApprove' is assigned a value but never used.", "'handleReject' is assigned a value but never used.", "'getPriorityBadgeClass' is assigned a value but never used.", "'getStatusBadgeClass' is assigned a value but never used.", "'processedImage' is assigned a value but never used.", "'results' is assigned a value but never used.", "'showClassificationModal' is assigned a value but never used.", "'classificationError' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'batchResults.length'. Either include it or remove the dependency array.", "ArrayExpression", ["221"], "React Hook useEffect has missing dependencies: 'handleLocationRequest' and 'locationPermission'. Either include them or remove the dependency array.", ["222"], "React Hook useEffect has a missing dependency: 'handleLocationRequest'. Either include it or remove the dependency array.", ["223"], "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["224"], "'activePage' is assigned a value but never used.", "jsx-a11y/heading-has-content", "Headings must have content and the content must be accessible by a screen reader.", "'value' is assigned a value but never used.", "'processedVideo' is assigned a value but never used.", "'shouldStop' is assigned a value but never used.", "'recordedChunks' is assigned a value but never used.", "'currentDetections' is assigned a value but never used.", "'BUFFER_SIZE' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'handleStopRecording'. Either include it or remove the dependency array.", ["225"], "'handlePlayPause' is assigned a value but never used.", "'handleRewind' is assigned a value but never used.", "'handleForward' is assigned a value but never used.", "'setCenter' is assigned a value but never used.", "'setZoom' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchDefectData' and 'fetchUsers'. Either include them or remove the dependency array.", ["226"], {"desc": "227", "fix": "228"}, {"desc": "229", "fix": "230"}, {"desc": "231", "fix": "232"}, {"desc": "233", "fix": "234"}, {"desc": "235", "fix": "236"}, {"desc": "237", "fix": "238"}, "Update the dependencies array to be: [batchResults.length, roadClassificationEnabled]", {"range": "239", "text": "240"}, "Update the dependencies array to be: [cameraActive, handleLocationRequest, locationPermission]", {"range": "241", "text": "242"}, "Update the dependencies array to be: [cameraActive, coordinates, handleLocationRequest]", {"range": "243", "text": "244"}, "Update the dependencies array to be: [fetchData]", {"range": "245", "text": "246"}, "Update the dependencies array to be: [handleStopRecording, isRecording]", {"range": "247", "text": "248"}, "Update the dependencies array to be: [fetchDefectData, fetchUsers, user]", {"range": "249", "text": "250"}, [29440, 29467], "[batchResults.length, roadClassificationEnabled]", [29694, 29708], "[cameraActive, handleLocationRequest, locationPermission]", [30878, 30905], "[cameraActive, coordinates, handleLocationRequest]", [6950, 6952], "[fetchData]", [3893, 3906], "[handleStopRecording, isRecording]", [5311, 5317], "[fetchDefectData, fetchUsers, user]"]