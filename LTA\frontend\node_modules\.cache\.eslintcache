[{"C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\App.js": "3", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\Login.js": "4", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\Home.js": "5", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\RoadInfrastructure.js": "6", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\Recommendation.js": "7", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\Pavement.js": "8", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\DefectDetail.js": "9", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\Dashboard.js": "10", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\Header.js": "11", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\Sidebar.js": "12", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\ResponsiveCheckboxList.js": "13", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\ChartContainer.js": "14", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\VideoDefectDetection.js": "15", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\DefectMap.js": "16", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\hooks\\useResponsive.js": "17", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\UserManagement\\ClientManagement.js": "18", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\UserManagement\\HierarchyView.js": "19", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\UserManagement\\RouteManagement.js": "20"}, {"size": 633, "mtime": 1753938470359, "results": "21", "hashOfConfig": "22"}, {"size": 376, "mtime": 1753938470372, "results": "23", "hashOfConfig": "22"}, {"size": 3916, "mtime": 1754564694717, "results": "24", "hashOfConfig": "22"}, {"size": 4500, "mtime": 1753963770147, "results": "25", "hashOfConfig": "22"}, {"size": 2994, "mtime": 1753938470363, "results": "26", "hashOfConfig": "22"}, {"size": 37851, "mtime": 1753938470369, "results": "27", "hashOfConfig": "22"}, {"size": 27373, "mtime": 1753938470368, "results": "28", "hashOfConfig": "22"}, {"size": 89420, "mtime": 1754565974357, "results": "29", "hashOfConfig": "22"}, {"size": 15619, "mtime": 1753938470361, "results": "30", "hashOfConfig": "22"}, {"size": 43573, "mtime": 1754128768623, "results": "31", "hashOfConfig": "22"}, {"size": 659, "mtime": 1753938470351, "results": "32", "hashOfConfig": "22"}, {"size": 7009, "mtime": 1754564428151, "results": "33", "hashOfConfig": "22"}, {"size": 4005, "mtime": 1753938470352, "results": "34", "hashOfConfig": "22"}, {"size": 3414, "mtime": 1753938470350, "results": "35", "hashOfConfig": "22"}, {"size": 37926, "mtime": 1753938470356, "results": "36", "hashOfConfig": "22"}, {"size": 14715, "mtime": 1753938470350, "results": "37", "hashOfConfig": "22"}, {"size": 1018, "mtime": 1753938470357, "results": "38", "hashOfConfig": "22"}, {"size": 15179, "mtime": 1754564499693, "results": "39", "hashOfConfig": "22"}, {"size": 9230, "mtime": 1754564560045, "results": "40", "hashOfConfig": "22"}, {"size": 22034, "mtime": 1754564673650, "results": "41", "hashOfConfig": "22"}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1djdb2s", {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\Login.js", ["102", "103", "104", "105"], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\Home.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\RoadInfrastructure.js", ["106", "107", "108", "109", "110", "111", "112"], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\Recommendation.js", ["113", "114", "115", "116", "117", "118", "119", "120", "121", "122"], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\Pavement.js", ["123", "124", "125", "126", "127", "128", "129", "130", "131", "132"], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\DefectDetail.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\Dashboard.js", ["133", "134"], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\Header.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\Sidebar.js", ["135", "136"], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\ResponsiveCheckboxList.js", ["137"], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\ChartContainer.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\VideoDefectDetection.js", ["138", "139", "140", "141", "142", "143", "144", "145", "146"], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\DefectMap.js", ["147", "148", "149"], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\hooks\\useResponsive.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\UserManagement\\ClientManagement.js", ["150"], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\UserManagement\\HierarchyView.js", ["151"], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\UserManagement\\RouteManagement.js", ["152", "153", "154"], [], {"ruleId": "155", "severity": 1, "message": "156", "line": 3, "column": 10, "nodeType": "157", "messageId": "158", "endLine": 3, "endColumn": 19}, {"ruleId": "155", "severity": 1, "message": "159", "line": 3, "column": 21, "nodeType": "157", "messageId": "158", "endLine": 3, "endColumn": 24}, {"ruleId": "155", "severity": 1, "message": "160", "line": 3, "column": 26, "nodeType": "157", "messageId": "158", "endLine": 3, "endColumn": 29}, {"ruleId": "161", "severity": 1, "message": "162", "line": 52, "column": 29, "nodeType": "163", "messageId": "164", "endLine": 52, "endColumn": 78}, {"ruleId": "155", "severity": 1, "message": "165", "line": 4, "column": 10, "nodeType": "157", "messageId": "158", "endLine": 4, "endColumn": 22}, {"ruleId": "155", "severity": 1, "message": "166", "line": 4, "column": 24, "nodeType": "157", "messageId": "158", "endLine": 4, "endColumn": 33}, {"ruleId": "155", "severity": 1, "message": "167", "line": 4, "column": 35, "nodeType": "157", "messageId": "158", "endLine": 4, "endColumn": 41}, {"ruleId": "155", "severity": 1, "message": "168", "line": 4, "column": 43, "nodeType": "157", "messageId": "158", "endLine": 4, "endColumn": 48}, {"ruleId": "155", "severity": 1, "message": "169", "line": 23, "column": 10, "nodeType": "157", "messageId": "158", "endLine": 23, "endColumn": 19}, {"ruleId": "155", "severity": 1, "message": "170", "line": 70, "column": 9, "nodeType": "157", "messageId": "158", "endLine": 70, "endColumn": 25}, {"ruleId": "171", "severity": 1, "message": "172", "line": 528, "column": 19, "nodeType": "173", "endLine": 532, "endColumn": 21}, {"ruleId": "155", "severity": 1, "message": "174", "line": 3, "column": 58, "nodeType": "157", "messageId": "158", "endLine": 3, "endColumn": 63}, {"ruleId": "155", "severity": 1, "message": "175", "line": 3, "column": 65, "nodeType": "157", "messageId": "158", "endLine": 3, "endColumn": 69}, {"ruleId": "155", "severity": 1, "message": "176", "line": 3, "column": 71, "nodeType": "157", "messageId": "158", "endLine": 3, "endColumn": 74}, {"ruleId": "155", "severity": 1, "message": "177", "line": 4, "column": 8, "nodeType": "157", "messageId": "158", "endLine": 4, "endColumn": 12}, {"ruleId": "155", "severity": 1, "message": "178", "line": 76, "column": 10, "nodeType": "157", "messageId": "158", "endLine": 76, "endColumn": 25}, {"ruleId": "155", "severity": 1, "message": "179", "line": 79, "column": 10, "nodeType": "157", "messageId": "158", "endLine": 79, "endColumn": 19}, {"ruleId": "155", "severity": 1, "message": "180", "line": 277, "column": 9, "nodeType": "157", "messageId": "158", "endLine": 277, "endColumn": 22}, {"ruleId": "155", "severity": 1, "message": "181", "line": 291, "column": 9, "nodeType": "157", "messageId": "158", "endLine": 291, "endColumn": 21}, {"ruleId": "155", "severity": 1, "message": "182", "line": 305, "column": 9, "nodeType": "157", "messageId": "158", "endLine": 305, "endColumn": 30}, {"ruleId": "155", "severity": 1, "message": "183", "line": 318, "column": 9, "nodeType": "157", "messageId": "158", "endLine": 318, "endColumn": 28}, {"ruleId": "155", "severity": 1, "message": "184", "line": 18, "column": 10, "nodeType": "157", "messageId": "158", "endLine": 18, "endColumn": 24}, {"ruleId": "155", "severity": 1, "message": "185", "line": 19, "column": 10, "nodeType": "157", "messageId": "158", "endLine": 19, "endColumn": 17}, {"ruleId": "155", "severity": 1, "message": "186", "line": 38, "column": 10, "nodeType": "157", "messageId": "158", "endLine": 38, "endColumn": 33}, {"ruleId": "155", "severity": 1, "message": "187", "line": 39, "column": 10, "nodeType": "157", "messageId": "158", "endLine": 39, "endColumn": 29}, {"ruleId": "188", "severity": 1, "message": "189", "line": 866, "column": 6, "nodeType": "190", "endLine": 866, "endColumn": 33, "suggestions": "191"}, {"ruleId": "188", "severity": 1, "message": "192", "line": 875, "column": 6, "nodeType": "190", "endLine": 875, "endColumn": 20, "suggestions": "193"}, {"ruleId": "188", "severity": 1, "message": "194", "line": 912, "column": 6, "nodeType": "190", "endLine": 912, "endColumn": 33, "suggestions": "195"}, {"ruleId": "171", "severity": 1, "message": "172", "line": 958, "column": 21, "nodeType": "173", "endLine": 962, "endColumn": 23}, {"ruleId": "171", "severity": 1, "message": "172", "line": 2061, "column": 17, "nodeType": "173", "endLine": 2071, "endColumn": 19}, {"ruleId": "171", "severity": 1, "message": "172", "line": 2079, "column": 19, "nodeType": "173", "endLine": 2089, "endColumn": 21}, {"ruleId": "155", "severity": 1, "message": "177", "line": 4, "column": 8, "nodeType": "157", "messageId": "158", "endLine": 4, "endColumn": 12}, {"ruleId": "188", "severity": 1, "message": "196", "line": 193, "column": 6, "nodeType": "190", "endLine": 193, "endColumn": 8, "suggestions": "197"}, {"ruleId": "155", "severity": 1, "message": "198", "line": 27, "column": 10, "nodeType": "157", "messageId": "158", "endLine": 27, "endColumn": 20}, {"ruleId": "199", "severity": 1, "message": "200", "line": 116, "column": 11, "nodeType": "173", "endLine": 116, "endColumn": 15}, {"ruleId": "155", "severity": 1, "message": "201", "line": 35, "column": 11, "nodeType": "157", "messageId": "158", "endLine": 35, "endColumn": 16}, {"ruleId": "155", "severity": 1, "message": "202", "line": 12, "column": 10, "nodeType": "157", "messageId": "158", "endLine": 12, "endColumn": 24}, {"ruleId": "155", "severity": 1, "message": "203", "line": 16, "column": 10, "nodeType": "157", "messageId": "158", "endLine": 16, "endColumn": 20}, {"ruleId": "155", "severity": 1, "message": "204", "line": 23, "column": 10, "nodeType": "157", "messageId": "158", "endLine": 23, "endColumn": 24}, {"ruleId": "155", "severity": 1, "message": "205", "line": 36, "column": 10, "nodeType": "157", "messageId": "158", "endLine": 36, "endColumn": 27}, {"ruleId": "155", "severity": 1, "message": "206", "line": 46, "column": 9, "nodeType": "157", "messageId": "158", "endLine": 46, "endColumn": 20}, {"ruleId": "188", "severity": 1, "message": "207", "line": 103, "column": 6, "nodeType": "190", "endLine": 103, "endColumn": 19, "suggestions": "208"}, {"ruleId": "155", "severity": 1, "message": "209", "line": 544, "column": 9, "nodeType": "157", "messageId": "158", "endLine": 544, "endColumn": 24}, {"ruleId": "155", "severity": 1, "message": "210", "line": 545, "column": 9, "nodeType": "157", "messageId": "158", "endLine": 545, "endColumn": 21}, {"ruleId": "155", "severity": 1, "message": "211", "line": 546, "column": 9, "nodeType": "157", "messageId": "158", "endLine": 546, "endColumn": 22}, {"ruleId": "155", "severity": 1, "message": "212", "line": 42, "column": 18, "nodeType": "157", "messageId": "158", "endLine": 42, "endColumn": 27}, {"ruleId": "155", "severity": 1, "message": "213", "line": 43, "column": 16, "nodeType": "157", "messageId": "158", "endLine": 43, "endColumn": 23}, {"ruleId": "188", "severity": 1, "message": "214", "line": 141, "column": 6, "nodeType": "190", "endLine": 141, "endColumn": 12, "suggestions": "215"}, {"ruleId": "188", "severity": 1, "message": "216", "line": 26, "column": 6, "nodeType": "190", "endLine": 26, "endColumn": 8, "suggestions": "217"}, {"ruleId": "188", "severity": 1, "message": "218", "line": 13, "column": 6, "nodeType": "190", "endLine": 13, "endColumn": 8, "suggestions": "219"}, {"ruleId": "155", "severity": 1, "message": "220", "line": 2, "column": 88, "nodeType": "157", "messageId": "158", "endLine": 2, "endColumn": 96}, {"ruleId": "188", "severity": 1, "message": "196", "line": 35, "column": 6, "nodeType": "190", "endLine": 35, "endColumn": 8, "suggestions": "221"}, {"ruleId": "155", "severity": 1, "message": "222", "line": 161, "column": 26, "nodeType": "157", "messageId": "158", "endLine": 161, "endColumn": 30}, "no-unused-vars", "'Container' is defined but never used.", "Identifier", "unusedVar", "'Row' is defined but never used.", "'Col' is defined but never used.", "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'retryCount'.", "ArrowFunctionExpression", "unsafeRefs", "'MapContainer' is defined but never used.", "'TileLayer' is defined but never used.", "'Marker' is defined but never used.", "'Popup' is defined but never used.", "'imageFile' is assigned a value but never used.", "'roadInfraClasses' is assigned a value but never used.", "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", "'Badge' is defined but never used.", "'Tabs' is defined but never used.", "'Tab' is defined but never used.", "'Plot' is defined but never used.", "'recommendations' is assigned a value but never used.", "'chartData' is assigned a value but never used.", "'handleApprove' is assigned a value but never used.", "'handleReject' is assigned a value but never used.", "'getPriorityBadgeClass' is assigned a value but never used.", "'getStatusBadgeClass' is assigned a value but never used.", "'processedImage' is assigned a value but never used.", "'results' is assigned a value but never used.", "'showClassificationModal' is assigned a value but never used.", "'classificationError' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'batchResults.length'. Either include it or remove the dependency array.", "ArrayExpression", ["223"], "React Hook useEffect has missing dependencies: 'handleLocationRequest' and 'locationPermission'. Either include them or remove the dependency array.", ["224"], "React Hook useEffect has a missing dependency: 'handleLocationRequest'. Either include it or remove the dependency array.", ["225"], "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["226"], "'activePage' is assigned a value but never used.", "jsx-a11y/heading-has-content", "Headings must have content and the content must be accessible by a screen reader.", "'value' is assigned a value but never used.", "'processedVideo' is assigned a value but never used.", "'shouldStop' is assigned a value but never used.", "'recordedChunks' is assigned a value but never used.", "'currentDetections' is assigned a value but never used.", "'BUFFER_SIZE' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'handleStopRecording'. Either include it or remove the dependency array.", ["227"], "'handlePlayPause' is assigned a value but never used.", "'handleRewind' is assigned a value but never used.", "'handleForward' is assigned a value but never used.", "'setCenter' is assigned a value but never used.", "'setZoom' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchDefectData' and 'fetchUsers'. Either include them or remove the dependency array.", ["228"], "React Hook useEffect has a missing dependency: 'fetchClients'. Either include it or remove the dependency array.", ["229"], "React Hook useEffect has a missing dependency: 'fetchHierarchy'. Either include it or remove the dependency array.", ["230"], "'Dropdown' is defined but never used.", ["231"], "'type' is assigned a value but never used.", {"desc": "232", "fix": "233"}, {"desc": "234", "fix": "235"}, {"desc": "236", "fix": "237"}, {"desc": "238", "fix": "239"}, {"desc": "240", "fix": "241"}, {"desc": "242", "fix": "243"}, {"desc": "244", "fix": "245"}, {"desc": "246", "fix": "247"}, {"desc": "238", "fix": "248"}, "Update the dependencies array to be: [batchResults.length, roadClassificationEnabled]", {"range": "249", "text": "250"}, "Update the dependencies array to be: [cameraActive, handleLocationRequest, locationPermission]", {"range": "251", "text": "252"}, "Update the dependencies array to be: [cameraActive, coordinates, handleLocationRequest]", {"range": "253", "text": "254"}, "Update the dependencies array to be: [fetchData]", {"range": "255", "text": "256"}, "Update the dependencies array to be: [handleStopRecording, isRecording]", {"range": "257", "text": "258"}, "Update the dependencies array to be: [fetchDefectData, fetchUsers, user]", {"range": "259", "text": "260"}, "Update the dependencies array to be: [fetchClients]", {"range": "261", "text": "262"}, "Update the dependencies array to be: [fetchHierarchy]", {"range": "263", "text": "264"}, {"range": "265", "text": "256"}, [29440, 29467], "[batchResults.length, roadClassificationEnabled]", [29694, 29708], "[cameraActive, handleLocationRequest, locationPermission]", [30878, 30905], "[cameraActive, coordinates, handleLocationRequest]", [6950, 6952], "[fetchData]", [3893, 3906], "[handleStopRecording, isRecording]", [5311, 5317], "[fetchDefectData, fetchUsers, user]", [996, 998], "[fetchClients]", [526, 528], "[fetchHierarchy]", [1323, 1325]]