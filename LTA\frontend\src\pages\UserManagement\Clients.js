import React, { useState, useEffect } from 'react';
import axios from 'axios';
import './UserManagement.css';

const Clients = () => {
  const [clients, setClients] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchClients();
  }, []);

  const fetchClients = async () => {
    try {
      setLoading(true);
      // TODO: Replace with actual API endpoint
      const response = await axios.get('/api/user-management/clients');
      setClients(response.data.clients || []);
    } catch (err) {
      setError('Failed to fetch clients');
      console.error('Error fetching clients:', err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="user-management-container">
        <div className="loading">Loading clients...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="user-management-container">
        <div className="error">{error}</div>
      </div>
    );
  }

  return (
    <div className="user-management-container">
      <div className="user-management-header">
        <h1>Client Management</h1>
        <button className="btn-primary">Add New Client</button>
      </div>
      
      <div className="user-management-content">
        <div className="hierarchy-view">
          <h3>Client Hierarchy</h3>
          <div className="hierarchy-tree">
            {clients.length === 0 ? (
              <div className="no-data">No clients found</div>
            ) : (
              clients.map((client) => (
                <div key={client.id} className="client-node">
                  <div className="client-info">
                    <span className="client-name">{client.name}</span>
                    <span className="client-role">Client</span>
                  </div>
                  <div className="client-projects">
                    {client.projects && client.projects.map((project) => (
                      <div key={project.id} className="project-node">
                        <span className="project-name">{project.name}</span>
                      </div>
                    ))}
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
        
        <div className="management-actions">
          <h3>Quick Actions</h3>
          <div className="action-buttons">
            <button className="btn-secondary">Assign Projects</button>
            <button className="btn-secondary">Manage Supervisors</button>
            <button className="btn-secondary">View Reports</button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Clients;
