{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Deep Learning\\\\LTAGIT\\\\LTA\\\\frontend\\\\src\\\\pages\\\\UserManagement\\\\Clients.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport './UserManagement.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Clients = () => {\n  _s();\n  const [clients, setClients] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    fetchClients();\n  }, []);\n  const fetchClients = async () => {\n    try {\n      setLoading(true);\n      // TODO: Replace with actual API endpoint\n      const response = await axios.get('/api/user-management/clients');\n      setClients(response.data.clients || []);\n    } catch (err) {\n      setError('Failed to fetch clients');\n      console.error('Error fetching clients:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"user-management-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading\",\n        children: \"Loading clients...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"user-management-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"user-management-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"user-management-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Client Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn-primary\",\n        children: \"Add New Client\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"user-management-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hierarchy-view\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Client Hierarchy\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hierarchy-tree\",\n          children: clients.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"no-data\",\n            children: \"No clients found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 15\n          }, this) : clients.map(client => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"client-node\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"client-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"client-name\",\n                children: client.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 61,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"client-role\",\n                children: \"Client\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 62,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"client-projects\",\n              children: client.projects && client.projects.map(project => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"project-node\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"project-name\",\n                  children: project.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 67,\n                  columnNumber: 25\n                }, this)\n              }, project.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 66,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 19\n            }, this)]\n          }, client.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"management-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Quick Actions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"action-buttons\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-secondary\",\n            children: \"Assign Projects\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-secondary\",\n            children: \"Manage Supervisors\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-secondary\",\n            children: \"View Reports\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 45,\n    columnNumber: 5\n  }, this);\n};\n_s(Clients, \"zQV1hJCcmbjAPczvHNExEVBWQBA=\");\n_c = Clients;\nexport default Clients;\nvar _c;\n$RefreshReg$(_c, \"Clients\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "jsxDEV", "_jsxDEV", "Clients", "_s", "clients", "setClients", "loading", "setLoading", "error", "setError", "fetchClients", "response", "get", "data", "err", "console", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "map", "client", "name", "projects", "project", "id", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Deep Learning/LTAGIT/LTA/frontend/src/pages/UserManagement/Clients.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport './UserManagement.css';\n\nconst Clients = () => {\n  const [clients, setClients] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  useEffect(() => {\n    fetchClients();\n  }, []);\n\n  const fetchClients = async () => {\n    try {\n      setLoading(true);\n      // TODO: Replace with actual API endpoint\n      const response = await axios.get('/api/user-management/clients');\n      setClients(response.data.clients || []);\n    } catch (err) {\n      setError('Failed to fetch clients');\n      console.error('Error fetching clients:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"user-management-container\">\n        <div className=\"loading\">Loading clients...</div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"user-management-container\">\n        <div className=\"error\">{error}</div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"user-management-container\">\n      <div className=\"user-management-header\">\n        <h1>Client Management</h1>\n        <button className=\"btn-primary\">Add New Client</button>\n      </div>\n      \n      <div className=\"user-management-content\">\n        <div className=\"hierarchy-view\">\n          <h3>Client Hierarchy</h3>\n          <div className=\"hierarchy-tree\">\n            {clients.length === 0 ? (\n              <div className=\"no-data\">No clients found</div>\n            ) : (\n              clients.map((client) => (\n                <div key={client.id} className=\"client-node\">\n                  <div className=\"client-info\">\n                    <span className=\"client-name\">{client.name}</span>\n                    <span className=\"client-role\">Client</span>\n                  </div>\n                  <div className=\"client-projects\">\n                    {client.projects && client.projects.map((project) => (\n                      <div key={project.id} className=\"project-node\">\n                        <span className=\"project-name\">{project.name}</span>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              ))\n            )}\n          </div>\n        </div>\n        \n        <div className=\"management-actions\">\n          <h3>Quick Actions</h3>\n          <div className=\"action-buttons\">\n            <button className=\"btn-secondary\">Assign Projects</button>\n            <button className=\"btn-secondary\">Manage Supervisors</button>\n            <button className=\"btn-secondary\">View Reports</button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Clients;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACS,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACW,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAExCC,SAAS,CAAC,MAAM;IACdY,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFH,UAAU,CAAC,IAAI,CAAC;MAChB;MACA,MAAMI,QAAQ,GAAG,MAAMZ,KAAK,CAACa,GAAG,CAAC,8BAA8B,CAAC;MAChEP,UAAU,CAACM,QAAQ,CAACE,IAAI,CAACT,OAAO,IAAI,EAAE,CAAC;IACzC,CAAC,CAAC,OAAOU,GAAG,EAAE;MACZL,QAAQ,CAAC,yBAAyB,CAAC;MACnCM,OAAO,CAACP,KAAK,CAAC,yBAAyB,EAAEM,GAAG,CAAC;IAC/C,CAAC,SAAS;MACRP,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,IAAID,OAAO,EAAE;IACX,oBACEL,OAAA;MAAKe,SAAS,EAAC,2BAA2B;MAAAC,QAAA,eACxChB,OAAA;QAAKe,SAAS,EAAC,SAAS;QAAAC,QAAA,EAAC;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9C,CAAC;EAEV;EAEA,IAAIb,KAAK,EAAE;IACT,oBACEP,OAAA;MAAKe,SAAS,EAAC,2BAA2B;MAAAC,QAAA,eACxChB,OAAA;QAAKe,SAAS,EAAC,OAAO;QAAAC,QAAA,EAAET;MAAK;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC,CAAC;EAEV;EAEA,oBACEpB,OAAA;IAAKe,SAAS,EAAC,2BAA2B;IAAAC,QAAA,gBACxChB,OAAA;MAAKe,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBACrChB,OAAA;QAAAgB,QAAA,EAAI;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1BpB,OAAA;QAAQe,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpD,CAAC,eAENpB,OAAA;MAAKe,SAAS,EAAC,yBAAyB;MAAAC,QAAA,gBACtChB,OAAA;QAAKe,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BhB,OAAA;UAAAgB,QAAA,EAAI;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzBpB,OAAA;UAAKe,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAC5Bb,OAAO,CAACkB,MAAM,KAAK,CAAC,gBACnBrB,OAAA;YAAKe,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,GAE/CjB,OAAO,CAACmB,GAAG,CAAEC,MAAM,iBACjBvB,OAAA;YAAqBe,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1ChB,OAAA;cAAKe,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BhB,OAAA;gBAAMe,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAEO,MAAM,CAACC;cAAI;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClDpB,OAAA;gBAAMe,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eACNpB,OAAA;cAAKe,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAC7BO,MAAM,CAACE,QAAQ,IAAIF,MAAM,CAACE,QAAQ,CAACH,GAAG,CAAEI,OAAO,iBAC9C1B,OAAA;gBAAsBe,SAAS,EAAC,cAAc;gBAAAC,QAAA,eAC5ChB,OAAA;kBAAMe,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAEU,OAAO,CAACF;gBAAI;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC,GAD5CM,OAAO,CAACC,EAAE;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEf,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA,GAXEG,MAAM,CAACI,EAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAYd,CACN;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpB,OAAA;QAAKe,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjChB,OAAA;UAAAgB,QAAA,EAAI;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtBpB,OAAA;UAAKe,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BhB,OAAA;YAAQe,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1DpB,OAAA;YAAQe,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC7DpB,OAAA;YAAQe,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClB,EAAA,CAnFID,OAAO;AAAA2B,EAAA,GAAP3B,OAAO;AAqFb,eAAeA,OAAO;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}