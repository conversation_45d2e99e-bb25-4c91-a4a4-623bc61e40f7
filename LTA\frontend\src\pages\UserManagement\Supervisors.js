import React, { useState, useEffect } from 'react';
import axios from 'axios';
import './UserManagement.css';

const Supervisors = () => {
  const [supervisors, setSupervisors] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchSupervisors();
  }, []);

  const fetchSupervisors = async () => {
    try {
      setLoading(true);
      // TODO: Replace with actual API endpoint
      const response = await axios.get('/api/user-management/supervisors');
      setSupervisors(response.data.supervisors || []);
    } catch (err) {
      setError('Failed to fetch supervisors');
      console.error('Error fetching supervisors:', err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="user-management-container">
        <div className="loading">Loading supervisors...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="user-management-container">
        <div className="error">{error}</div>
      </div>
    );
  }

  return (
    <div className="user-management-container">
      <div className="user-management-header">
        <h1>Supervisor Management</h1>
        <button className="btn-primary">Add New Supervisor</button>
      </div>
      
      <div className="user-management-content">
        <div className="hierarchy-view">
          <h3>Supervisor Hierarchy</h3>
          <div className="hierarchy-tree">
            {supervisors.length === 0 ? (
              <div className="no-data">No supervisors found</div>
            ) : (
              supervisors.map((supervisor) => (
                <div key={supervisor.id} className="supervisor-node">
                  <div className="supervisor-info">
                    <span className="supervisor-name">{supervisor.name}</span>
                    <span className="supervisor-role">Supervisor</span>
                  </div>
                  <div className="supervisor-field-officers">
                    {supervisor.fieldOfficers && supervisor.fieldOfficers.map((fo) => (
                      <div key={fo.id} className="field-officer-node">
                        <span className="field-officer-name">{fo.name}</span>
                      </div>
                    ))}
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
        
        <div className="management-actions">
          <h3>Quick Actions</h3>
          <div className="action-buttons">
            <button className="btn-secondary">Assign Field Officers</button>
            <button className="btn-secondary">View Assigned Routes</button>
            <button className="btn-secondary">Generate Reports</button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Supervisors;
