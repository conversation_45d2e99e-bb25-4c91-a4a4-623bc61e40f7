import React, { useState, useEffect } from 'react';
import { NavLink, useNavigate } from 'react-router-dom';
import {
  FaHome,
  FaMap,
  FaColumns,
  FaLightbulb,
  FaChartBar,
  FaSignOutAlt,
  FaBars,
  FaTimes,
  FaUsers,
  FaChevronDown,
  FaChevronRight,
  FaUserTie,
  FaUserCog,
  FaUserShield,
  FaProjectDiagram,
  FaRoute
} from 'react-icons/fa';
import axios from 'axios';
import './Sidebar.css';

const Sidebar = ({ onLogout }) => {
  const navigate = useNavigate();
  const [activePage, setActivePage] = useState(window.location.pathname);
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);
  const [isOpen, setIsOpen] = useState(!isMobile);
  const [userManagementOpen, setUserManagementOpen] = useState(false);

  // Handle window resize events
  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth <= 768;
      setIsMobile(mobile);
      if (!mobile) {
        setIsOpen(true);
      } else {
        setIsOpen(false);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const handleLogout = async () => {
    try {
      // Call logout API
      await axios.post('/api/auth/logout');
      // Call the onLogout prop function to update app state
      onLogout();
      // Redirect to login page
      navigate('/login');
    } catch (error) {
      console.error('Logout error:', error);
      // Even if there's an error, still log out on the client side
      onLogout();
      navigate('/login');
    }
  };

  const toggleSidebar = () => {
    setIsOpen(!isOpen);
  };

  const handleNavClick = () => {
    if (isMobile) {
      setIsOpen(false);
    }
  };

  const menuItems = [
    { path: '/', name: 'Home', icon: <FaHome size={20} /> },
    { path: '/pavement', name: 'Pavement', icon: <FaMap size={20} /> },
    { path: '/road-infrastructure', name: 'Infrastructure', icon: <FaColumns size={20} /> },
    { path: '/recommendation', name: 'Recommendation', icon: <FaLightbulb size={20} /> },
    { path: '/dashboard', name: 'Dashboard', icon: <FaChartBar size={20} /> }
  ];

  const userManagementItems = [
    { path: '/user-management/clients', name: 'Clients', icon: <FaUserTie size={18} /> },
    { path: '/user-management/supervisors', name: 'Supervisors', icon: <FaUserShield size={18} /> },
    { path: '/user-management/field-officers', name: 'Field Officers', icon: <FaUserCog size={18} /> },
    { path: '/user-management/projects', name: 'Projects', icon: <FaProjectDiagram size={18} /> },
    { path: '/user-management/routes', name: 'Routes', icon: <FaRoute size={18} /> }
  ];

  const toggleUserManagement = () => {
    setUserManagementOpen(!userManagementOpen);
  };

  return (
    <>
      {/* Mobile sidebar toggle button */}
      {isMobile && (
        <button 
          className="sidebar-toggle" 
          onClick={toggleSidebar}
          aria-label={isOpen ? "Close menu" : "Open menu"}
        >
          {isOpen ? <FaTimes size={24} /> : <FaBars size={24} />}
        </button>
      )}

      {/* Sidebar overlay backdrop for mobile */}
      {isMobile && isOpen && (
        <div className="sidebar-backdrop" onClick={toggleSidebar} />
      )}

      {/* Main sidebar */}
      <div className={`sidebar ${isOpen ? 'open' : 'closed'}`}>
        <div className="sidebar-header">
          <h3></h3>
          {isMobile && (
            <button 
              className="sidebar-close" 
              onClick={toggleSidebar}
              aria-label="Close menu"
            >
              <FaTimes size={20} />
            </button>
          )}
        </div>
        <div className="sidebar-menu">
          {menuItems.map((item) => (
            <NavLink
              key={item.path}
              to={item.path}
              className={({ isActive }) =>
                isActive ? 'sidebar-item active' : 'sidebar-item'
              }
              onClick={() => {
                setActivePage(item.path);
                handleNavClick();
              }}
            >
              <div className="sidebar-icon">{item.icon}</div>
              <div className="sidebar-text">{item.name}</div>
            </NavLink>
          ))}

          {/* User Management Collapsible Menu */}
          <div className="sidebar-collapsible">
            <div
              className={`sidebar-item collapsible-header ${userManagementOpen ? 'open' : ''}`}
              onClick={toggleUserManagement}
            >
              <div className="sidebar-icon"><FaUsers size={20} /></div>
              <div className="sidebar-text">User Management</div>
              <div className="sidebar-chevron">
                {userManagementOpen ? <FaChevronDown size={14} /> : <FaChevronRight size={14} />}
              </div>
            </div>

            <div className={`sidebar-submenu ${userManagementOpen ? 'open' : ''}`}>
              {userManagementItems.map((item) => (
                <NavLink
                  key={item.path}
                  to={item.path}
                  className={({ isActive }) =>
                    isActive ? 'sidebar-subitem active' : 'sidebar-subitem'
                  }
                  onClick={() => {
                    setActivePage(item.path);
                    handleNavClick();
                  }}
                >
                  <div className="sidebar-icon">{item.icon}</div>
                  <div className="sidebar-text">{item.name}</div>
                </NavLink>
              ))}
            </div>
          </div>
        </div>
        <div className="sidebar-footer">
          <button className="sidebar-logout" onClick={handleLogout}>
            <div className="sidebar-icon"><FaSignOutAlt size={20} /></div>
            <div className="sidebar-text">Logout</div>
          </button>
          <div className="sidebar-watermark">Powered by AiSPRY</div>
        </div>
      </div>

      {/* Mobile bottom navigation for quick access */}
      {isMobile && (
        <div className="mobile-nav">
          {menuItems.map((item) => (
            <NavLink
              key={item.path}
              to={item.path}
              className={({ isActive }) => 
                isActive ? 'mobile-nav-item active' : 'mobile-nav-item'
              }
              onClick={() => setActivePage(item.path)}
            >
              <div className="mobile-nav-icon">{item.icon}</div>
              <div className="mobile-nav-text">{item.name}</div>
            </NavLink>
          ))}
        </div>
      )}
    </>
  );
};

export default Sidebar; 