{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Deep Learning\\\\LTAGIT\\\\LTA\\\\frontend\\\\src\\\\components\\\\Sidebar.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { NavLink, useNavigate } from 'react-router-dom';\nimport { FaHome, FaMap, FaColumns, FaLightbulb, FaChartBar, FaSignOutAlt, FaBars, FaTimes, FaUsers, FaChevronDown, FaChevronRight, FaUserTie, FaUserCog, FaUserShield, FaProjectDiagram, FaRoute } from 'react-icons/fa';\nimport axios from 'axios';\nimport './Sidebar.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Sidebar = ({\n  onLogout\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const [activePage, setActivePage] = useState(window.location.pathname);\n  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);\n  const [isOpen, setIsOpen] = useState(!isMobile);\n  const [userManagementOpen, setUserManagementOpen] = useState(false);\n\n  // Handle window resize events\n  useEffect(() => {\n    const handleResize = () => {\n      const mobile = window.innerWidth <= 768;\n      setIsMobile(mobile);\n      if (!mobile) {\n        setIsOpen(true);\n      } else {\n        setIsOpen(false);\n      }\n    };\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n  const handleLogout = async () => {\n    try {\n      // Call logout API\n      await axios.post('/api/auth/logout');\n      // Call the onLogout prop function to update app state\n      onLogout();\n      // Redirect to login page\n      navigate('/login');\n    } catch (error) {\n      console.error('Logout error:', error);\n      // Even if there's an error, still log out on the client side\n      onLogout();\n      navigate('/login');\n    }\n  };\n  const toggleSidebar = () => {\n    setIsOpen(!isOpen);\n  };\n  const handleNavClick = () => {\n    if (isMobile) {\n      setIsOpen(false);\n    }\n  };\n  const menuItems = [{\n    path: '/',\n    name: 'Home',\n    icon: /*#__PURE__*/_jsxDEV(FaHome, {\n      size: 20\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 38\n    }, this)\n  }, {\n    path: '/pavement',\n    name: 'Pavement',\n    icon: /*#__PURE__*/_jsxDEV(FaMap, {\n      size: 20\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 50\n    }, this)\n  }, {\n    path: '/road-infrastructure',\n    name: 'Infrastructure',\n    icon: /*#__PURE__*/_jsxDEV(FaColumns, {\n      size: 20\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 67\n    }, this)\n  }, {\n    path: '/recommendation',\n    name: 'Recommendation',\n    icon: /*#__PURE__*/_jsxDEV(FaLightbulb, {\n      size: 20\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 62\n    }, this)\n  }, {\n    path: '/dashboard',\n    name: 'Dashboard',\n    icon: /*#__PURE__*/_jsxDEV(FaChartBar, {\n      size: 20\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 52\n    }, this)\n  }];\n  const userManagementItems = [{\n    path: '/user-management/clients',\n    name: 'Clients',\n    icon: /*#__PURE__*/_jsxDEV(FaUserTie, {\n      size: 18\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 64\n    }, this)\n  }, {\n    path: '/user-management/supervisors',\n    name: 'Supervisors',\n    icon: /*#__PURE__*/_jsxDEV(FaUserShield, {\n      size: 18\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 72\n    }, this)\n  }, {\n    path: '/user-management/field-officers',\n    name: 'Field Officers',\n    icon: /*#__PURE__*/_jsxDEV(FaUserCog, {\n      size: 18\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 78\n    }, this)\n  }, {\n    path: '/user-management/projects',\n    name: 'Projects',\n    icon: /*#__PURE__*/_jsxDEV(FaProjectDiagram, {\n      size: 18\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 66\n    }, this)\n  }, {\n    path: '/user-management/routes',\n    name: 'Routes',\n    icon: /*#__PURE__*/_jsxDEV(FaRoute, {\n      size: 18\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 86,\n      columnNumber: 62\n    }, this)\n  }];\n  const toggleUserManagement = () => {\n    setUserManagementOpen(!userManagementOpen);\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [isMobile && /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"sidebar-toggle\",\n      onClick: toggleSidebar,\n      \"aria-label\": isOpen ? \"Close menu\" : \"Open menu\",\n      children: isOpen ? /*#__PURE__*/_jsxDEV(FaTimes, {\n        size: 24\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 21\n      }, this) : /*#__PURE__*/_jsxDEV(FaBars, {\n        size: 24\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 45\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 9\n    }, this), isMobile && isOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sidebar-backdrop\",\n      onClick: toggleSidebar\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `sidebar ${isOpen ? 'open' : 'closed'}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sidebar-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this), isMobile && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"sidebar-close\",\n          onClick: toggleSidebar,\n          \"aria-label\": \"Close menu\",\n          children: /*#__PURE__*/_jsxDEV(FaTimes, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sidebar-menu\",\n        children: [menuItems.map(item => /*#__PURE__*/_jsxDEV(NavLink, {\n          to: item.path,\n          className: ({\n            isActive\n          }) => isActive ? 'sidebar-item active' : 'sidebar-item',\n          onClick: () => {\n            setActivePage(item.path);\n            handleNavClick();\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"sidebar-icon\",\n            children: item.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"sidebar-text\",\n            children: item.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 15\n          }, this)]\n        }, item.path, true, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 13\n        }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"sidebar-collapsible\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `sidebar-item collapsible-header ${userManagementOpen ? 'open' : ''}`,\n            onClick: toggleUserManagement,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sidebar-icon\",\n              children: /*#__PURE__*/_jsxDEV(FaUsers, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sidebar-text\",\n              children: \"User Management\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sidebar-chevron\",\n              children: userManagementOpen ? /*#__PURE__*/_jsxDEV(FaChevronDown, {\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 39\n              }, this) : /*#__PURE__*/_jsxDEV(FaChevronRight, {\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 69\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `sidebar-submenu ${userManagementOpen ? 'open' : ''}`,\n            children: userManagementItems.map(item => /*#__PURE__*/_jsxDEV(NavLink, {\n              to: item.path,\n              className: ({\n                isActive\n              }) => isActive ? 'sidebar-subitem active' : 'sidebar-subitem',\n              onClick: () => {\n                setActivePage(item.path);\n                handleNavClick();\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"sidebar-icon\",\n                children: item.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"sidebar-text\",\n                children: item.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 19\n              }, this)]\n            }, item.path, true, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sidebar-footer\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"sidebar-logout\",\n          onClick: handleLogout,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"sidebar-icon\",\n            children: /*#__PURE__*/_jsxDEV(FaSignOutAlt, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 43\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"sidebar-text\",\n            children: \"Logout\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"sidebar-watermark\",\n          children: \"Powered by AiSPRY\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 7\n    }, this), isMobile && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mobile-nav\",\n      children: menuItems.map(item => /*#__PURE__*/_jsxDEV(NavLink, {\n        to: item.path,\n        className: ({\n          isActive\n        }) => isActive ? 'mobile-nav-item active' : 'mobile-nav-item',\n        onClick: () => setActivePage(item.path),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mobile-nav-icon\",\n          children: item.icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mobile-nav-text\",\n          children: item.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 15\n        }, this)]\n      }, item.path, true, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true);\n};\n_s(Sidebar, \"Ao/aLSUDXbgbS1nab3YX1hoU4g4=\", false, function () {\n  return [useNavigate];\n});\n_c = Sidebar;\nexport default Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "NavLink", "useNavigate", "FaHome", "FaMap", "FaColumns", "FaLightbulb", "FaChartBar", "FaSignOutAlt", "FaBars", "FaTimes", "FaUsers", "FaChevronDown", "FaChevronRight", "FaUserTie", "FaUserCog", "FaUserShield", "FaProjectDiagram", "FaRoute", "axios", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Sidebar", "onLogout", "_s", "navigate", "activePage", "setActivePage", "window", "location", "pathname", "isMobile", "setIsMobile", "innerWidth", "isOpen", "setIsOpen", "userManagementOpen", "setUserManagementOpen", "handleResize", "mobile", "addEventListener", "removeEventListener", "handleLogout", "post", "error", "console", "toggleSidebar", "handleNavClick", "menuItems", "path", "name", "icon", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "userManagementItems", "toggleUserManagement", "children", "className", "onClick", "map", "item", "to", "isActive", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Deep Learning/LTAGIT/LTA/frontend/src/components/Sidebar.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { NavLink, useNavigate } from 'react-router-dom';\r\nimport {\r\n  FaHome,\r\n  FaMap,\r\n  FaColumns,\r\n  FaLightbulb,\r\n  FaChartBar,\r\n  FaSignOutAlt,\r\n  FaBars,\r\n  FaTimes,\r\n  FaUsers,\r\n  FaChevronDown,\r\n  FaChevronRight,\r\n  FaUserTie,\r\n  FaUserCog,\r\n  FaUserShield,\r\n  FaProjectDiagram,\r\n  FaRoute\r\n} from 'react-icons/fa';\r\nimport axios from 'axios';\r\nimport './Sidebar.css';\r\n\r\nconst Sidebar = ({ onLogout }) => {\r\n  const navigate = useNavigate();\r\n  const [activePage, setActivePage] = useState(window.location.pathname);\r\n  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);\r\n  const [isOpen, setIsOpen] = useState(!isMobile);\r\n  const [userManagementOpen, setUserManagementOpen] = useState(false);\r\n\r\n  // Handle window resize events\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      const mobile = window.innerWidth <= 768;\r\n      setIsMobile(mobile);\r\n      if (!mobile) {\r\n        setIsOpen(true);\r\n      } else {\r\n        setIsOpen(false);\r\n      }\r\n    };\r\n\r\n    window.addEventListener('resize', handleResize);\r\n    return () => window.removeEventListener('resize', handleResize);\r\n  }, []);\r\n\r\n  const handleLogout = async () => {\r\n    try {\r\n      // Call logout API\r\n      await axios.post('/api/auth/logout');\r\n      // Call the onLogout prop function to update app state\r\n      onLogout();\r\n      // Redirect to login page\r\n      navigate('/login');\r\n    } catch (error) {\r\n      console.error('Logout error:', error);\r\n      // Even if there's an error, still log out on the client side\r\n      onLogout();\r\n      navigate('/login');\r\n    }\r\n  };\r\n\r\n  const toggleSidebar = () => {\r\n    setIsOpen(!isOpen);\r\n  };\r\n\r\n  const handleNavClick = () => {\r\n    if (isMobile) {\r\n      setIsOpen(false);\r\n    }\r\n  };\r\n\r\n  const menuItems = [\r\n    { path: '/', name: 'Home', icon: <FaHome size={20} /> },\r\n    { path: '/pavement', name: 'Pavement', icon: <FaMap size={20} /> },\r\n    { path: '/road-infrastructure', name: 'Infrastructure', icon: <FaColumns size={20} /> },\r\n    { path: '/recommendation', name: 'Recommendation', icon: <FaLightbulb size={20} /> },\r\n    { path: '/dashboard', name: 'Dashboard', icon: <FaChartBar size={20} /> }\r\n  ];\r\n\r\n  const userManagementItems = [\r\n    { path: '/user-management/clients', name: 'Clients', icon: <FaUserTie size={18} /> },\r\n    { path: '/user-management/supervisors', name: 'Supervisors', icon: <FaUserShield size={18} /> },\r\n    { path: '/user-management/field-officers', name: 'Field Officers', icon: <FaUserCog size={18} /> },\r\n    { path: '/user-management/projects', name: 'Projects', icon: <FaProjectDiagram size={18} /> },\r\n    { path: '/user-management/routes', name: 'Routes', icon: <FaRoute size={18} /> }\r\n  ];\r\n\r\n  const toggleUserManagement = () => {\r\n    setUserManagementOpen(!userManagementOpen);\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {/* Mobile sidebar toggle button */}\r\n      {isMobile && (\r\n        <button \r\n          className=\"sidebar-toggle\" \r\n          onClick={toggleSidebar}\r\n          aria-label={isOpen ? \"Close menu\" : \"Open menu\"}\r\n        >\r\n          {isOpen ? <FaTimes size={24} /> : <FaBars size={24} />}\r\n        </button>\r\n      )}\r\n\r\n      {/* Sidebar overlay backdrop for mobile */}\r\n      {isMobile && isOpen && (\r\n        <div className=\"sidebar-backdrop\" onClick={toggleSidebar} />\r\n      )}\r\n\r\n      {/* Main sidebar */}\r\n      <div className={`sidebar ${isOpen ? 'open' : 'closed'}`}>\r\n        <div className=\"sidebar-header\">\r\n          <h3></h3>\r\n          {isMobile && (\r\n            <button \r\n              className=\"sidebar-close\" \r\n              onClick={toggleSidebar}\r\n              aria-label=\"Close menu\"\r\n            >\r\n              <FaTimes size={20} />\r\n            </button>\r\n          )}\r\n        </div>\r\n        <div className=\"sidebar-menu\">\r\n          {menuItems.map((item) => (\r\n            <NavLink\r\n              key={item.path}\r\n              to={item.path}\r\n              className={({ isActive }) =>\r\n                isActive ? 'sidebar-item active' : 'sidebar-item'\r\n              }\r\n              onClick={() => {\r\n                setActivePage(item.path);\r\n                handleNavClick();\r\n              }}\r\n            >\r\n              <div className=\"sidebar-icon\">{item.icon}</div>\r\n              <div className=\"sidebar-text\">{item.name}</div>\r\n            </NavLink>\r\n          ))}\r\n\r\n          {/* User Management Collapsible Menu */}\r\n          <div className=\"sidebar-collapsible\">\r\n            <div\r\n              className={`sidebar-item collapsible-header ${userManagementOpen ? 'open' : ''}`}\r\n              onClick={toggleUserManagement}\r\n            >\r\n              <div className=\"sidebar-icon\"><FaUsers size={20} /></div>\r\n              <div className=\"sidebar-text\">User Management</div>\r\n              <div className=\"sidebar-chevron\">\r\n                {userManagementOpen ? <FaChevronDown size={14} /> : <FaChevronRight size={14} />}\r\n              </div>\r\n            </div>\r\n\r\n            <div className={`sidebar-submenu ${userManagementOpen ? 'open' : ''}`}>\r\n              {userManagementItems.map((item) => (\r\n                <NavLink\r\n                  key={item.path}\r\n                  to={item.path}\r\n                  className={({ isActive }) =>\r\n                    isActive ? 'sidebar-subitem active' : 'sidebar-subitem'\r\n                  }\r\n                  onClick={() => {\r\n                    setActivePage(item.path);\r\n                    handleNavClick();\r\n                  }}\r\n                >\r\n                  <div className=\"sidebar-icon\">{item.icon}</div>\r\n                  <div className=\"sidebar-text\">{item.name}</div>\r\n                </NavLink>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div className=\"sidebar-footer\">\r\n          <button className=\"sidebar-logout\" onClick={handleLogout}>\r\n            <div className=\"sidebar-icon\"><FaSignOutAlt size={20} /></div>\r\n            <div className=\"sidebar-text\">Logout</div>\r\n          </button>\r\n          <div className=\"sidebar-watermark\">Powered by AiSPRY</div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Mobile bottom navigation for quick access */}\r\n      {isMobile && (\r\n        <div className=\"mobile-nav\">\r\n          {menuItems.map((item) => (\r\n            <NavLink\r\n              key={item.path}\r\n              to={item.path}\r\n              className={({ isActive }) => \r\n                isActive ? 'mobile-nav-item active' : 'mobile-nav-item'\r\n              }\r\n              onClick={() => setActivePage(item.path)}\r\n            >\r\n              <div className=\"mobile-nav-icon\">{item.icon}</div>\r\n              <div className=\"mobile-nav-text\">{item.name}</div>\r\n            </NavLink>\r\n          ))}\r\n        </div>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Sidebar; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,EAAEC,WAAW,QAAQ,kBAAkB;AACvD,SACEC,MAAM,EACNC,KAAK,EACLC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,YAAY,EACZC,MAAM,EACNC,OAAO,EACPC,OAAO,EACPC,aAAa,EACbC,cAAc,EACdC,SAAS,EACTC,SAAS,EACTC,YAAY,EACZC,gBAAgB,EAChBC,OAAO,QACF,gBAAgB;AACvB,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvB,MAAMC,OAAO,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAChC,MAAMC,QAAQ,GAAGzB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC0B,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC+B,MAAM,CAACC,QAAQ,CAACC,QAAQ,CAAC;EACtE,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGnC,QAAQ,CAAC+B,MAAM,CAACK,UAAU,IAAI,GAAG,CAAC;EAClE,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGtC,QAAQ,CAAC,CAACkC,QAAQ,CAAC;EAC/C,MAAM,CAACK,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;;EAEnE;EACAC,SAAS,CAAC,MAAM;IACd,MAAMwC,YAAY,GAAGA,CAAA,KAAM;MACzB,MAAMC,MAAM,GAAGX,MAAM,CAACK,UAAU,IAAI,GAAG;MACvCD,WAAW,CAACO,MAAM,CAAC;MACnB,IAAI,CAACA,MAAM,EAAE;QACXJ,SAAS,CAAC,IAAI,CAAC;MACjB,CAAC,MAAM;QACLA,SAAS,CAAC,KAAK,CAAC;MAClB;IACF,CAAC;IAEDP,MAAM,CAACY,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;IAC/C,OAAO,MAAMV,MAAM,CAACa,mBAAmB,CAAC,QAAQ,EAAEH,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF;MACA,MAAMzB,KAAK,CAAC0B,IAAI,CAAC,kBAAkB,CAAC;MACpC;MACApB,QAAQ,CAAC,CAAC;MACV;MACAE,QAAQ,CAAC,QAAQ,CAAC;IACpB,CAAC,CAAC,OAAOmB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrC;MACArB,QAAQ,CAAC,CAAC;MACVE,QAAQ,CAAC,QAAQ,CAAC;IACpB;EACF,CAAC;EAED,MAAMqB,aAAa,GAAGA,CAAA,KAAM;IAC1BX,SAAS,CAAC,CAACD,MAAM,CAAC;EACpB,CAAC;EAED,MAAMa,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIhB,QAAQ,EAAE;MACZI,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;EAED,MAAMa,SAAS,GAAG,CAChB;IAAEC,IAAI,EAAE,GAAG;IAAEC,IAAI,EAAE,MAAM;IAAEC,IAAI,eAAEhC,OAAA,CAAClB,MAAM;MAACmD,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EACvD;IAAEP,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAE,UAAU;IAAEC,IAAI,eAAEhC,OAAA,CAACjB,KAAK;MAACkD,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EAClE;IAAEP,IAAI,EAAE,sBAAsB;IAAEC,IAAI,EAAE,gBAAgB;IAAEC,IAAI,eAAEhC,OAAA,CAAChB,SAAS;MAACiD,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EACvF;IAAEP,IAAI,EAAE,iBAAiB;IAAEC,IAAI,EAAE,gBAAgB;IAAEC,IAAI,eAAEhC,OAAA,CAACf,WAAW;MAACgD,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EACpF;IAAEP,IAAI,EAAE,YAAY;IAAEC,IAAI,EAAE,WAAW;IAAEC,IAAI,eAAEhC,OAAA,CAACd,UAAU;MAAC+C,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,CAC1E;EAED,MAAMC,mBAAmB,GAAG,CAC1B;IAAER,IAAI,EAAE,0BAA0B;IAAEC,IAAI,EAAE,SAAS;IAAEC,IAAI,eAAEhC,OAAA,CAACP,SAAS;MAACwC,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EACpF;IAAEP,IAAI,EAAE,8BAA8B;IAAEC,IAAI,EAAE,aAAa;IAAEC,IAAI,eAAEhC,OAAA,CAACL,YAAY;MAACsC,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EAC/F;IAAEP,IAAI,EAAE,iCAAiC;IAAEC,IAAI,EAAE,gBAAgB;IAAEC,IAAI,eAAEhC,OAAA,CAACN,SAAS;MAACuC,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EAClG;IAAEP,IAAI,EAAE,2BAA2B;IAAEC,IAAI,EAAE,UAAU;IAAEC,IAAI,eAAEhC,OAAA,CAACJ,gBAAgB;MAACqC,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EAC7F;IAAEP,IAAI,EAAE,yBAAyB;IAAEC,IAAI,EAAE,QAAQ;IAAEC,IAAI,eAAEhC,OAAA,CAACH,OAAO;MAACoC,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,CACjF;EAED,MAAME,oBAAoB,GAAGA,CAAA,KAAM;IACjCrB,qBAAqB,CAAC,CAACD,kBAAkB,CAAC;EAC5C,CAAC;EAED,oBACEjB,OAAA,CAAAE,SAAA;IAAAsC,QAAA,GAEG5B,QAAQ,iBACPZ,OAAA;MACEyC,SAAS,EAAC,gBAAgB;MAC1BC,OAAO,EAAEf,aAAc;MACvB,cAAYZ,MAAM,GAAG,YAAY,GAAG,WAAY;MAAAyB,QAAA,EAE/CzB,MAAM,gBAAGf,OAAA,CAACX,OAAO;QAAC4C,IAAI,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAAGrC,OAAA,CAACZ,MAAM;QAAC6C,IAAI,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CACT,EAGAzB,QAAQ,IAAIG,MAAM,iBACjBf,OAAA;MAAKyC,SAAS,EAAC,kBAAkB;MAACC,OAAO,EAAEf;IAAc;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAC5D,eAGDrC,OAAA;MAAKyC,SAAS,EAAE,WAAW1B,MAAM,GAAG,MAAM,GAAG,QAAQ,EAAG;MAAAyB,QAAA,gBACtDxC,OAAA;QAAKyC,SAAS,EAAC,gBAAgB;QAAAD,QAAA,gBAC7BxC,OAAA;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EACRzB,QAAQ,iBACPZ,OAAA;UACEyC,SAAS,EAAC,eAAe;UACzBC,OAAO,EAAEf,aAAc;UACvB,cAAW,YAAY;UAAAa,QAAA,eAEvBxC,OAAA,CAACX,OAAO;YAAC4C,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACNrC,OAAA;QAAKyC,SAAS,EAAC,cAAc;QAAAD,QAAA,GAC1BX,SAAS,CAACc,GAAG,CAAEC,IAAI,iBAClB5C,OAAA,CAACpB,OAAO;UAENiE,EAAE,EAAED,IAAI,CAACd,IAAK;UACdW,SAAS,EAAEA,CAAC;YAAEK;UAAS,CAAC,KACtBA,QAAQ,GAAG,qBAAqB,GAAG,cACpC;UACDJ,OAAO,EAAEA,CAAA,KAAM;YACblC,aAAa,CAACoC,IAAI,CAACd,IAAI,CAAC;YACxBF,cAAc,CAAC,CAAC;UAClB,CAAE;UAAAY,QAAA,gBAEFxC,OAAA;YAAKyC,SAAS,EAAC,cAAc;YAAAD,QAAA,EAAEI,IAAI,CAACZ;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/CrC,OAAA;YAAKyC,SAAS,EAAC,cAAc;YAAAD,QAAA,EAAEI,IAAI,CAACb;UAAI;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA,GAX1CO,IAAI,CAACd,IAAI;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAYP,CACV,CAAC,eAGFrC,OAAA;UAAKyC,SAAS,EAAC,qBAAqB;UAAAD,QAAA,gBAClCxC,OAAA;YACEyC,SAAS,EAAE,mCAAmCxB,kBAAkB,GAAG,MAAM,GAAG,EAAE,EAAG;YACjFyB,OAAO,EAAEH,oBAAqB;YAAAC,QAAA,gBAE9BxC,OAAA;cAAKyC,SAAS,EAAC,cAAc;cAAAD,QAAA,eAACxC,OAAA,CAACV,OAAO;gBAAC2C,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACzDrC,OAAA;cAAKyC,SAAS,EAAC,cAAc;cAAAD,QAAA,EAAC;YAAe;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnDrC,OAAA;cAAKyC,SAAS,EAAC,iBAAiB;cAAAD,QAAA,EAC7BvB,kBAAkB,gBAAGjB,OAAA,CAACT,aAAa;gBAAC0C,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGrC,OAAA,CAACR,cAAc;gBAACyC,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENrC,OAAA;YAAKyC,SAAS,EAAE,mBAAmBxB,kBAAkB,GAAG,MAAM,GAAG,EAAE,EAAG;YAAAuB,QAAA,EACnEF,mBAAmB,CAACK,GAAG,CAAEC,IAAI,iBAC5B5C,OAAA,CAACpB,OAAO;cAENiE,EAAE,EAAED,IAAI,CAACd,IAAK;cACdW,SAAS,EAAEA,CAAC;gBAAEK;cAAS,CAAC,KACtBA,QAAQ,GAAG,wBAAwB,GAAG,iBACvC;cACDJ,OAAO,EAAEA,CAAA,KAAM;gBACblC,aAAa,CAACoC,IAAI,CAACd,IAAI,CAAC;gBACxBF,cAAc,CAAC,CAAC;cAClB,CAAE;cAAAY,QAAA,gBAEFxC,OAAA;gBAAKyC,SAAS,EAAC,cAAc;gBAAAD,QAAA,EAAEI,IAAI,CAACZ;cAAI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/CrC,OAAA;gBAAKyC,SAAS,EAAC,cAAc;gBAAAD,QAAA,EAAEI,IAAI,CAACb;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,GAX1CO,IAAI,CAACd,IAAI;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAYP,CACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNrC,OAAA;QAAKyC,SAAS,EAAC,gBAAgB;QAAAD,QAAA,gBAC7BxC,OAAA;UAAQyC,SAAS,EAAC,gBAAgB;UAACC,OAAO,EAAEnB,YAAa;UAAAiB,QAAA,gBACvDxC,OAAA;YAAKyC,SAAS,EAAC,cAAc;YAAAD,QAAA,eAACxC,OAAA,CAACb,YAAY;cAAC8C,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC9DrC,OAAA;YAAKyC,SAAS,EAAC,cAAc;YAAAD,QAAA,EAAC;UAAM;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,eACTrC,OAAA;UAAKyC,SAAS,EAAC,mBAAmB;UAAAD,QAAA,EAAC;QAAiB;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLzB,QAAQ,iBACPZ,OAAA;MAAKyC,SAAS,EAAC,YAAY;MAAAD,QAAA,EACxBX,SAAS,CAACc,GAAG,CAAEC,IAAI,iBAClB5C,OAAA,CAACpB,OAAO;QAENiE,EAAE,EAAED,IAAI,CAACd,IAAK;QACdW,SAAS,EAAEA,CAAC;UAAEK;QAAS,CAAC,KACtBA,QAAQ,GAAG,wBAAwB,GAAG,iBACvC;QACDJ,OAAO,EAAEA,CAAA,KAAMlC,aAAa,CAACoC,IAAI,CAACd,IAAI,CAAE;QAAAU,QAAA,gBAExCxC,OAAA;UAAKyC,SAAS,EAAC,iBAAiB;UAAAD,QAAA,EAAEI,IAAI,CAACZ;QAAI;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAClDrC,OAAA;UAAKyC,SAAS,EAAC,iBAAiB;UAAAD,QAAA,EAAEI,IAAI,CAACb;QAAI;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA,GAR7CO,IAAI,CAACd,IAAI;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OASP,CACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA,eACD,CAAC;AAEP,CAAC;AAAChC,EAAA,CArLIF,OAAO;EAAA,QACMtB,WAAW;AAAA;AAAAkE,EAAA,GADxB5C,OAAO;AAuLb,eAAeA,OAAO;AAAC,IAAA4C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}