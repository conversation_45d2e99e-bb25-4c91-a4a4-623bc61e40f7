{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Deep Learning\\\\LTAGIT\\\\LTA\\\\frontend\\\\src\\\\pages\\\\UserManagement\\\\Projects.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport './UserManagement.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Projects = () => {\n  _s();\n  const [projects, setProjects] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    fetchProjects();\n  }, []);\n  const fetchProjects = async () => {\n    try {\n      setLoading(true);\n      // TODO: Replace with actual API endpoint\n      const response = await axios.get('/api/user-management/projects');\n      setProjects(response.data.projects || []);\n    } catch (err) {\n      setError('Failed to fetch projects');\n      console.error('Error fetching projects:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"user-management-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading\",\n        children: \"Loading projects...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"user-management-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"user-management-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"user-management-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Project Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn-primary\",\n        children: \"Create New Project\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"user-management-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hierarchy-view\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Project Overview\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hierarchy-tree\",\n          children: projects.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"no-data\",\n            children: \"No projects found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 15\n          }, this) : projects.map(project => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"project-node\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"project-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"project-name\",\n                children: project.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 61,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"project-status\",\n                children: project.status\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 62,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"project-routes\",\n              children: project.routes && project.routes.map(route => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"route-node\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"route-name\",\n                  children: route.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 67,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"route-shared\",\n                  children: route.isShared ? '(Shared)' : ''\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 68,\n                  columnNumber: 25\n                }, this)]\n              }, route.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 66,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 19\n            }, this)]\n          }, project.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"management-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Quick Actions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"action-buttons\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-secondary\",\n            children: \"Assign Routes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-secondary\",\n            children: \"Manage Team\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-secondary\",\n            children: \"View Analytics\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 45,\n    columnNumber: 5\n  }, this);\n};\n_s(Projects, \"D01YWZOqXtPgB8Jf7trLNzFYnAg=\");\n_c = Projects;\nexport default Projects;\nvar _c;\n$RefreshReg$(_c, \"Projects\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "jsxDEV", "_jsxDEV", "Projects", "_s", "projects", "setProjects", "loading", "setLoading", "error", "setError", "fetchProjects", "response", "get", "data", "err", "console", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "map", "project", "name", "status", "routes", "route", "isShared", "id", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Deep Learning/LTAGIT/LTA/frontend/src/pages/UserManagement/Projects.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport './UserManagement.css';\n\nconst Projects = () => {\n  const [projects, setProjects] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  useEffect(() => {\n    fetchProjects();\n  }, []);\n\n  const fetchProjects = async () => {\n    try {\n      setLoading(true);\n      // TODO: Replace with actual API endpoint\n      const response = await axios.get('/api/user-management/projects');\n      setProjects(response.data.projects || []);\n    } catch (err) {\n      setError('Failed to fetch projects');\n      console.error('Error fetching projects:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"user-management-container\">\n        <div className=\"loading\">Loading projects...</div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"user-management-container\">\n        <div className=\"error\">{error}</div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"user-management-container\">\n      <div className=\"user-management-header\">\n        <h1>Project Management</h1>\n        <button className=\"btn-primary\">Create New Project</button>\n      </div>\n      \n      <div className=\"user-management-content\">\n        <div className=\"hierarchy-view\">\n          <h3>Project Overview</h3>\n          <div className=\"hierarchy-tree\">\n            {projects.length === 0 ? (\n              <div className=\"no-data\">No projects found</div>\n            ) : (\n              projects.map((project) => (\n                <div key={project.id} className=\"project-node\">\n                  <div className=\"project-info\">\n                    <span className=\"project-name\">{project.name}</span>\n                    <span className=\"project-status\">{project.status}</span>\n                  </div>\n                  <div className=\"project-routes\">\n                    {project.routes && project.routes.map((route) => (\n                      <div key={route.id} className=\"route-node\">\n                        <span className=\"route-name\">{route.name}</span>\n                        <span className=\"route-shared\">{route.isShared ? '(Shared)' : ''}</span>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              ))\n            )}\n          </div>\n        </div>\n        \n        <div className=\"management-actions\">\n          <h3>Quick Actions</h3>\n          <div className=\"action-buttons\">\n            <button className=\"btn-secondary\">Assign Routes</button>\n            <button className=\"btn-secondary\">Manage Team</button>\n            <button className=\"btn-secondary\">View Analytics</button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Projects;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACS,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACW,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAExCC,SAAS,CAAC,MAAM;IACdY,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACFH,UAAU,CAAC,IAAI,CAAC;MAChB;MACA,MAAMI,QAAQ,GAAG,MAAMZ,KAAK,CAACa,GAAG,CAAC,+BAA+B,CAAC;MACjEP,WAAW,CAACM,QAAQ,CAACE,IAAI,CAACT,QAAQ,IAAI,EAAE,CAAC;IAC3C,CAAC,CAAC,OAAOU,GAAG,EAAE;MACZL,QAAQ,CAAC,0BAA0B,CAAC;MACpCM,OAAO,CAACP,KAAK,CAAC,0BAA0B,EAAEM,GAAG,CAAC;IAChD,CAAC,SAAS;MACRP,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,IAAID,OAAO,EAAE;IACX,oBACEL,OAAA;MAAKe,SAAS,EAAC,2BAA2B;MAAAC,QAAA,eACxChB,OAAA;QAAKe,SAAS,EAAC,SAAS;QAAAC,QAAA,EAAC;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/C,CAAC;EAEV;EAEA,IAAIb,KAAK,EAAE;IACT,oBACEP,OAAA;MAAKe,SAAS,EAAC,2BAA2B;MAAAC,QAAA,eACxChB,OAAA;QAAKe,SAAS,EAAC,OAAO;QAAAC,QAAA,EAAET;MAAK;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC,CAAC;EAEV;EAEA,oBACEpB,OAAA;IAAKe,SAAS,EAAC,2BAA2B;IAAAC,QAAA,gBACxChB,OAAA;MAAKe,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBACrChB,OAAA;QAAAgB,QAAA,EAAI;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3BpB,OAAA;QAAQe,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD,CAAC,eAENpB,OAAA;MAAKe,SAAS,EAAC,yBAAyB;MAAAC,QAAA,gBACtChB,OAAA;QAAKe,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BhB,OAAA;UAAAgB,QAAA,EAAI;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzBpB,OAAA;UAAKe,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAC5Bb,QAAQ,CAACkB,MAAM,KAAK,CAAC,gBACpBrB,OAAA;YAAKe,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,GAEhDjB,QAAQ,CAACmB,GAAG,CAAEC,OAAO,iBACnBvB,OAAA;YAAsBe,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC5ChB,OAAA;cAAKe,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BhB,OAAA;gBAAMe,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAEO,OAAO,CAACC;cAAI;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpDpB,OAAA;gBAAMe,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAAEO,OAAO,CAACE;cAAM;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC,eACNpB,OAAA;cAAKe,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAC5BO,OAAO,CAACG,MAAM,IAAIH,OAAO,CAACG,MAAM,CAACJ,GAAG,CAAEK,KAAK,iBAC1C3B,OAAA;gBAAoBe,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACxChB,OAAA;kBAAMe,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAEW,KAAK,CAACH;gBAAI;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAChDpB,OAAA;kBAAMe,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAEW,KAAK,CAACC,QAAQ,GAAG,UAAU,GAAG;gBAAE;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA,GAFhEO,KAAK,CAACE,EAAE;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGb,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA,GAZEG,OAAO,CAACM,EAAE;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAaf,CACN;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpB,OAAA;QAAKe,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjChB,OAAA;UAAAgB,QAAA,EAAI;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtBpB,OAAA;UAAKe,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BhB,OAAA;YAAQe,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACxDpB,OAAA;YAAQe,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtDpB,OAAA;YAAQe,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClB,EAAA,CApFID,QAAQ;AAAA6B,EAAA,GAAR7B,QAAQ;AAsFd,eAAeA,QAAQ;AAAC,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}