import React, { useState, useEffect } from 'react';
import axios from 'axios';
import './UserManagement.css';

const FieldOfficers = () => {
  const [fieldOfficers, setFieldOfficers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchFieldOfficers();
  }, []);

  const fetchFieldOfficers = async () => {
    try {
      setLoading(true);
      // TODO: Replace with actual API endpoint
      const response = await axios.get('/api/user-management/field-officers');
      setFieldOfficers(response.data.fieldOfficers || []);
    } catch (err) {
      setError('Failed to fetch field officers');
      console.error('Error fetching field officers:', err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="user-management-container">
        <div className="loading">Loading field officers...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="user-management-container">
        <div className="error">{error}</div>
      </div>
    );
  }

  return (
    <div className="user-management-container">
      <div className="user-management-header">
        <h1>Field Officer Management</h1>
        <button className="btn-primary">Add New Field Officer</button>
      </div>
      
      <div className="user-management-content">
        <div className="hierarchy-view">
          <h3>Field Officer List</h3>
          <div className="hierarchy-tree">
            {fieldOfficers.length === 0 ? (
              <div className="no-data">No field officers found</div>
            ) : (
              fieldOfficers.map((fo) => (
                <div key={fo.id} className="field-officer-node">
                  <div className="field-officer-info">
                    <span className="field-officer-name">{fo.name}</span>
                    <span className="field-officer-role">Field Officer</span>
                  </div>
                  <div className="field-officer-routes">
                    {fo.assignedRoutes && fo.assignedRoutes.map((route) => (
                      <div key={route.id} className="route-node">
                        <span className="route-name">{route.name}</span>
                      </div>
                    ))}
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
        
        <div className="management-actions">
          <h3>Quick Actions</h3>
          <div className="action-buttons">
            <button className="btn-secondary">Assign Routes</button>
            <button className="btn-secondary">View Reports</button>
            <button className="btn-secondary">Update Profile</button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FieldOfficers;
