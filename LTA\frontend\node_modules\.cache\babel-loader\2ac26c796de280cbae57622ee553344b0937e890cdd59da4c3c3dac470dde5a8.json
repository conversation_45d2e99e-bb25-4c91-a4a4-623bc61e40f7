{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Deep Learning\\\\LTAGIT\\\\LTA\\\\frontend\\\\src\\\\pages\\\\UserManagement\\\\Supervisors.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport './UserManagement.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Supervisors = () => {\n  _s();\n  const [supervisors, setSupervisors] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    fetchSupervisors();\n  }, []);\n  const fetchSupervisors = async () => {\n    try {\n      setLoading(true);\n      // TODO: Replace with actual API endpoint\n      const response = await axios.get('/api/user-management/supervisors');\n      setSupervisors(response.data.supervisors || []);\n    } catch (err) {\n      setError('Failed to fetch supervisors');\n      console.error('Error fetching supervisors:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"user-management-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading\",\n        children: \"Loading supervisors...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"user-management-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"user-management-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"user-management-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Supervisor Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn-primary\",\n        children: \"Add New Supervisor\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"user-management-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hierarchy-view\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Supervisor Hierarchy\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hierarchy-tree\",\n          children: supervisors.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"no-data\",\n            children: \"No supervisors found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 15\n          }, this) : supervisors.map(supervisor => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"supervisor-node\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"supervisor-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"supervisor-name\",\n                children: supervisor.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 61,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"supervisor-role\",\n                children: \"Supervisor\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 62,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"supervisor-field-officers\",\n              children: supervisor.fieldOfficers && supervisor.fieldOfficers.map(fo => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"field-officer-node\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"field-officer-name\",\n                  children: fo.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 67,\n                  columnNumber: 25\n                }, this)\n              }, fo.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 66,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 19\n            }, this)]\n          }, supervisor.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"management-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Quick Actions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"action-buttons\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-secondary\",\n            children: \"Assign Field Officers\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-secondary\",\n            children: \"View Assigned Routes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-secondary\",\n            children: \"Generate Reports\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 45,\n    columnNumber: 5\n  }, this);\n};\n_s(Supervisors, \"B0FvuVXp2BHXM+WpOcn7V5eejXo=\");\n_c = Supervisors;\nexport default Supervisors;\nvar _c;\n$RefreshReg$(_c, \"Supervisors\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "jsxDEV", "_jsxDEV", "Supervisors", "_s", "supervisors", "setSupervisors", "loading", "setLoading", "error", "setError", "fetchSupervisors", "response", "get", "data", "err", "console", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "map", "supervisor", "name", "fieldOfficers", "fo", "id", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Deep Learning/LTAGIT/LTA/frontend/src/pages/UserManagement/Supervisors.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport './UserManagement.css';\n\nconst Supervisors = () => {\n  const [supervisors, setSupervisors] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  useEffect(() => {\n    fetchSupervisors();\n  }, []);\n\n  const fetchSupervisors = async () => {\n    try {\n      setLoading(true);\n      // TODO: Replace with actual API endpoint\n      const response = await axios.get('/api/user-management/supervisors');\n      setSupervisors(response.data.supervisors || []);\n    } catch (err) {\n      setError('Failed to fetch supervisors');\n      console.error('Error fetching supervisors:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"user-management-container\">\n        <div className=\"loading\">Loading supervisors...</div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"user-management-container\">\n        <div className=\"error\">{error}</div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"user-management-container\">\n      <div className=\"user-management-header\">\n        <h1>Supervisor Management</h1>\n        <button className=\"btn-primary\">Add New Supervisor</button>\n      </div>\n      \n      <div className=\"user-management-content\">\n        <div className=\"hierarchy-view\">\n          <h3>Supervisor Hierarchy</h3>\n          <div className=\"hierarchy-tree\">\n            {supervisors.length === 0 ? (\n              <div className=\"no-data\">No supervisors found</div>\n            ) : (\n              supervisors.map((supervisor) => (\n                <div key={supervisor.id} className=\"supervisor-node\">\n                  <div className=\"supervisor-info\">\n                    <span className=\"supervisor-name\">{supervisor.name}</span>\n                    <span className=\"supervisor-role\">Supervisor</span>\n                  </div>\n                  <div className=\"supervisor-field-officers\">\n                    {supervisor.fieldOfficers && supervisor.fieldOfficers.map((fo) => (\n                      <div key={fo.id} className=\"field-officer-node\">\n                        <span className=\"field-officer-name\">{fo.name}</span>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              ))\n            )}\n          </div>\n        </div>\n        \n        <div className=\"management-actions\">\n          <h3>Quick Actions</h3>\n          <div className=\"action-buttons\">\n            <button className=\"btn-secondary\">Assign Field Officers</button>\n            <button className=\"btn-secondary\">View Assigned Routes</button>\n            <button className=\"btn-secondary\">Generate Reports</button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Supervisors;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACS,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACW,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAExCC,SAAS,CAAC,MAAM;IACdY,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFH,UAAU,CAAC,IAAI,CAAC;MAChB;MACA,MAAMI,QAAQ,GAAG,MAAMZ,KAAK,CAACa,GAAG,CAAC,kCAAkC,CAAC;MACpEP,cAAc,CAACM,QAAQ,CAACE,IAAI,CAACT,WAAW,IAAI,EAAE,CAAC;IACjD,CAAC,CAAC,OAAOU,GAAG,EAAE;MACZL,QAAQ,CAAC,6BAA6B,CAAC;MACvCM,OAAO,CAACP,KAAK,CAAC,6BAA6B,EAAEM,GAAG,CAAC;IACnD,CAAC,SAAS;MACRP,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,IAAID,OAAO,EAAE;IACX,oBACEL,OAAA;MAAKe,SAAS,EAAC,2BAA2B;MAAAC,QAAA,eACxChB,OAAA;QAAKe,SAAS,EAAC,SAAS;QAAAC,QAAA,EAAC;MAAsB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CAAC;EAEV;EAEA,IAAIb,KAAK,EAAE;IACT,oBACEP,OAAA;MAAKe,SAAS,EAAC,2BAA2B;MAAAC,QAAA,eACxChB,OAAA;QAAKe,SAAS,EAAC,OAAO;QAAAC,QAAA,EAAET;MAAK;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC,CAAC;EAEV;EAEA,oBACEpB,OAAA;IAAKe,SAAS,EAAC,2BAA2B;IAAAC,QAAA,gBACxChB,OAAA;MAAKe,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBACrChB,OAAA;QAAAgB,QAAA,EAAI;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9BpB,OAAA;QAAQe,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD,CAAC,eAENpB,OAAA;MAAKe,SAAS,EAAC,yBAAyB;MAAAC,QAAA,gBACtChB,OAAA;QAAKe,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BhB,OAAA;UAAAgB,QAAA,EAAI;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7BpB,OAAA;UAAKe,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAC5Bb,WAAW,CAACkB,MAAM,KAAK,CAAC,gBACvBrB,OAAA;YAAKe,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,GAEnDjB,WAAW,CAACmB,GAAG,CAAEC,UAAU,iBACzBvB,OAAA;YAAyBe,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAClDhB,OAAA;cAAKe,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BhB,OAAA;gBAAMe,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAAEO,UAAU,CAACC;cAAI;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1DpB,OAAA;gBAAMe,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACNpB,OAAA;cAAKe,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EACvCO,UAAU,CAACE,aAAa,IAAIF,UAAU,CAACE,aAAa,CAACH,GAAG,CAAEI,EAAE,iBAC3D1B,OAAA;gBAAiBe,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,eAC7ChB,OAAA;kBAAMe,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAEU,EAAE,CAACF;gBAAI;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC,GAD7CM,EAAE,CAACC,EAAE;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA,GAXEG,UAAU,CAACI,EAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAYlB,CACN;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpB,OAAA;QAAKe,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjChB,OAAA;UAAAgB,QAAA,EAAI;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtBpB,OAAA;UAAKe,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BhB,OAAA;YAAQe,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAChEpB,OAAA;YAAQe,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC/DpB,OAAA;YAAQe,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClB,EAAA,CAnFID,WAAW;AAAA2B,EAAA,GAAX3B,WAAW;AAqFjB,eAAeA,WAAW;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}