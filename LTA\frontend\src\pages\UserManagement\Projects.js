import React, { useState, useEffect } from 'react';
import axios from 'axios';
import './UserManagement.css';

const Projects = () => {
  const [projects, setProjects] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchProjects();
  }, []);

  const fetchProjects = async () => {
    try {
      setLoading(true);
      // TODO: Replace with actual API endpoint
      const response = await axios.get('/api/user-management/projects');
      setProjects(response.data.projects || []);
    } catch (err) {
      setError('Failed to fetch projects');
      console.error('Error fetching projects:', err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="user-management-container">
        <div className="loading">Loading projects...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="user-management-container">
        <div className="error">{error}</div>
      </div>
    );
  }

  return (
    <div className="user-management-container">
      <div className="user-management-header">
        <h1>Project Management</h1>
        <button className="btn-primary">Create New Project</button>
      </div>
      
      <div className="user-management-content">
        <div className="hierarchy-view">
          <h3>Project Overview</h3>
          <div className="hierarchy-tree">
            {projects.length === 0 ? (
              <div className="no-data">No projects found</div>
            ) : (
              projects.map((project) => (
                <div key={project.id} className="project-node">
                  <div className="project-info">
                    <span className="project-name">{project.name}</span>
                    <span className="project-status">{project.status}</span>
                  </div>
                  <div className="project-routes">
                    {project.routes && project.routes.map((route) => (
                      <div key={route.id} className="route-node">
                        <span className="route-name">{route.name}</span>
                        <span className="route-shared">{route.isShared ? '(Shared)' : ''}</span>
                      </div>
                    ))}
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
        
        <div className="management-actions">
          <h3>Quick Actions</h3>
          <div className="action-buttons">
            <button className="btn-secondary">Assign Routes</button>
            <button className="btn-secondary">Manage Team</button>
            <button className="btn-secondary">View Analytics</button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Projects;
