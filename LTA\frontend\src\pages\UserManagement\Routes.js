import React, { useState, useEffect } from 'react';
import axios from 'axios';
import './UserManagement.css';

const Routes = () => {
  const [routes, setRoutes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchRoutes();
  }, []);

  const fetchRoutes = async () => {
    try {
      setLoading(true);
      // TODO: Replace with actual API endpoint
      const response = await axios.get('/api/user-management/routes');
      setRoutes(response.data.routes || []);
    } catch (err) {
      setError('Failed to fetch routes');
      console.error('Error fetching routes:', err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="user-management-container">
        <div className="loading">Loading routes...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="user-management-container">
        <div className="error">{error}</div>
      </div>
    );
  }

  return (
    <div className="user-management-container">
      <div className="user-management-header">
        <h1>Route Management</h1>
        <button className="btn-primary">Create New Route</button>
      </div>
      
      <div className="user-management-content">
        <div className="hierarchy-view">
          <h3>Route Assignment</h3>
          <div className="hierarchy-tree">
            {routes.length === 0 ? (
              <div className="no-data">No routes found</div>
            ) : (
              routes.map((route) => (
                <div key={route.id} className="route-node">
                  <div className="route-info">
                    <span className="route-name">{route.name}</span>
                    <span className="route-type">{route.type}</span>
                    {route.isShared && <span className="route-shared">Shared</span>}
                  </div>
                  <div className="route-assignments">
                    {route.assignedTo && route.assignedTo.map((assignment) => (
                      <div key={assignment.id} className="assignment-node">
                        <span className="assignment-name">{assignment.name}</span>
                        <span className="assignment-role">{assignment.role}</span>
                      </div>
                    ))}
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
        
        <div className="management-actions">
          <h3>Quick Actions</h3>
          <div className="action-buttons">
            <button className="btn-secondary">Assign to Field Officers</button>
            <button className="btn-secondary">Mark as Shared</button>
            <button className="btn-secondary">View Route Details</button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Routes;
