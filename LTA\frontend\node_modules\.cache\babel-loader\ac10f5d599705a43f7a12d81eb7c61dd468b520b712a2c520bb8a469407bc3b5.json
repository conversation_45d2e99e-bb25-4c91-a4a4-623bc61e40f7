{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Deep Learning\\\\LTAGIT\\\\LTA\\\\frontend\\\\src\\\\components\\\\Sidebar.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { NavLink, useNavigate } from 'react-router-dom';\nimport { FaHome, FaMap, FaColumns, FaLightbulb, FaChartBar, FaSignOutAlt, FaBars, FaTimes, FaUsers, FaUserTie, FaUserCog, FaRoute, FaBuilding, FaProjectDiagram, FaChevronDown, FaChevronRight } from 'react-icons/fa';\nimport axios from 'axios';\nimport './Sidebar.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Sidebar = ({\n  onLogout\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const [activePage, setActivePage] = useState(window.location.pathname);\n  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);\n  const [isOpen, setIsOpen] = useState(!isMobile);\n  const [expandedSections, setExpandedSections] = useState({\n    userManagement: false,\n    routeManagement: false\n  });\n\n  // Get current user from sessionStorage\n  const currentUser = JSON.parse(sessionStorage.getItem('currentUser') || '{}');\n\n  // Handle window resize events\n  useEffect(() => {\n    const handleResize = () => {\n      const mobile = window.innerWidth <= 768;\n      setIsMobile(mobile);\n      if (!mobile) {\n        setIsOpen(true);\n      } else {\n        setIsOpen(false);\n      }\n    };\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n  const handleLogout = async () => {\n    try {\n      // Call logout API\n      await axios.post('/api/auth/logout');\n      // Call the onLogout prop function to update app state\n      onLogout();\n      // Redirect to login page\n      navigate('/login');\n    } catch (error) {\n      console.error('Logout error:', error);\n      // Even if there's an error, still log out on the client side\n      onLogout();\n      navigate('/login');\n    }\n  };\n  const toggleSidebar = () => {\n    setIsOpen(!isOpen);\n  };\n  const handleNavClick = () => {\n    if (isMobile) {\n      setIsOpen(false);\n    }\n  };\n  const toggleSection = section => {\n    setExpandedSections(prev => ({\n      ...prev,\n      [section]: !prev[section]\n    }));\n  };\n  const hasPermission = requiredRoles => {\n    return requiredRoles.includes(currentUser.role);\n  };\n  const menuItems = [{\n    path: '/',\n    name: 'Home',\n    icon: /*#__PURE__*/_jsxDEV(FaHome, {\n      size: 20\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 38\n    }, this),\n    roles: ['Admin', 'Client', 'Supervisor', 'Field Officer']\n  }, {\n    path: '/pavement',\n    name: 'Pavement',\n    icon: /*#__PURE__*/_jsxDEV(FaMap, {\n      size: 20\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 50\n    }, this),\n    roles: ['Admin', 'Client', 'Supervisor', 'Field Officer']\n  }, {\n    path: '/road-infrastructure',\n    name: 'Infrastructure',\n    icon: /*#__PURE__*/_jsxDEV(FaColumns, {\n      size: 20\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 67\n    }, this),\n    roles: ['Admin', 'Client', 'Supervisor', 'Field Officer']\n  }, {\n    path: '/recommendation',\n    name: 'Recommendation',\n    icon: /*#__PURE__*/_jsxDEV(FaLightbulb, {\n      size: 20\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 62\n    }, this),\n    roles: ['Admin', 'Client', 'Supervisor', 'Field Officer']\n  }, {\n    path: '/dashboard',\n    name: 'Dashboard',\n    icon: /*#__PURE__*/_jsxDEV(FaChartBar, {\n      size: 20\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 52\n    }, this),\n    roles: ['Admin', 'Client', 'Supervisor', 'Field Officer']\n  }];\n  const userManagementItems = [{\n    path: '/clients',\n    name: 'Clients',\n    icon: /*#__PURE__*/_jsxDEV(FaBuilding, {\n      size: 18\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 48\n    }, this),\n    roles: ['Admin']\n  }, {\n    path: '/supervisors',\n    name: 'Supervisors',\n    icon: /*#__PURE__*/_jsxDEV(FaUserTie, {\n      size: 18\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 56\n    }, this),\n    roles: ['Admin', 'Client']\n  }, {\n    path: '/field-officers',\n    name: 'Field Officers',\n    icon: /*#__PURE__*/_jsxDEV(FaUserCog, {\n      size: 18\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 62\n    }, this),\n    roles: ['Admin', 'Client', 'Supervisor']\n  }];\n  const routeManagementItems = [{\n    path: '/projects',\n    name: 'Projects',\n    icon: /*#__PURE__*/_jsxDEV(FaProjectDiagram, {\n      size: 18\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 50\n    }, this),\n    roles: ['Admin', 'Client', 'Supervisor']\n  }, {\n    path: '/routes',\n    name: 'Routes',\n    icon: /*#__PURE__*/_jsxDEV(FaRoute, {\n      size: 18\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 46\n    }, this),\n    roles: ['Admin', 'Client', 'Supervisor', 'Field Officer']\n  }, {\n    path: '/assignments',\n    name: 'Assignments',\n    icon: /*#__PURE__*/_jsxDEV(FaUsers, {\n      size: 18\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 56\n    }, this),\n    roles: ['Admin', 'Client', 'Supervisor']\n  }];\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [isMobile && /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"sidebar-toggle\",\n      onClick: toggleSidebar,\n      \"aria-label\": isOpen ? \"Close menu\" : \"Open menu\",\n      children: isOpen ? /*#__PURE__*/_jsxDEV(FaTimes, {\n        size: 24\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 21\n      }, this) : /*#__PURE__*/_jsxDEV(FaBars, {\n        size: 24\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 45\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 9\n    }, this), isMobile && isOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sidebar-backdrop\",\n      onClick: toggleSidebar\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `sidebar ${isOpen ? 'open' : 'closed'}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sidebar-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this), isMobile && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"sidebar-close\",\n          onClick: toggleSidebar,\n          \"aria-label\": \"Close menu\",\n          children: /*#__PURE__*/_jsxDEV(FaTimes, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sidebar-menu\",\n        children: [menuItems.filter(item => hasPermission(item.roles)).map(item => /*#__PURE__*/_jsxDEV(NavLink, {\n          to: item.path,\n          className: ({\n            isActive\n          }) => isActive ? 'sidebar-item active' : 'sidebar-item',\n          onClick: () => {\n            setActivePage(item.path);\n            handleNavClick();\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"sidebar-icon\",\n            children: item.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"sidebar-text\",\n            children: item.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 15\n          }, this)]\n        }, item.path, true, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 13\n        }, this)), hasPermission(['Admin', 'Client', 'Supervisor']) && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"sidebar-section-header\",\n            onClick: () => toggleSection('userManagement'),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sidebar-icon\",\n              children: /*#__PURE__*/_jsxDEV(FaUsers, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sidebar-text\",\n              children: \"User Management\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sidebar-expand-icon\",\n              children: expandedSections.userManagement ? /*#__PURE__*/_jsxDEV(FaChevronDown, {\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 54\n              }, this) : /*#__PURE__*/_jsxDEV(FaChevronRight, {\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 84\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 15\n          }, this), expandedSections.userManagement && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"sidebar-subsection\",\n            children: userManagementItems.filter(item => hasPermission(item.roles)).map(item => /*#__PURE__*/_jsxDEV(NavLink, {\n              to: item.path,\n              className: ({\n                isActive\n              }) => isActive ? 'sidebar-subitem active' : 'sidebar-subitem',\n              onClick: () => {\n                setActivePage(item.path);\n                handleNavClick();\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"sidebar-icon\",\n                children: item.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"sidebar-text\",\n                children: item.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 23\n              }, this)]\n            }, item.path, true, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true), hasPermission(['Admin', 'Client', 'Supervisor', 'Field Officer']) && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"sidebar-section-header\",\n            onClick: () => toggleSection('routeManagement'),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sidebar-icon\",\n              children: /*#__PURE__*/_jsxDEV(FaRoute, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sidebar-text\",\n              children: \"Route Management\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sidebar-expand-icon\",\n              children: expandedSections.routeManagement ? /*#__PURE__*/_jsxDEV(FaChevronDown, {\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 55\n              }, this) : /*#__PURE__*/_jsxDEV(FaChevronRight, {\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 85\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 15\n          }, this), expandedSections.routeManagement && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"sidebar-subsection\",\n            children: routeManagementItems.filter(item => hasPermission(item.roles)).map(item => /*#__PURE__*/_jsxDEV(NavLink, {\n              to: item.path,\n              className: ({\n                isActive\n              }) => isActive ? 'sidebar-subitem active' : 'sidebar-subitem',\n              onClick: () => {\n                setActivePage(item.path);\n                handleNavClick();\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"sidebar-icon\",\n                children: item.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"sidebar-text\",\n                children: item.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 23\n              }, this)]\n            }, item.path, true, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sidebar-footer\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"sidebar-logout\",\n          onClick: handleLogout,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"sidebar-icon\",\n            children: /*#__PURE__*/_jsxDEV(FaSignOutAlt, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 43\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"sidebar-text\",\n            children: \"Logout\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"sidebar-watermark\",\n          children: \"Powered by AiSPRY\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this), isMobile && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mobile-nav\",\n      children: menuItems.filter(item => hasPermission(item.roles)).slice(0, 5).map(item => /*#__PURE__*/_jsxDEV(NavLink, {\n        to: item.path,\n        className: ({\n          isActive\n        }) => isActive ? 'mobile-nav-item active' : 'mobile-nav-item',\n        onClick: () => setActivePage(item.path),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mobile-nav-icon\",\n          children: item.icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mobile-nav-text\",\n          children: item.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 15\n        }, this)]\n      }, item.path, true, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 248,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true);\n};\n_s(Sidebar, \"Nt7mtNx6+Z0YACMdMNFZLmcojIg=\", false, function () {\n  return [useNavigate];\n});\n_c = Sidebar;\nexport default Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "NavLink", "useNavigate", "FaHome", "FaMap", "FaColumns", "FaLightbulb", "FaChartBar", "FaSignOutAlt", "FaBars", "FaTimes", "FaUsers", "FaUserTie", "FaUserCog", "FaRoute", "FaBuilding", "FaProjectDiagram", "FaChevronDown", "FaChevronRight", "axios", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Sidebar", "onLogout", "_s", "navigate", "activePage", "setActivePage", "window", "location", "pathname", "isMobile", "setIsMobile", "innerWidth", "isOpen", "setIsOpen", "expandedSections", "setExpandedSections", "userManagement", "routeManagement", "currentUser", "JSON", "parse", "sessionStorage", "getItem", "handleResize", "mobile", "addEventListener", "removeEventListener", "handleLogout", "post", "error", "console", "toggleSidebar", "handleNavClick", "toggleSection", "section", "prev", "hasPermission", "requiredRoles", "includes", "role", "menuItems", "path", "name", "icon", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "roles", "userManagementItems", "routeManagementItems", "children", "className", "onClick", "filter", "item", "map", "to", "isActive", "slice", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Deep Learning/LTAGIT/LTA/frontend/src/components/Sidebar.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { NavLink, useNavigate } from 'react-router-dom';\r\nimport {\r\n  FaHome,\r\n  FaMap,\r\n  FaColumns,\r\n  FaLightbulb,\r\n  FaChartBar,\r\n  FaSignOutAlt,\r\n  FaBars,\r\n  FaTimes,\r\n  FaUsers,\r\n  FaUserTie,\r\n  FaUserCog,\r\n  FaRoute,\r\n  FaBuilding,\r\n  FaProjectDiagram,\r\n  FaChevronDown,\r\n  FaChevronRight\r\n} from 'react-icons/fa';\r\nimport axios from 'axios';\r\nimport './Sidebar.css';\r\n\r\nconst Sidebar = ({ onLogout }) => {\r\n  const navigate = useNavigate();\r\n  const [activePage, setActivePage] = useState(window.location.pathname);\r\n  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);\r\n  const [isOpen, setIsOpen] = useState(!isMobile);\r\n  const [expandedSections, setExpandedSections] = useState({\r\n    userManagement: false,\r\n    routeManagement: false\r\n  });\r\n\r\n  // Get current user from sessionStorage\r\n  const currentUser = JSON.parse(sessionStorage.getItem('currentUser') || '{}');\r\n\r\n  // Handle window resize events\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      const mobile = window.innerWidth <= 768;\r\n      setIsMobile(mobile);\r\n      if (!mobile) {\r\n        setIsOpen(true);\r\n      } else {\r\n        setIsOpen(false);\r\n      }\r\n    };\r\n\r\n    window.addEventListener('resize', handleResize);\r\n    return () => window.removeEventListener('resize', handleResize);\r\n  }, []);\r\n\r\n  const handleLogout = async () => {\r\n    try {\r\n      // Call logout API\r\n      await axios.post('/api/auth/logout');\r\n      // Call the onLogout prop function to update app state\r\n      onLogout();\r\n      // Redirect to login page\r\n      navigate('/login');\r\n    } catch (error) {\r\n      console.error('Logout error:', error);\r\n      // Even if there's an error, still log out on the client side\r\n      onLogout();\r\n      navigate('/login');\r\n    }\r\n  };\r\n\r\n  const toggleSidebar = () => {\r\n    setIsOpen(!isOpen);\r\n  };\r\n\r\n  const handleNavClick = () => {\r\n    if (isMobile) {\r\n      setIsOpen(false);\r\n    }\r\n  };\r\n\r\n  const toggleSection = (section) => {\r\n    setExpandedSections(prev => ({\r\n      ...prev,\r\n      [section]: !prev[section]\r\n    }));\r\n  };\r\n\r\n  const hasPermission = (requiredRoles) => {\r\n    return requiredRoles.includes(currentUser.role);\r\n  };\r\n\r\n  const menuItems = [\r\n    { path: '/', name: 'Home', icon: <FaHome size={20} />, roles: ['Admin', 'Client', 'Supervisor', 'Field Officer'] },\r\n    { path: '/pavement', name: 'Pavement', icon: <FaMap size={20} />, roles: ['Admin', 'Client', 'Supervisor', 'Field Officer'] },\r\n    { path: '/road-infrastructure', name: 'Infrastructure', icon: <FaColumns size={20} />, roles: ['Admin', 'Client', 'Supervisor', 'Field Officer'] },\r\n    { path: '/recommendation', name: 'Recommendation', icon: <FaLightbulb size={20} />, roles: ['Admin', 'Client', 'Supervisor', 'Field Officer'] },\r\n    { path: '/dashboard', name: 'Dashboard', icon: <FaChartBar size={20} />, roles: ['Admin', 'Client', 'Supervisor', 'Field Officer'] }\r\n  ];\r\n\r\n  const userManagementItems = [\r\n    { path: '/clients', name: 'Clients', icon: <FaBuilding size={18} />, roles: ['Admin'] },\r\n    { path: '/supervisors', name: 'Supervisors', icon: <FaUserTie size={18} />, roles: ['Admin', 'Client'] },\r\n    { path: '/field-officers', name: 'Field Officers', icon: <FaUserCog size={18} />, roles: ['Admin', 'Client', 'Supervisor'] }\r\n  ];\r\n\r\n  const routeManagementItems = [\r\n    { path: '/projects', name: 'Projects', icon: <FaProjectDiagram size={18} />, roles: ['Admin', 'Client', 'Supervisor'] },\r\n    { path: '/routes', name: 'Routes', icon: <FaRoute size={18} />, roles: ['Admin', 'Client', 'Supervisor', 'Field Officer'] },\r\n    { path: '/assignments', name: 'Assignments', icon: <FaUsers size={18} />, roles: ['Admin', 'Client', 'Supervisor'] }\r\n  ];\r\n\r\n  return (\r\n    <>\r\n      {/* Mobile sidebar toggle button */}\r\n      {isMobile && (\r\n        <button \r\n          className=\"sidebar-toggle\" \r\n          onClick={toggleSidebar}\r\n          aria-label={isOpen ? \"Close menu\" : \"Open menu\"}\r\n        >\r\n          {isOpen ? <FaTimes size={24} /> : <FaBars size={24} />}\r\n        </button>\r\n      )}\r\n\r\n      {/* Sidebar overlay backdrop for mobile */}\r\n      {isMobile && isOpen && (\r\n        <div className=\"sidebar-backdrop\" onClick={toggleSidebar} />\r\n      )}\r\n\r\n      {/* Main sidebar */}\r\n      <div className={`sidebar ${isOpen ? 'open' : 'closed'}`}>\r\n        <div className=\"sidebar-header\">\r\n          <h3></h3>\r\n          {isMobile && (\r\n            <button \r\n              className=\"sidebar-close\" \r\n              onClick={toggleSidebar}\r\n              aria-label=\"Close menu\"\r\n            >\r\n              <FaTimes size={20} />\r\n            </button>\r\n          )}\r\n        </div>\r\n        <div className=\"sidebar-menu\">\r\n          {/* Main menu items */}\r\n          {menuItems.filter(item => hasPermission(item.roles)).map((item) => (\r\n            <NavLink\r\n              key={item.path}\r\n              to={item.path}\r\n              className={({ isActive }) =>\r\n                isActive ? 'sidebar-item active' : 'sidebar-item'\r\n              }\r\n              onClick={() => {\r\n                setActivePage(item.path);\r\n                handleNavClick();\r\n              }}\r\n            >\r\n              <div className=\"sidebar-icon\">{item.icon}</div>\r\n              <div className=\"sidebar-text\">{item.name}</div>\r\n            </NavLink>\r\n          ))}\r\n\r\n          {/* User Management Section */}\r\n          {hasPermission(['Admin', 'Client', 'Supervisor']) && (\r\n            <>\r\n              <div\r\n                className=\"sidebar-section-header\"\r\n                onClick={() => toggleSection('userManagement')}\r\n              >\r\n                <div className=\"sidebar-icon\">\r\n                  <FaUsers size={20} />\r\n                </div>\r\n                <div className=\"sidebar-text\">User Management</div>\r\n                <div className=\"sidebar-expand-icon\">\r\n                  {expandedSections.userManagement ? <FaChevronDown size={14} /> : <FaChevronRight size={14} />}\r\n                </div>\r\n              </div>\r\n              {expandedSections.userManagement && (\r\n                <div className=\"sidebar-subsection\">\r\n                  {userManagementItems.filter(item => hasPermission(item.roles)).map((item) => (\r\n                    <NavLink\r\n                      key={item.path}\r\n                      to={item.path}\r\n                      className={({ isActive }) =>\r\n                        isActive ? 'sidebar-subitem active' : 'sidebar-subitem'\r\n                      }\r\n                      onClick={() => {\r\n                        setActivePage(item.path);\r\n                        handleNavClick();\r\n                      }}\r\n                    >\r\n                      <div className=\"sidebar-icon\">{item.icon}</div>\r\n                      <div className=\"sidebar-text\">{item.name}</div>\r\n                    </NavLink>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </>\r\n          )}\r\n\r\n          {/* Route Management Section */}\r\n          {hasPermission(['Admin', 'Client', 'Supervisor', 'Field Officer']) && (\r\n            <>\r\n              <div\r\n                className=\"sidebar-section-header\"\r\n                onClick={() => toggleSection('routeManagement')}\r\n              >\r\n                <div className=\"sidebar-icon\">\r\n                  <FaRoute size={20} />\r\n                </div>\r\n                <div className=\"sidebar-text\">Route Management</div>\r\n                <div className=\"sidebar-expand-icon\">\r\n                  {expandedSections.routeManagement ? <FaChevronDown size={14} /> : <FaChevronRight size={14} />}\r\n                </div>\r\n              </div>\r\n              {expandedSections.routeManagement && (\r\n                <div className=\"sidebar-subsection\">\r\n                  {routeManagementItems.filter(item => hasPermission(item.roles)).map((item) => (\r\n                    <NavLink\r\n                      key={item.path}\r\n                      to={item.path}\r\n                      className={({ isActive }) =>\r\n                        isActive ? 'sidebar-subitem active' : 'sidebar-subitem'\r\n                      }\r\n                      onClick={() => {\r\n                        setActivePage(item.path);\r\n                        handleNavClick();\r\n                      }}\r\n                    >\r\n                      <div className=\"sidebar-icon\">{item.icon}</div>\r\n                      <div className=\"sidebar-text\">{item.name}</div>\r\n                    </NavLink>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </>\r\n          )}\r\n        </div>\r\n        <div className=\"sidebar-footer\">\r\n          <button className=\"sidebar-logout\" onClick={handleLogout}>\r\n            <div className=\"sidebar-icon\"><FaSignOutAlt size={20} /></div>\r\n            <div className=\"sidebar-text\">Logout</div>\r\n          </button>\r\n          <div className=\"sidebar-watermark\">Powered by AiSPRY</div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Mobile bottom navigation for quick access */}\r\n      {isMobile && (\r\n        <div className=\"mobile-nav\">\r\n          {menuItems.filter(item => hasPermission(item.roles)).slice(0, 5).map((item) => (\r\n            <NavLink\r\n              key={item.path}\r\n              to={item.path}\r\n              className={({ isActive }) =>\r\n                isActive ? 'mobile-nav-item active' : 'mobile-nav-item'\r\n              }\r\n              onClick={() => setActivePage(item.path)}\r\n            >\r\n              <div className=\"mobile-nav-icon\">{item.icon}</div>\r\n              <div className=\"mobile-nav-text\">{item.name}</div>\r\n            </NavLink>\r\n          ))}\r\n        </div>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Sidebar; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,EAAEC,WAAW,QAAQ,kBAAkB;AACvD,SACEC,MAAM,EACNC,KAAK,EACLC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,YAAY,EACZC,MAAM,EACNC,OAAO,EACPC,OAAO,EACPC,SAAS,EACTC,SAAS,EACTC,OAAO,EACPC,UAAU,EACVC,gBAAgB,EAChBC,aAAa,EACbC,cAAc,QACT,gBAAgB;AACvB,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvB,MAAMC,OAAO,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAChC,MAAMC,QAAQ,GAAGzB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC0B,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC+B,MAAM,CAACC,QAAQ,CAACC,QAAQ,CAAC;EACtE,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGnC,QAAQ,CAAC+B,MAAM,CAACK,UAAU,IAAI,GAAG,CAAC;EAClE,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGtC,QAAQ,CAAC,CAACkC,QAAQ,CAAC;EAC/C,MAAM,CAACK,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxC,QAAQ,CAAC;IACvDyC,cAAc,EAAE,KAAK;IACrBC,eAAe,EAAE;EACnB,CAAC,CAAC;;EAEF;EACA,MAAMC,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC;;EAE7E;EACA9C,SAAS,CAAC,MAAM;IACd,MAAM+C,YAAY,GAAGA,CAAA,KAAM;MACzB,MAAMC,MAAM,GAAGlB,MAAM,CAACK,UAAU,IAAI,GAAG;MACvCD,WAAW,CAACc,MAAM,CAAC;MACnB,IAAI,CAACA,MAAM,EAAE;QACXX,SAAS,CAAC,IAAI,CAAC;MACjB,CAAC,MAAM;QACLA,SAAS,CAAC,KAAK,CAAC;MAClB;IACF,CAAC;IAEDP,MAAM,CAACmB,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;IAC/C,OAAO,MAAMjB,MAAM,CAACoB,mBAAmB,CAAC,QAAQ,EAAEH,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF;MACA,MAAMhC,KAAK,CAACiC,IAAI,CAAC,kBAAkB,CAAC;MACpC;MACA3B,QAAQ,CAAC,CAAC;MACV;MACAE,QAAQ,CAAC,QAAQ,CAAC;IACpB,CAAC,CAAC,OAAO0B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrC;MACA5B,QAAQ,CAAC,CAAC;MACVE,QAAQ,CAAC,QAAQ,CAAC;IACpB;EACF,CAAC;EAED,MAAM4B,aAAa,GAAGA,CAAA,KAAM;IAC1BlB,SAAS,CAAC,CAACD,MAAM,CAAC;EACpB,CAAC;EAED,MAAMoB,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIvB,QAAQ,EAAE;MACZI,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;EAED,MAAMoB,aAAa,GAAIC,OAAO,IAAK;IACjCnB,mBAAmB,CAACoB,IAAI,KAAK;MAC3B,GAAGA,IAAI;MACP,CAACD,OAAO,GAAG,CAACC,IAAI,CAACD,OAAO;IAC1B,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,aAAa,GAAIC,aAAa,IAAK;IACvC,OAAOA,aAAa,CAACC,QAAQ,CAACpB,WAAW,CAACqB,IAAI,CAAC;EACjD,CAAC;EAED,MAAMC,SAAS,GAAG,CAChB;IAAEC,IAAI,EAAE,GAAG;IAAEC,IAAI,EAAE,MAAM;IAAEC,IAAI,eAAE9C,OAAA,CAAClB,MAAM;MAACiE,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,eAAe;EAAE,CAAC,EAClH;IAAER,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAE,UAAU;IAAEC,IAAI,eAAE9C,OAAA,CAACjB,KAAK;MAACgE,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,eAAe;EAAE,CAAC,EAC7H;IAAER,IAAI,EAAE,sBAAsB;IAAEC,IAAI,EAAE,gBAAgB;IAAEC,IAAI,eAAE9C,OAAA,CAAChB,SAAS;MAAC+D,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,eAAe;EAAE,CAAC,EAClJ;IAAER,IAAI,EAAE,iBAAiB;IAAEC,IAAI,EAAE,gBAAgB;IAAEC,IAAI,eAAE9C,OAAA,CAACf,WAAW;MAAC8D,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,eAAe;EAAE,CAAC,EAC/I;IAAER,IAAI,EAAE,YAAY;IAAEC,IAAI,EAAE,WAAW;IAAEC,IAAI,eAAE9C,OAAA,CAACd,UAAU;MAAC6D,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,eAAe;EAAE,CAAC,CACrI;EAED,MAAMC,mBAAmB,GAAG,CAC1B;IAAET,IAAI,EAAE,UAAU;IAAEC,IAAI,EAAE,SAAS;IAAEC,IAAI,eAAE9C,OAAA,CAACN,UAAU;MAACqD,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE,CAAC,OAAO;EAAE,CAAC,EACvF;IAAER,IAAI,EAAE,cAAc;IAAEC,IAAI,EAAE,aAAa;IAAEC,IAAI,eAAE9C,OAAA,CAACT,SAAS;MAACwD,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE,CAAC,OAAO,EAAE,QAAQ;EAAE,CAAC,EACxG;IAAER,IAAI,EAAE,iBAAiB;IAAEC,IAAI,EAAE,gBAAgB;IAAEC,IAAI,eAAE9C,OAAA,CAACR,SAAS;MAACuD,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,YAAY;EAAE,CAAC,CAC7H;EAED,MAAME,oBAAoB,GAAG,CAC3B;IAAEV,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAE,UAAU;IAAEC,IAAI,eAAE9C,OAAA,CAACL,gBAAgB;MAACoD,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,YAAY;EAAE,CAAC,EACvH;IAAER,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE,QAAQ;IAAEC,IAAI,eAAE9C,OAAA,CAACP,OAAO;MAACsD,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,eAAe;EAAE,CAAC,EAC3H;IAAER,IAAI,EAAE,cAAc;IAAEC,IAAI,EAAE,aAAa;IAAEC,IAAI,eAAE9C,OAAA,CAACV,OAAO;MAACyD,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,YAAY;EAAE,CAAC,CACrH;EAED,oBACEpD,OAAA,CAAAE,SAAA;IAAAqD,QAAA,GAEG3C,QAAQ,iBACPZ,OAAA;MACEwD,SAAS,EAAC,gBAAgB;MAC1BC,OAAO,EAAEvB,aAAc;MACvB,cAAYnB,MAAM,GAAG,YAAY,GAAG,WAAY;MAAAwC,QAAA,EAE/CxC,MAAM,gBAAGf,OAAA,CAACX,OAAO;QAAC0D,IAAI,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAAGnD,OAAA,CAACZ,MAAM;QAAC2D,IAAI,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CACT,EAGAvC,QAAQ,IAAIG,MAAM,iBACjBf,OAAA;MAAKwD,SAAS,EAAC,kBAAkB;MAACC,OAAO,EAAEvB;IAAc;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAC5D,eAGDnD,OAAA;MAAKwD,SAAS,EAAE,WAAWzC,MAAM,GAAG,MAAM,GAAG,QAAQ,EAAG;MAAAwC,QAAA,gBACtDvD,OAAA;QAAKwD,SAAS,EAAC,gBAAgB;QAAAD,QAAA,gBAC7BvD,OAAA;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EACRvC,QAAQ,iBACPZ,OAAA;UACEwD,SAAS,EAAC,eAAe;UACzBC,OAAO,EAAEvB,aAAc;UACvB,cAAW,YAAY;UAAAqB,QAAA,eAEvBvD,OAAA,CAACX,OAAO;YAAC0D,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACNnD,OAAA;QAAKwD,SAAS,EAAC,cAAc;QAAAD,QAAA,GAE1BZ,SAAS,CAACe,MAAM,CAACC,IAAI,IAAIpB,aAAa,CAACoB,IAAI,CAACP,KAAK,CAAC,CAAC,CAACQ,GAAG,CAAED,IAAI,iBAC5D3D,OAAA,CAACpB,OAAO;UAENiF,EAAE,EAAEF,IAAI,CAACf,IAAK;UACdY,SAAS,EAAEA,CAAC;YAAEM;UAAS,CAAC,KACtBA,QAAQ,GAAG,qBAAqB,GAAG,cACpC;UACDL,OAAO,EAAEA,CAAA,KAAM;YACbjD,aAAa,CAACmD,IAAI,CAACf,IAAI,CAAC;YACxBT,cAAc,CAAC,CAAC;UAClB,CAAE;UAAAoB,QAAA,gBAEFvD,OAAA;YAAKwD,SAAS,EAAC,cAAc;YAAAD,QAAA,EAAEI,IAAI,CAACb;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/CnD,OAAA;YAAKwD,SAAS,EAAC,cAAc;YAAAD,QAAA,EAAEI,IAAI,CAACd;UAAI;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA,GAX1CQ,IAAI,CAACf,IAAI;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAYP,CACV,CAAC,EAGDZ,aAAa,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC,iBAC/CvC,OAAA,CAAAE,SAAA;UAAAqD,QAAA,gBACEvD,OAAA;YACEwD,SAAS,EAAC,wBAAwB;YAClCC,OAAO,EAAEA,CAAA,KAAMrB,aAAa,CAAC,gBAAgB,CAAE;YAAAmB,QAAA,gBAE/CvD,OAAA;cAAKwD,SAAS,EAAC,cAAc;cAAAD,QAAA,eAC3BvD,OAAA,CAACV,OAAO;gBAACyD,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC,eACNnD,OAAA;cAAKwD,SAAS,EAAC,cAAc;cAAAD,QAAA,EAAC;YAAe;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnDnD,OAAA;cAAKwD,SAAS,EAAC,qBAAqB;cAAAD,QAAA,EACjCtC,gBAAgB,CAACE,cAAc,gBAAGnB,OAAA,CAACJ,aAAa;gBAACmD,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGnD,OAAA,CAACH,cAAc;gBAACkD,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1F,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EACLlC,gBAAgB,CAACE,cAAc,iBAC9BnB,OAAA;YAAKwD,SAAS,EAAC,oBAAoB;YAAAD,QAAA,EAChCF,mBAAmB,CAACK,MAAM,CAACC,IAAI,IAAIpB,aAAa,CAACoB,IAAI,CAACP,KAAK,CAAC,CAAC,CAACQ,GAAG,CAAED,IAAI,iBACtE3D,OAAA,CAACpB,OAAO;cAENiF,EAAE,EAAEF,IAAI,CAACf,IAAK;cACdY,SAAS,EAAEA,CAAC;gBAAEM;cAAS,CAAC,KACtBA,QAAQ,GAAG,wBAAwB,GAAG,iBACvC;cACDL,OAAO,EAAEA,CAAA,KAAM;gBACbjD,aAAa,CAACmD,IAAI,CAACf,IAAI,CAAC;gBACxBT,cAAc,CAAC,CAAC;cAClB,CAAE;cAAAoB,QAAA,gBAEFvD,OAAA;gBAAKwD,SAAS,EAAC,cAAc;gBAAAD,QAAA,EAAEI,IAAI,CAACb;cAAI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/CnD,OAAA;gBAAKwD,SAAS,EAAC,cAAc;gBAAAD,QAAA,EAAEI,IAAI,CAACd;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,GAX1CQ,IAAI,CAACf,IAAI;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAYP,CACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN;QAAA,eACD,CACH,EAGAZ,aAAa,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,eAAe,CAAC,CAAC,iBAChEvC,OAAA,CAAAE,SAAA;UAAAqD,QAAA,gBACEvD,OAAA;YACEwD,SAAS,EAAC,wBAAwB;YAClCC,OAAO,EAAEA,CAAA,KAAMrB,aAAa,CAAC,iBAAiB,CAAE;YAAAmB,QAAA,gBAEhDvD,OAAA;cAAKwD,SAAS,EAAC,cAAc;cAAAD,QAAA,eAC3BvD,OAAA,CAACP,OAAO;gBAACsD,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC,eACNnD,OAAA;cAAKwD,SAAS,EAAC,cAAc;cAAAD,QAAA,EAAC;YAAgB;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACpDnD,OAAA;cAAKwD,SAAS,EAAC,qBAAqB;cAAAD,QAAA,EACjCtC,gBAAgB,CAACG,eAAe,gBAAGpB,OAAA,CAACJ,aAAa;gBAACmD,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGnD,OAAA,CAACH,cAAc;gBAACkD,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3F,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EACLlC,gBAAgB,CAACG,eAAe,iBAC/BpB,OAAA;YAAKwD,SAAS,EAAC,oBAAoB;YAAAD,QAAA,EAChCD,oBAAoB,CAACI,MAAM,CAACC,IAAI,IAAIpB,aAAa,CAACoB,IAAI,CAACP,KAAK,CAAC,CAAC,CAACQ,GAAG,CAAED,IAAI,iBACvE3D,OAAA,CAACpB,OAAO;cAENiF,EAAE,EAAEF,IAAI,CAACf,IAAK;cACdY,SAAS,EAAEA,CAAC;gBAAEM;cAAS,CAAC,KACtBA,QAAQ,GAAG,wBAAwB,GAAG,iBACvC;cACDL,OAAO,EAAEA,CAAA,KAAM;gBACbjD,aAAa,CAACmD,IAAI,CAACf,IAAI,CAAC;gBACxBT,cAAc,CAAC,CAAC;cAClB,CAAE;cAAAoB,QAAA,gBAEFvD,OAAA;gBAAKwD,SAAS,EAAC,cAAc;gBAAAD,QAAA,EAAEI,IAAI,CAACb;cAAI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/CnD,OAAA;gBAAKwD,SAAS,EAAC,cAAc;gBAAAD,QAAA,EAAEI,IAAI,CAACd;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,GAX1CQ,IAAI,CAACf,IAAI;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAYP,CACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN;QAAA,eACD,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACNnD,OAAA;QAAKwD,SAAS,EAAC,gBAAgB;QAAAD,QAAA,gBAC7BvD,OAAA;UAAQwD,SAAS,EAAC,gBAAgB;UAACC,OAAO,EAAE3B,YAAa;UAAAyB,QAAA,gBACvDvD,OAAA;YAAKwD,SAAS,EAAC,cAAc;YAAAD,QAAA,eAACvD,OAAA,CAACb,YAAY;cAAC4D,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC9DnD,OAAA;YAAKwD,SAAS,EAAC,cAAc;YAAAD,QAAA,EAAC;UAAM;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,eACTnD,OAAA;UAAKwD,SAAS,EAAC,mBAAmB;UAAAD,QAAA,EAAC;QAAiB;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLvC,QAAQ,iBACPZ,OAAA;MAAKwD,SAAS,EAAC,YAAY;MAAAD,QAAA,EACxBZ,SAAS,CAACe,MAAM,CAACC,IAAI,IAAIpB,aAAa,CAACoB,IAAI,CAACP,KAAK,CAAC,CAAC,CAACW,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACH,GAAG,CAAED,IAAI,iBACxE3D,OAAA,CAACpB,OAAO;QAENiF,EAAE,EAAEF,IAAI,CAACf,IAAK;QACdY,SAAS,EAAEA,CAAC;UAAEM;QAAS,CAAC,KACtBA,QAAQ,GAAG,wBAAwB,GAAG,iBACvC;QACDL,OAAO,EAAEA,CAAA,KAAMjD,aAAa,CAACmD,IAAI,CAACf,IAAI,CAAE;QAAAW,QAAA,gBAExCvD,OAAA;UAAKwD,SAAS,EAAC,iBAAiB;UAAAD,QAAA,EAAEI,IAAI,CAACb;QAAI;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAClDnD,OAAA;UAAKwD,SAAS,EAAC,iBAAiB;UAAAD,QAAA,EAAEI,IAAI,CAACd;QAAI;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA,GAR7CQ,IAAI,CAACf,IAAI;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OASP,CACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA,eACD,CAAC;AAEP,CAAC;AAAC9C,EAAA,CAlPIF,OAAO;EAAA,QACMtB,WAAW;AAAA;AAAAmF,EAAA,GADxB7D,OAAO;AAoPb,eAAeA,OAAO;AAAC,IAAA6D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}