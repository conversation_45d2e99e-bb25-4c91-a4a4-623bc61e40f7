{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Deep Learning\\\\LTAGIT\\\\LTA\\\\frontend\\\\src\\\\pages\\\\Pavement.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Container, Card, Button, Form, Tabs, Tab, Alert, Spinner, OverlayTrigger, Popover, Modal } from 'react-bootstrap';\nimport axios from 'axios';\nimport Webcam from 'react-webcam';\nimport './Pavement.css';\nimport useResponsive from '../hooks/useResponsive';\nimport VideoDefectDetection from '../components/VideoDefectDetection';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Pavement = () => {\n  _s();\n  const [activeTab, setActiveTab] = useState('detection');\n  const [detectionType, setDetectionType] = useState('all');\n  const [imageFiles, setImageFiles] = useState([]);\n  const [imagePreviewsMap, setImagePreviewsMap] = useState({});\n  const [imageLocationMap, setImageLocationMap] = useState({});\n  const [currentImageIndex, setCurrentImageIndex] = useState(0);\n  const [processedImage, setProcessedImage] = useState(null);\n  const [results, setResults] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [cameraActive, setCameraActive] = useState(false);\n  const [coordinates, setCoordinates] = useState('Not Available');\n  const [cameraOrientation, setCameraOrientation] = useState('environment');\n  const [locationPermission, setLocationPermission] = useState('unknown');\n  const [locationError, setLocationError] = useState('');\n  const [locationLoading, setLocationLoading] = useState(false);\n\n  // Add state for batch processing results\n  const [batchResults, setBatchResults] = useState([]);\n  const [batchProcessing, setBatchProcessing] = useState(false);\n  const [processedCount, setProcessedCount] = useState(0);\n\n  // Add state for storing processed images for results table\n  const [processedImagesData, setProcessedImagesData] = useState({});\n\n  // Add state for classification error modal\n  const [showClassificationModal, setShowClassificationModal] = useState(false);\n  const [classificationError, setClassificationError] = useState('');\n  const [totalToProcess, setTotalToProcess] = useState(0);\n\n  // Add state for image modal\n  const [showImageModal, setShowImageModal] = useState(false);\n  const [selectedImageData, setSelectedImageData] = useState(null);\n\n  // Add state for image status table filtering\n  const [imageFilter, setImageFilter] = useState('all'); // 'all', 'road', 'non-road'\n\n  // Add state for road classification toggle (default to false for better user experience)\n  const [roadClassificationEnabled, setRoadClassificationEnabled] = useState(false);\n  // Add state for enhanced detection results table\n  const [detectionTableFilter, setDetectionTableFilter] = useState('all'); // 'all', 'potholes', 'cracks', 'kerbs'\n  const [sortConfig, setSortConfig] = useState({\n    key: null,\n    direction: 'asc'\n  });\n\n  // Auto-clear is always enabled - no toggle needed\n\n  const webcamRef = useRef(null);\n  const fileInputRef = useRef(null);\n  const {\n    isMobile\n  } = useResponsive();\n\n  // Create the popover content\n  const reminderPopover = /*#__PURE__*/_jsxDEV(Popover, {\n    id: \"reminder-popover\",\n    style: {\n      maxWidth: '300px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Popover.Header, {\n      as: \"h3\",\n      children: \"\\uD83D\\uDCF8 Image Upload Guidelines\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Popover.Body, {\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          marginBottom: '10px'\n        },\n        children: \"Please ensure your uploaded images are:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n        style: {\n          marginBottom: '0',\n          paddingLeft: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"Focused directly on the road surface\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"Well-lit and clear\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"Showing the entire area of concern\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"Taken from a reasonable distance to capture context\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 64,\n    columnNumber: 5\n  }, this);\n\n  // Safari-compatible geolocation permission check\n  const checkLocationPermission = async () => {\n    if (!navigator.permissions || !navigator.permissions.query) {\n      // Fallback for older browsers\n      return 'prompt';\n    }\n    try {\n      const permission = await navigator.permissions.query({\n        name: 'geolocation'\n      });\n      return permission.state;\n    } catch (err) {\n      console.warn('Permission API not supported or failed:', err);\n      return 'prompt';\n    }\n  };\n\n  // Safari-compatible geolocation request\n  const requestLocation = () => {\n    return new Promise((resolve, reject) => {\n      // Check if geolocation is supported\n      if (!navigator.geolocation) {\n        reject(new Error('Geolocation is not supported by this browser'));\n        return;\n      }\n\n      // Check if we're in a secure context (HTTPS)\n      if (!window.isSecureContext) {\n        reject(new Error('Geolocation requires a secure context (HTTPS)'));\n        return;\n      }\n      const options = {\n        enableHighAccuracy: true,\n        timeout: 15000,\n        // 15 seconds timeout\n        maximumAge: 60000 // Accept cached position up to 1 minute old\n      };\n      navigator.geolocation.getCurrentPosition(position => {\n        resolve(position);\n      }, error => {\n        let errorMessage = 'Unable to retrieve location';\n        switch (error.code) {\n          case error.PERMISSION_DENIED:\n            errorMessage = 'Location access denied. Please enable location permissions in your browser settings.';\n            break;\n          case error.POSITION_UNAVAILABLE:\n            errorMessage = 'Location information is unavailable. Please try again.';\n            break;\n          case error.TIMEOUT:\n            errorMessage = 'Location request timed out. Please try again.';\n            break;\n          default:\n            errorMessage = `Location error: ${error.message}`;\n            break;\n        }\n        reject(new Error(errorMessage));\n      }, options);\n    });\n  };\n\n  // Enhanced location handler with Safari-specific fixes\n  const handleLocationRequest = async () => {\n    setLocationLoading(true);\n    setLocationError('');\n    try {\n      // First check permission state\n      const permissionState = await checkLocationPermission();\n      setLocationPermission(permissionState);\n\n      // If permission is denied, provide user guidance\n      if (permissionState === 'denied') {\n        const errorMsg = 'Location access denied. To enable location access:\\n' + '• Safari: Settings > Privacy & Security > Location Services\\n' + '• Chrome: Settings > Privacy > Location\\n' + '• Firefox: Settings > Privacy > Location\\n' + 'Then refresh this page and try again.';\n        setLocationError(errorMsg);\n        setCoordinates('Permission Denied');\n        return;\n      }\n\n      // Request location\n      const position = await requestLocation();\n      const {\n        latitude,\n        longitude\n      } = position.coords;\n\n      // Format coordinates with better precision\n      const formattedCoords = `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;\n      setCoordinates(formattedCoords);\n      setLocationPermission('granted');\n      setLocationError('');\n      console.log('Location acquired:', {\n        latitude,\n        longitude,\n        accuracy: position.coords.accuracy\n      });\n    } catch (error) {\n      console.error('Location request failed:', error);\n      setLocationError(error.message);\n      setCoordinates('Location Error');\n\n      // Update permission state based on error\n      if (error.message.includes('denied')) {\n        setLocationPermission('denied');\n      }\n    } finally {\n      setLocationLoading(false);\n    }\n  };\n\n  // Handle multiple file input change\n  const handleFileChange = e => {\n    const files = Array.from(e.target.files);\n    if (files.length > 0) {\n      setImageFiles([...imageFiles, ...files]);\n\n      // Create previews and location data for each file\n      files.forEach(file => {\n        const reader = new FileReader();\n        reader.onloadend = () => {\n          setImagePreviewsMap(prev => ({\n            ...prev,\n            [file.name]: reader.result\n          }));\n        };\n        reader.readAsDataURL(file);\n\n        // Store location as \"Not Available\" for uploaded files\n        setImageLocationMap(prev => ({\n          ...prev,\n          [file.name]: 'Not Available'\n        }));\n      });\n\n      // Reset results\n      setProcessedImage(null);\n      setResults(null);\n      setError('');\n    }\n  };\n\n  // Handle camera capture with location validation\n  const handleCapture = async () => {\n    const imageSrc = webcamRef.current.getScreenshot();\n    if (imageSrc) {\n      // If we don't have location data, try to get it before capturing\n      if (coordinates === 'Not Available' || coordinates === 'Location Error') {\n        await handleLocationRequest();\n      }\n      const timestamp = new Date().toISOString();\n      const filename = `camera_capture_${timestamp}.jpg`;\n      const captureCoordinates = coordinates; // Capture current coordinates\n\n      setImageFiles([...imageFiles, filename]);\n      setImagePreviewsMap(prev => ({\n        ...prev,\n        [filename]: imageSrc\n      }));\n      setImageLocationMap(prev => ({\n        ...prev,\n        [filename]: captureCoordinates\n      }));\n      setCurrentImageIndex(imageFiles.length);\n      setProcessedImage(null);\n      setResults(null);\n      setError('');\n\n      // Log capture with current coordinates\n      console.log('Photo captured with coordinates:', captureCoordinates);\n    }\n  };\n\n  // Get location data for currently selected image\n  const getCurrentImageLocation = () => {\n    if (Object.keys(imagePreviewsMap).length === 0) {\n      return coordinates; // Use current coordinates if no images\n    }\n    const currentFilename = Object.keys(imagePreviewsMap)[currentImageIndex];\n    return imageLocationMap[currentFilename] || 'Not Available';\n  };\n\n  // Toggle camera with improved location handling\n  const toggleCamera = async () => {\n    const newCameraState = !cameraActive;\n    setCameraActive(newCameraState);\n    if (newCameraState) {\n      // Get location when camera is activated\n      await handleLocationRequest();\n    } else {\n      // Only reset location if no images are captured\n      // This preserves location data for captured images\n      if (Object.keys(imagePreviewsMap).length === 0) {\n        setCoordinates('Not Available');\n        setLocationError('');\n        setLocationPermission('unknown');\n      }\n    }\n  };\n\n  // Toggle camera orientation (front/back) for mobile devices\n  const toggleCameraOrientation = () => {\n    setCameraOrientation(prev => prev === 'environment' ? 'user' : 'environment');\n  };\n\n  // Helper function to handle classification errors\n  const handleClassificationError = errorMessage => {\n    setClassificationError(errorMessage);\n    setShowClassificationModal(true);\n    setError(''); // Clear general error since we're showing specific modal\n  };\n\n  // Process image for detection\n  const handleProcess = async () => {\n    setLoading(true);\n    setError('');\n    try {\n      // Get user info from session storage\n      const userString = sessionStorage.getItem('user');\n      const user = userString ? JSON.parse(userString) : null;\n\n      // Get the currently selected image\n      const currentImagePreview = Object.values(imagePreviewsMap)[currentImageIndex];\n      if (!currentImagePreview) {\n        setError('No image selected for processing');\n        setLoading(false);\n        return;\n      }\n\n      // Get coordinates for the current image\n      const imageCoordinates = getCurrentImageLocation();\n\n      // Get the current image filename\n      const filenames = Object.keys(imagePreviewsMap);\n      const currentFilename = filenames[currentImageIndex];\n\n      // Prepare request data\n      const requestData = {\n        image: currentImagePreview,\n        coordinates: imageCoordinates,\n        username: (user === null || user === void 0 ? void 0 : user.username) || 'Unknown',\n        role: (user === null || user === void 0 ? void 0 : user.role) || 'Unknown',\n        skip_road_classification: !roadClassificationEnabled\n      };\n\n      // Determine endpoint based on detection type\n      let endpoint;\n      switch (detectionType) {\n        case 'all':\n          endpoint = '/api/pavement/detect-all';\n          break;\n        case 'potholes':\n          endpoint = '/api/pavement/detect-potholes';\n          break;\n        case 'cracks':\n          endpoint = '/api/pavement/detect-cracks';\n          break;\n        case 'kerbs':\n          endpoint = '/api/pavement/detect-kerbs';\n          break;\n        default:\n          endpoint = '/api/pavement/detect-all';\n      }\n\n      // Make API request\n      const response = await axios.post(endpoint, requestData);\n\n      // Handle response\n      if (response.data.success) {\n        var _response$data$classi;\n        // Check if the image was actually processed (contains road) or just classified\n        const isProcessed = response.data.processed !== false;\n        const isRoad = ((_response$data$classi = response.data.classification) === null || _response$data$classi === void 0 ? void 0 : _response$data$classi.is_road) || false;\n\n        // Set the processed image and results for display\n        setProcessedImage(response.data.processed_image);\n        setResults(response.data);\n\n        // Extract detailed detection results for table display\n        const detectionResults = {\n          potholes: response.data.potholes || [],\n          cracks: response.data.cracks || [],\n          kerbs: response.data.kerbs || []\n        };\n\n        // Create batch result entry for the status table\n        const batchResult = {\n          filename: currentFilename,\n          success: true,\n          processed: isProcessed,\n          isRoad: isRoad,\n          classification: response.data.classification,\n          processedImage: response.data.processed_image,\n          originalImage: currentImagePreview,\n          // Add original image data\n          data: response.data,\n          detectionResults: detectionResults,\n          detectionCounts: {\n            potholes: detectionResults.potholes.length,\n            cracks: detectionResults.cracks.length,\n            kerbs: detectionResults.kerbs.length,\n            total: detectionResults.potholes.length + detectionResults.cracks.length + detectionResults.kerbs.length\n          }\n        };\n\n        // Update batch results to show the status table\n        setBatchResults([batchResult]);\n\n        // Auto-clear uploaded image icons after successful single image processing\n        // Store the processed image data before clearing (for both road and non-road)\n        setProcessedImagesData(prev => ({\n          ...prev,\n          [currentFilename]: {\n            originalImage: currentImagePreview,\n            processedImage: isRoad ? response.data.processed_image : null,\n            results: response.data,\n            isRoad: isRoad\n          }\n        }));\n\n        // Clear image previews and files but keep results\n        setImageFiles([]);\n        setImagePreviewsMap({});\n        setImageLocationMap({});\n        setCurrentImageIndex(0);\n\n        // Reset coordinates when clearing all images\n        setCoordinates('Not Available');\n        setLocationError('');\n        setLocationPermission('unknown');\n        if (fileInputRef.current) {\n          fileInputRef.current.value = '';\n        }\n      } else {\n        const errorMessage = response.data.message || 'Detection failed';\n\n        // Create batch result entry for failed processing\n        const batchResult = {\n          filename: currentFilename,\n          success: false,\n          processed: false,\n          isRoad: false,\n          error: errorMessage,\n          isClassificationError: errorMessage.includes('No road detected')\n        };\n\n        // Update batch results to show the status table\n        setBatchResults([batchResult]);\n        setError(errorMessage);\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      const errorMessage = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'An error occurred during detection. Please try again.';\n\n      // Get the current image filename for batch results\n      const filenames = Object.keys(imagePreviewsMap);\n      const currentFilename = filenames[currentImageIndex];\n\n      // Create batch result entry for error case\n      const batchResult = {\n        filename: currentFilename,\n        success: false,\n        processed: false,\n        isRoad: false,\n        error: errorMessage,\n        isClassificationError: errorMessage.includes('No road detected')\n      };\n\n      // Update batch results to show the status table\n      setBatchResults([batchResult]);\n\n      // Check if this is a classification error (no road detected)\n      if (errorMessage.includes('No road detected')) {\n        handleClassificationError(errorMessage);\n      } else {\n        setError(errorMessage);\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Add a new function to process all images\n  const handleProcessAll = async () => {\n    if (Object.keys(imagePreviewsMap).length === 0) {\n      setError('No images to process');\n      return;\n    }\n    setBatchProcessing(true);\n    setError('');\n    setBatchResults([]);\n    setProcessedCount(0);\n    setTotalToProcess(Object.keys(imagePreviewsMap).length);\n\n    // Get user info from session storage\n    const userString = sessionStorage.getItem('user');\n    const user = userString ? JSON.parse(userString) : null;\n    try {\n      // Determine endpoint based on detection type\n      let endpoint;\n      switch (detectionType) {\n        case 'all':\n          endpoint = '/api/pavement/detect-all';\n          break;\n        case 'potholes':\n          endpoint = '/api/pavement/detect-potholes';\n          break;\n        case 'cracks':\n          endpoint = '/api/pavement/detect-cracks';\n          break;\n        case 'kerbs':\n          endpoint = '/api/pavement/detect-kerbs';\n          break;\n        default:\n          endpoint = '/api/pavement/detect-all';\n      }\n      const results = [];\n      const filenames = Object.keys(imagePreviewsMap);\n\n      // Process each image sequentially and display immediately\n      for (let i = 0; i < filenames.length; i++) {\n        const filename = filenames[i];\n        const imageData = imagePreviewsMap[filename];\n        try {\n          // Update current image index to show which image is being processed\n          setCurrentImageIndex(i);\n\n          // Get coordinates for this specific image\n          const imageCoordinates = imageLocationMap[filename] || 'Not Available';\n\n          // Prepare request data\n          const requestData = {\n            image: imageData,\n            coordinates: imageCoordinates,\n            username: (user === null || user === void 0 ? void 0 : user.username) || 'Unknown',\n            role: (user === null || user === void 0 ? void 0 : user.role) || 'Unknown',\n            skip_road_classification: !roadClassificationEnabled\n          };\n\n          // Make API request\n          const response = await axios.post(endpoint, requestData);\n          if (response.data.success) {\n            var _response$data$classi2;\n            // Check if the image was actually processed (contains road) or just classified\n            const isProcessed = response.data.processed !== false;\n            const isRoad = ((_response$data$classi2 = response.data.classification) === null || _response$data$classi2 === void 0 ? void 0 : _response$data$classi2.is_road) || false;\n            if (isProcessed && isRoad) {\n              // Road image that was processed - display the results\n              setProcessedImage(response.data.processed_image);\n              setResults(response.data);\n            }\n\n            // Extract detailed detection results for table display\n            const detectionResults = {\n              potholes: response.data.potholes || [],\n              cracks: response.data.cracks || [],\n              kerbs: response.data.kerbs || []\n            };\n            results.push({\n              filename,\n              success: true,\n              processed: isProcessed,\n              isRoad: isRoad,\n              classification: response.data.classification,\n              processedImage: response.data.processed_image,\n              originalImage: imageData,\n              // Add original image data\n              data: response.data,\n              detectionResults: detectionResults,\n              detectionCounts: {\n                potholes: detectionResults.potholes.length,\n                cracks: detectionResults.cracks.length,\n                kerbs: detectionResults.kerbs.length,\n                total: detectionResults.potholes.length + detectionResults.cracks.length + detectionResults.kerbs.length\n              }\n            });\n          } else {\n            const errorMessage = response.data.message || 'Detection failed';\n            results.push({\n              filename,\n              success: false,\n              processed: false,\n              isRoad: false,\n              error: errorMessage,\n              isClassificationError: errorMessage.includes('No road detected')\n            });\n          }\n        } catch (error) {\n          var _error$response2, _error$response2$data;\n          const errorMessage = ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'An error occurred during detection';\n          results.push({\n            filename,\n            success: false,\n            processed: false,\n            isRoad: false,\n            error: errorMessage,\n            isClassificationError: errorMessage.includes('No road detected')\n          });\n        }\n\n        // Update progress\n        setProcessedCount(prev => prev + 1);\n\n        // Pause briefly to allow user to see the result before moving to next image\n        // Only pause if not on the last image\n        if (i < filenames.length - 1) {\n          await new Promise(resolve => setTimeout(resolve, 2000)); // 2 second pause\n        }\n      }\n\n      // Store final results\n      setBatchResults(results);\n\n      // After batch processing is complete, display the first successfully processed road image\n      const processedRoadImages = results.filter(r => r.success && r.processed && r.isRoad);\n      if (processedRoadImages.length > 0) {\n        const firstProcessedRoadImage = processedRoadImages[0];\n        setProcessedImage(firstProcessedRoadImage.processedImage);\n        setResults(firstProcessedRoadImage.data);\n\n        // Set the current image index to 0 (first processed road image)\n        setCurrentImageIndex(0);\n      } else {\n        // No road images were processed, clear the display\n        setProcessedImage(null);\n        setResults(null);\n        setCurrentImageIndex(0);\n      }\n\n      // Auto-clear uploaded image icons after processing is complete\n      // Store processed images data before clearing (for both road and non-road)\n      const processedData = {};\n      results.forEach(result => {\n        if (result.success) {\n          const originalImage = imagePreviewsMap[result.filename];\n          processedData[result.filename] = {\n            originalImage: originalImage,\n            processedImage: result.isRoad ? result.processedImage : null,\n            results: result.data,\n            isRoad: result.isRoad\n          };\n          console.log('Storing image data for:', result.filename, 'isRoad:', result.isRoad, 'hasOriginalImage:', !!originalImage);\n        }\n      });\n      setProcessedImagesData(prev => ({\n        ...prev,\n        ...processedData\n      }));\n\n      // Clear image previews and files but keep results\n      setImageFiles([]);\n      setImagePreviewsMap({});\n      setImageLocationMap({});\n      setCurrentImageIndex(0);\n\n      // Reset coordinates when clearing all images\n      setCoordinates('Not Available');\n      setLocationError('');\n      setLocationPermission('unknown');\n      if (fileInputRef.current) {\n        fileInputRef.current.value = '';\n      }\n    } catch (error) {\n      setError('Failed to process batch: ' + (error.message || 'Unknown error'));\n    } finally {\n      setBatchProcessing(false);\n    }\n  };\n\n  // Reset detection\n  const handleReset = () => {\n    setImageFiles([]);\n    setImagePreviewsMap({});\n    setImageLocationMap({});\n    setCurrentImageIndex(0);\n    setProcessedImage(null);\n    setResults(null);\n    setError('');\n    setBatchResults([]);\n    setProcessedCount(0);\n    setTotalToProcess(0);\n    setProcessedImagesData({});\n\n    // Reset coordinates when clearing all images\n    setCoordinates('Not Available');\n    setLocationError('');\n    setLocationPermission('unknown');\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n\n  // Add function to handle thumbnail clicks\n  const handleThumbnailClick = imageData => {\n    setSelectedImageData(imageData);\n    setShowImageModal(true);\n  };\n\n  // Add sorting function for detection results\n  const handleSort = key => {\n    let direction = 'asc';\n    if (sortConfig.key === key && sortConfig.direction === 'asc') {\n      direction = 'desc';\n    }\n    setSortConfig({\n      key,\n      direction\n    });\n  };\n\n  // Function to sort detection results\n  const sortDetections = detections => {\n    if (!sortConfig.key) return detections;\n    return [...detections].sort((a, b) => {\n      let aValue = a[sortConfig.key];\n      let bValue = b[sortConfig.key];\n\n      // Handle numeric values\n      if (typeof aValue === 'number' && typeof bValue === 'number') {\n        return sortConfig.direction === 'asc' ? aValue - bValue : bValue - aValue;\n      }\n\n      // Handle string values\n      if (typeof aValue === 'string' && typeof bValue === 'string') {\n        return sortConfig.direction === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);\n      }\n\n      // Handle null/undefined values\n      if (aValue == null && bValue == null) return 0;\n      if (aValue == null) return sortConfig.direction === 'asc' ? 1 : -1;\n      if (bValue == null) return sortConfig.direction === 'asc' ? -1 : 1;\n      return 0;\n    });\n  };\n\n  // Function to export detection results to CSV\n  const exportToCSV = () => {\n    // Flatten all detection results\n    const allDetections = [];\n    batchResults.forEach(result => {\n      if (result.success && result.processed && result.detectionResults) {\n        const {\n          potholes,\n          cracks,\n          kerbs\n        } = result.detectionResults;\n\n        // Add potholes\n        potholes.forEach(pothole => {\n          allDetections.push({\n            filename: result.filename,\n            type: 'Pothole',\n            id: pothole.pothole_id,\n            area_cm2: pothole.area_cm2,\n            depth_cm: pothole.depth_cm,\n            volume: pothole.volume,\n            volume_range: pothole.volume_range,\n            crack_type: '',\n            area_range: '',\n            kerb_type: '',\n            condition: '',\n            length_m: '',\n            confidence: pothole.confidence\n          });\n        });\n\n        // Add cracks\n        cracks.forEach(crack => {\n          allDetections.push({\n            filename: result.filename,\n            type: 'Crack',\n            id: crack.crack_id,\n            area_cm2: crack.area_cm2,\n            depth_cm: '',\n            volume: '',\n            volume_range: '',\n            crack_type: crack.crack_type,\n            area_range: crack.area_range,\n            kerb_type: '',\n            condition: '',\n            length_m: '',\n            confidence: crack.confidence\n          });\n        });\n\n        // Add kerbs\n        kerbs.forEach(kerb => {\n          allDetections.push({\n            filename: result.filename,\n            type: 'Kerb',\n            id: kerb.kerb_id,\n            area_cm2: '',\n            depth_cm: '',\n            volume: '',\n            volume_range: '',\n            crack_type: '',\n            area_range: '',\n            kerb_type: kerb.kerb_type,\n            condition: kerb.condition,\n            length_m: kerb.length_m,\n            confidence: kerb.confidence\n          });\n        });\n      }\n    });\n    if (allDetections.length === 0) {\n      alert('No detection results to export.');\n      return;\n    }\n\n    // Create CSV content\n    const headers = ['Image Filename', 'Detection Type', 'ID', 'Area (cm²)', 'Depth (cm)', 'Volume (cm³)', 'Volume Range', 'Crack Type', 'Area Range', 'Kerb Type', 'Condition', 'Length (m)', 'Confidence'];\n    const csvContent = [headers.join(','), ...allDetections.map(detection => [detection.filename, detection.type, detection.id || '', detection.area_cm2 || '', detection.depth_cm || '', detection.volume || '', detection.volume_range || '', detection.crack_type || '', detection.area_range || '', detection.kerb_type || '', detection.condition || '', detection.length_m || '', detection.confidence || ''].join(','))].join('\\n');\n\n    // Create and download file\n    const blob = new Blob([csvContent], {\n      type: 'text/csv;charset=utf-8;'\n    });\n    const link = document.createElement('a');\n    const url = URL.createObjectURL(blob);\n    link.setAttribute('href', url);\n    link.setAttribute('download', `pavement_detection_results_${new Date().toISOString().split('T')[0]}.csv`);\n    link.style.visibility = 'hidden';\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  };\n\n  // Handle location permission changes\n  useEffect(() => {\n    if (cameraActive && locationPermission === 'unknown') {\n      // Try to get location when camera is first activated\n      handleLocationRequest();\n    }\n  }, [cameraActive]);\n\n  // Listen for permission changes if supported\n  useEffect(() => {\n    let permissionWatcher = null;\n    const watchPermissions = async () => {\n      try {\n        if (navigator.permissions && navigator.permissions.query) {\n          const permission = await navigator.permissions.query({\n            name: 'geolocation'\n          });\n          permissionWatcher = () => {\n            setLocationPermission(permission.state);\n            if (permission.state === 'granted' && cameraActive && coordinates === 'Not Available') {\n              handleLocationRequest();\n            }\n          };\n          permission.addEventListener('change', permissionWatcher);\n        }\n      } catch (err) {\n        console.warn('Permission watching not supported:', err);\n      }\n    };\n    watchPermissions();\n    return () => {\n      if (permissionWatcher) {\n        try {\n          const permission = navigator.permissions.query({\n            name: 'geolocation'\n          });\n          permission.then(p => p.removeEventListener('change', permissionWatcher));\n        } catch (err) {\n          console.warn('Error removing permission listener:', err);\n        }\n      }\n    };\n  }, [cameraActive, coordinates]);\n\n  // Force re-render when current image changes to update location display\n  useEffect(() => {\n    // This effect ensures the UI updates when switching between images\n    // The getCurrentImageLocation function will return the correct location for the selected image\n  }, [currentImageIndex, imageLocationMap]);\n\n  // Helper to track if any images have been processed\n  const [hasProcessed, setHasProcessed] = useState(false);\n\n  // Update: Clear batchResults if toggle is changed after processing\n  const handleToggleRoadClassification = () => {\n    setRoadClassificationEnabled(prev => {\n      // If images have been processed, clear results when toggling\n      if (hasProcessed) {\n        setBatchResults([]);\n        setHasProcessed(false);\n      }\n      return !prev;\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    className: \"pavement-page\",\n    children: [/*#__PURE__*/_jsxDEV(Tabs, {\n      activeKey: activeTab,\n      onSelect: k => setActiveTab(k),\n      className: \"mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(Tab, {\n        eventKey: \"detection\",\n        title: \"Image Detection\",\n        children: [/*#__PURE__*/_jsxDEV(Card, {\n          className: \"mb-3\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            className: \"py-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                className: \"mb-1\",\n                children: \"Detection Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 936,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                value: detectionType,\n                onChange: e => setDetectionType(e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"all\",\n                  children: \"All (Potholes + Cracks + Kerbs)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 941,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"potholes\",\n                  children: \"Potholes\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 942,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"cracks\",\n                  children: \"Alligator Cracks\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 943,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"kerbs\",\n                  children: \"Kerbs\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 944,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 937,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 935,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex align-items-start gap-2 mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(OverlayTrigger, {\n                trigger: \"click\",\n                placement: \"right\",\n                overlay: reminderPopover,\n                rootClose: true,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"sticky-note-icon\",\n                  style: {\n                    cursor: 'pointer',\n                    display: 'inline-block'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: \"/remindericon.svg\",\n                    alt: \"Image Upload Guidelines\",\n                    style: {\n                      width: '28px',\n                      height: '28px'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 960,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 956,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 950,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"road-classification-control\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex align-items-center justify-content-between mb-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"me-2\",\n                    style: {\n                      fontSize: '0.9rem',\n                      fontWeight: '500',\n                      color: '#495057'\n                    },\n                    children: \"Road Classification\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 971,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(OverlayTrigger, {\n                    placement: \"right\",\n                    delay: {\n                      show: 200,\n                      hide: 100\n                    },\n                    overlay: /*#__PURE__*/_jsxDEV(Popover, {\n                      id: \"road-classification-detailed-info\",\n                      style: {\n                        maxWidth: '350px'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Popover.Header, {\n                        as: \"h3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"fas fa-brain me-2 text-primary\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 980,\n                          columnNumber: 31\n                        }, this), \"Road Classification Feature\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 979,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Popover.Body, {\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mb-2\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"mb-1\",\n                            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                              className: \"fas fa-toggle-on text-success me-2\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 986,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                              children: \"ENABLED (ON):\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 987,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 985,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            style: {\n                              fontSize: '12px',\n                              color: '#6c757d',\n                              marginLeft: '20px'\n                            },\n                            children: [\"\\u2022 AI analyzes images for road content first\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 990,\n                              columnNumber: 78\n                            }, this), \"\\u2022 Only road images get defect detection\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 991,\n                              columnNumber: 74\n                            }, this), \"\\u2022 More accurate results, slightly slower\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 989,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 984,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mb-2\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"mb-1\",\n                            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                              className: \"fas fa-toggle-off text-secondary me-2\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 998,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                              children: \"DISABLED (OFF):\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 999,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 997,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            style: {\n                              fontSize: '12px',\n                              color: '#6c757d',\n                              marginLeft: '20px'\n                            },\n                            children: [\"\\u2022 All images processed directly\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1002,\n                              columnNumber: 66\n                            }, this), \"\\u2022 No road verification step\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1003,\n                              columnNumber: 62\n                            }, this), \"\\u2022 Faster processing, may have false positives\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1001,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 996,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"alert alert-info py-2 px-2 mb-0\",\n                          style: {\n                            fontSize: '11px'\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                            className: \"fas fa-lightbulb me-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1009,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: \"Recommendation:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1010,\n                            columnNumber: 33\n                          }, this), \" Keep enabled for mixed image types. Disable only when all images contain roads and speed is priority.\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1008,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 983,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 978,\n                      columnNumber: 27\n                    }, this),\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"info-icon-wrapper\",\n                      children: /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"road-classification-info-icon\",\n                        style: {\n                          fontSize: '14px',\n                          cursor: 'help',\n                          color: '#007bff',\n                          display: 'inline-flex',\n                          alignItems: 'center',\n                          justifyContent: 'center',\n                          position: 'relative',\n                          zIndex: '1000',\n                          fontWeight: 'bold'\n                        },\n                        children: \"i\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1018,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1017,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 974,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 970,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex align-items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"toggle-switch me-2\",\n                    onClick: handleToggleRoadClassification,\n                    style: {\n                      width: '60px',\n                      height: '30px',\n                      backgroundColor: roadClassificationEnabled ? '#28a745' : '#6c757d',\n                      borderRadius: '15px',\n                      position: 'relative',\n                      cursor: 'pointer',\n                      transition: 'background-color 0.3s ease',\n                      border: '2px solid transparent'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"toggle-slider\",\n                      style: {\n                        width: '22px',\n                        height: '22px',\n                        backgroundColor: 'white',\n                        borderRadius: '50%',\n                        position: 'absolute',\n                        top: '2px',\n                        left: roadClassificationEnabled ? '34px' : '2px',\n                        transition: 'left 0.3s ease',\n                        boxShadow: '0 2px 4px rgba(0,0,0,0.2)'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1049,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        position: 'absolute',\n                        top: '50%',\n                        left: roadClassificationEnabled ? '8px' : '32px',\n                        transform: 'translateY(-50%)',\n                        fontSize: '10px',\n                        fontWeight: '600',\n                        color: 'white',\n                        transition: 'all 0.3s ease',\n                        userSelect: 'none'\n                      },\n                      children: roadClassificationEnabled ? 'ON' : 'OFF'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1063,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1035,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    style: {\n                      fontSize: '11px'\n                    },\n                    children: roadClassificationEnabled ? \"Only road images processed\" : \"All images processed\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1079,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1034,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 969,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 949,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Image Source\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1089,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex gap-2 mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  variant: cameraActive ? \"primary\" : \"outline-primary\",\n                  onClick: toggleCamera,\n                  disabled: locationLoading,\n                  children: locationLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                      as: \"span\",\n                      animation: \"border\",\n                      size: \"sm\",\n                      role: \"status\",\n                      \"aria-hidden\": \"true\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1098,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"ms-2\",\n                      children: \"Getting Location...\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1099,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true) : cameraActive ? \"Disable Camera\" : \"Enable Camera\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1091,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"file-input-container\",\n                  children: /*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"file-input-label\",\n                    children: [\"Upload Image\", /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"file\",\n                      className: \"file-input\",\n                      accept: \"image/*\",\n                      onChange: handleFileChange,\n                      ref: fileInputRef,\n                      disabled: cameraActive,\n                      multiple: true\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1108,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1106,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1105,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1090,\n                columnNumber: 17\n              }, this), cameraActive && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"location-status mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"text-muted\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Location Status:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1125,\n                    columnNumber: 23\n                  }, this), locationPermission === 'granted' && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-success ms-1\",\n                    children: \"\\u2713 Enabled\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1126,\n                    columnNumber: 60\n                  }, this), locationPermission === 'denied' && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-danger ms-1\",\n                    children: \"\\u2717 Denied\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1127,\n                    columnNumber: 59\n                  }, this), locationPermission === 'prompt' && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-warning ms-1\",\n                    children: \"\\u26A0 Requesting...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1128,\n                    columnNumber: 59\n                  }, this), locationPermission === 'unknown' && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-secondary ms-1\",\n                    children: \"? Unknown\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1129,\n                    columnNumber: 60\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1124,\n                  columnNumber: 21\n                }, this), (coordinates !== 'Not Available' || Object.keys(imagePreviewsMap).length > 0) && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Current Location:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1134,\n                      columnNumber: 27\n                    }, this), \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-primary\",\n                      children: coordinates\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1134,\n                      columnNumber: 62\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1133,\n                    columnNumber: 25\n                  }, this), Object.keys(imagePreviewsMap).length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-1\",\n                    children: /*#__PURE__*/_jsxDEV(\"small\", {\n                      className: \"text-muted\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Selected Image Location:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1139,\n                        columnNumber: 31\n                      }, this), \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-primary\",\n                        children: getCurrentImageLocation()\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1139,\n                        columnNumber: 73\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1138,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1137,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1132,\n                  columnNumber: 23\n                }, this), locationError && /*#__PURE__*/_jsxDEV(Alert, {\n                  variant: \"warning\",\n                  className: \"mt-2 mb-0\",\n                  style: {\n                    fontSize: '0.875rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Alert.Heading, {\n                    as: \"h6\",\n                    children: \"Location Access Issue\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1147,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      whiteSpace: 'pre-line'\n                    },\n                    children: locationError\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1148,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1149,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-flex justify-content-end\",\n                    children: /*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"outline-warning\",\n                      size: \"sm\",\n                      onClick: handleLocationRequest,\n                      children: \"Retry Location Access\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1151,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1150,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1146,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1123,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1088,\n              columnNumber: 15\n            }, this), cameraActive && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"webcam-container mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Webcam, {\n                audio: false,\n                ref: webcamRef,\n                screenshotFormat: \"image/jpeg\",\n                className: \"webcam\",\n                videoConstraints: {\n                  width: 640,\n                  height: 480,\n                  facingMode: cameraOrientation\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1163,\n                columnNumber: 19\n              }, this), isMobile && /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outline-secondary\",\n                onClick: toggleCameraOrientation,\n                className: \"mt-2 mb-2\",\n                size: \"sm\",\n                children: \"Rotate Camera\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1175,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"success\",\n                onClick: handleCapture,\n                className: \"mt-2\",\n                children: \"Capture Photo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1184,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1162,\n              columnNumber: 17\n            }, this), Object.keys(imagePreviewsMap).length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"image-preview-container mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                children: \"Previews\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1196,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"image-gallery\",\n                children: Object.entries(imagePreviewsMap).map(([name, preview], index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `image-thumbnail ${index === currentImageIndex ? 'selected' : ''}`,\n                  onClick: () => setCurrentImageIndex(index),\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: preview,\n                    alt: `Preview ${index + 1}`,\n                    className: \"img-thumbnail\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1204,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"btn btn-sm btn-danger remove-image\",\n                    onClick: e => {\n                      e.stopPropagation();\n                      const newFiles = imageFiles.filter((_, i) => i !== index);\n                      const newPreviewsMap = {\n                        ...imagePreviewsMap\n                      };\n                      const newLocationMap = {\n                        ...imageLocationMap\n                      };\n                      delete newPreviewsMap[name];\n                      delete newLocationMap[name];\n                      setImageFiles(newFiles);\n                      setImagePreviewsMap(newPreviewsMap);\n                      setImageLocationMap(newLocationMap);\n                      if (currentImageIndex >= newFiles.length) {\n                        setCurrentImageIndex(Math.max(0, newFiles.length - 1));\n                      }\n                    },\n                    children: \"\\xD7\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1209,\n                    columnNumber: 25\n                  }, this)]\n                }, name, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1199,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1197,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"current-image-preview\",\n                children: Object.values(imagePreviewsMap)[currentImageIndex] && /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: Object.values(imagePreviewsMap)[currentImageIndex],\n                  alt: \"Current Preview\",\n                  className: \"image-preview img-fluid\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1233,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1231,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1195,\n              columnNumber: 17\n            }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n              variant: \"danger\",\n              children: error\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1243,\n              columnNumber: 25\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex gap-2 mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: \"primary\",\n                onClick: handleProcess,\n                disabled: Object.keys(imagePreviewsMap).length === 0 || loading || batchProcessing,\n                children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                    as: \"span\",\n                    animation: \"border\",\n                    size: \"sm\",\n                    role: \"status\",\n                    \"aria-hidden\": \"true\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1253,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ms-2\",\n                    children: \"Detecting...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1254,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true) : `Detect Current Image`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1246,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"success\",\n                onClick: handleProcessAll,\n                disabled: Object.keys(imagePreviewsMap).length === 0 || loading || batchProcessing,\n                children: batchProcessing ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                    as: \"span\",\n                    animation: \"border\",\n                    size: \"sm\",\n                    role: \"status\",\n                    \"aria-hidden\": \"true\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1268,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ms-2\",\n                    children: [\"Processing \", processedCount, \"/\", totalToProcess]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1269,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true) : `Process All Images`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1261,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"secondary\",\n                onClick: handleReset,\n                disabled: loading || batchProcessing,\n                children: \"Reset\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1276,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1245,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 934,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 933,\n          columnNumber: 11\n        }, this), batchResults.some(result => {\n          var _result$detectionCoun;\n          return result.success && result.processed && ((_result$detectionCoun = result.detectionCounts) === null || _result$detectionCoun === void 0 ? void 0 : _result$detectionCoun.total) > 0;\n        }) && /*#__PURE__*/_jsxDEV(Card, {\n          className: \"mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-between align-items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"mb-0\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-table me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1294,\n                  columnNumber: 21\n                }, this), \"Detailed Detection Results\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1293,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex gap-2\",\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"success\",\n                  size: \"sm\",\n                  onClick: exportToCSV,\n                  title: \"Export results to CSV\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-download me-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1304,\n                    columnNumber: 23\n                  }, this), \"Export CSV\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1298,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1297,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1292,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1291,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-3\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex gap-2 flex-wrap\",\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  variant: detectionTableFilter === 'all' ? 'primary' : 'outline-primary',\n                  size: \"sm\",\n                  onClick: () => setDetectionTableFilter('all'),\n                  children: \"All Detections\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1314,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  variant: detectionTableFilter === 'potholes' ? 'danger' : 'outline-danger',\n                  size: \"sm\",\n                  onClick: () => setDetectionTableFilter('potholes'),\n                  children: \"Potholes Only\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1321,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  variant: detectionTableFilter === 'cracks' ? 'warning' : 'outline-warning',\n                  size: \"sm\",\n                  onClick: () => setDetectionTableFilter('cracks'),\n                  children: \"Cracks Only\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1328,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  variant: detectionTableFilter === 'kerbs' ? 'info' : 'outline-info',\n                  size: \"sm\",\n                  onClick: () => setDetectionTableFilter('kerbs'),\n                  children: \"Kerbs Only\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1335,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1313,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1312,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4 detection-summary-cards\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-md-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"card bg-danger text-white\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"card-body text-center py-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                        className: \"mb-1\",\n                        children: \"Total Potholes\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1351,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"mb-0\",\n                        children: batchResults.reduce((sum, result) => {\n                          var _result$detectionCoun2;\n                          return sum + (((_result$detectionCoun2 = result.detectionCounts) === null || _result$detectionCoun2 === void 0 ? void 0 : _result$detectionCoun2.potholes) || 0);\n                        }, 0)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1352,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1350,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1349,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1348,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-md-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"card bg-warning text-white\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"card-body text-center py-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                        className: \"mb-1\",\n                        children: \"Total Cracks\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1361,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"mb-0\",\n                        children: batchResults.reduce((sum, result) => {\n                          var _result$detectionCoun3;\n                          return sum + (((_result$detectionCoun3 = result.detectionCounts) === null || _result$detectionCoun3 === void 0 ? void 0 : _result$detectionCoun3.cracks) || 0);\n                        }, 0)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1362,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1360,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1359,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1358,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-md-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"card bg-info text-white\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"card-body text-center py-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                        className: \"mb-1\",\n                        children: \"Total Kerbs\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1371,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"mb-0\",\n                        children: batchResults.reduce((sum, result) => {\n                          var _result$detectionCoun4;\n                          return sum + (((_result$detectionCoun4 = result.detectionCounts) === null || _result$detectionCoun4 === void 0 ? void 0 : _result$detectionCoun4.kerbs) || 0);\n                        }, 0)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1372,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1370,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1369,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1368,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-md-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"card bg-success text-white\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"card-body text-center py-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                        className: \"mb-1\",\n                        children: \"Total Detections\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1381,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"mb-0\",\n                        children: batchResults.reduce((sum, result) => {\n                          var _result$detectionCoun5;\n                          return sum + (((_result$detectionCoun5 = result.detectionCounts) === null || _result$detectionCoun5 === void 0 ? void 0 : _result$detectionCoun5.total) || 0);\n                        }, 0)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1382,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1380,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1379,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1378,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1347,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1346,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"table-responsive detection-table-container\",\n              children: (() => {\n                // Flatten all detection results into a single array\n                const allDetections = [];\n                batchResults.forEach(result => {\n                  if (result.success && result.processed && result.detectionResults) {\n                    const {\n                      potholes,\n                      cracks,\n                      kerbs\n                    } = result.detectionResults;\n\n                    // Add potholes\n                    if (detectionTableFilter === 'all' || detectionTableFilter === 'potholes') {\n                      potholes.forEach(pothole => {\n                        allDetections.push({\n                          ...pothole,\n                          type: 'Pothole',\n                          filename: result.filename,\n                          detectionType: 'potholes'\n                        });\n                      });\n                    }\n\n                    // Add cracks\n                    if (detectionTableFilter === 'all' || detectionTableFilter === 'cracks') {\n                      cracks.forEach(crack => {\n                        allDetections.push({\n                          ...crack,\n                          type: 'Crack',\n                          filename: result.filename,\n                          detectionType: 'cracks'\n                        });\n                      });\n                    }\n\n                    // Add kerbs\n                    if (detectionTableFilter === 'all' || detectionTableFilter === 'kerbs') {\n                      kerbs.forEach(kerb => {\n                        allDetections.push({\n                          ...kerb,\n                          type: 'Kerb',\n                          filename: result.filename,\n                          detectionType: 'kerbs'\n                        });\n                      });\n                    }\n                  }\n                });\n                if (allDetections.length === 0) {\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center py-5\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-3\",\n                      children: /*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-search fa-3x text-muted\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1443,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1442,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n                      className: \"text-muted\",\n                      children: \"No detections found\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1445,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-muted mb-0\",\n                      children: detectionTableFilter === 'all' ? 'No defects were detected in the processed images.' : `No ${detectionTableFilter} were detected in the processed images.`\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1446,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1441,\n                    columnNumber: 27\n                  }, this);\n                }\n                return /*#__PURE__*/_jsxDEV(\"table\", {\n                  className: \"table table-striped table-bordered\",\n                  children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                    children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                        children: \"Original Image\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1459,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        children: \"Processed Image\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1460,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        style: {\n                          cursor: 'pointer'\n                        },\n                        onClick: () => handleSort('detectionType'),\n                        children: [\"Type \", sortConfig.key === 'detectionType' && /*#__PURE__*/_jsxDEV(\"i\", {\n                          className: `fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1466,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1461,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        children: \"ID\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1469,\n                        columnNumber: 31\n                      }, this), (detectionTableFilter === 'all' || detectionTableFilter === 'potholes') && /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                          style: {\n                            cursor: 'pointer'\n                          },\n                          onClick: () => handleSort('area_cm2'),\n                          children: [\"Area (cm\\xB2) \", sortConfig.key === 'area_cm2' && /*#__PURE__*/_jsxDEV(\"i\", {\n                            className: `fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1477,\n                            columnNumber: 39\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1472,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          style: {\n                            cursor: 'pointer'\n                          },\n                          onClick: () => handleSort('depth_cm'),\n                          children: [\"Depth (cm) \", sortConfig.key === 'depth_cm' && /*#__PURE__*/_jsxDEV(\"i\", {\n                            className: `fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1485,\n                            columnNumber: 39\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1480,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          style: {\n                            cursor: 'pointer'\n                          },\n                          onClick: () => handleSort('volume'),\n                          children: [\"Volume (cm\\xB3) \", sortConfig.key === 'volume' && /*#__PURE__*/_jsxDEV(\"i\", {\n                            className: `fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1493,\n                            columnNumber: 39\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1488,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          children: \"Volume Range\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1496,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true), (detectionTableFilter === 'all' || detectionTableFilter === 'cracks') && /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                          style: {\n                            cursor: 'pointer'\n                          },\n                          onClick: () => handleSort('crack_type'),\n                          children: [\"Crack Type \", sortConfig.key === 'crack_type' && /*#__PURE__*/_jsxDEV(\"i\", {\n                            className: `fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1506,\n                            columnNumber: 39\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1501,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          style: {\n                            cursor: 'pointer'\n                          },\n                          onClick: () => handleSort('area_cm2'),\n                          children: [\"Area (cm\\xB2) \", sortConfig.key === 'area_cm2' && /*#__PURE__*/_jsxDEV(\"i\", {\n                            className: `fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1514,\n                            columnNumber: 39\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1509,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          children: \"Area Range\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1517,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true), (detectionTableFilter === 'all' || detectionTableFilter === 'kerbs') && /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                          style: {\n                            cursor: 'pointer'\n                          },\n                          onClick: () => handleSort('kerb_type'),\n                          children: [\"Kerb Type \", sortConfig.key === 'kerb_type' && /*#__PURE__*/_jsxDEV(\"i\", {\n                            className: `fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1527,\n                            columnNumber: 39\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1522,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          style: {\n                            cursor: 'pointer'\n                          },\n                          onClick: () => handleSort('condition'),\n                          children: [\"Condition \", sortConfig.key === 'condition' && /*#__PURE__*/_jsxDEV(\"i\", {\n                            className: `fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1535,\n                            columnNumber: 39\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1530,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          style: {\n                            cursor: 'pointer'\n                          },\n                          onClick: () => handleSort('length_m'),\n                          children: [\"Length (m) \", sortConfig.key === 'length_m' && /*#__PURE__*/_jsxDEV(\"i\", {\n                            className: `fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1543,\n                            columnNumber: 39\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1538,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true), /*#__PURE__*/_jsxDEV(\"th\", {\n                        style: {\n                          cursor: 'pointer'\n                        },\n                        onClick: () => handleSort('confidence'),\n                        children: [\"Confidence \", sortConfig.key === 'confidence' && /*#__PURE__*/_jsxDEV(\"i\", {\n                          className: `fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1553,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1548,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1458,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1457,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                    children: sortDetections(allDetections).map((detection, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                        style: {\n                          width: '120px',\n                          textAlign: 'center'\n                        },\n                        children: (() => {\n                          const result = batchResults.find(r => r.filename === detection.filename);\n                          const originalImage = result === null || result === void 0 ? void 0 : result.originalImage;\n                          return originalImage ? /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"image-thumbnail-container\",\n                            onClick: () => handleThumbnailClick({\n                              originalImage: originalImage,\n                              processedImage: result === null || result === void 0 ? void 0 : result.processedImage,\n                              isRoad: result === null || result === void 0 ? void 0 : result.isRoad,\n                              filename: detection.filename\n                            }),\n                            title: \"Click to enlarge \\uD83D\\uDD0D\",\n                            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                              src: originalImage,\n                              alt: \"Original\",\n                              className: \"image-thumbnail\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1577,\n                              columnNumber: 41\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"thumbnail-overlay\",\n                              children: \"\\uD83D\\uDD0D\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1582,\n                              columnNumber: 41\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1567,\n                            columnNumber: 39\n                          }, this) : /*#__PURE__*/_jsxDEV(\"small\", {\n                            className: \"text-muted\",\n                            children: \"No image\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1587,\n                            columnNumber: 39\n                          }, this);\n                        })()\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1562,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        style: {\n                          width: '120px',\n                          textAlign: 'center'\n                        },\n                        children: (() => {\n                          const result = batchResults.find(r => r.filename === detection.filename);\n                          const processedImage = result === null || result === void 0 ? void 0 : result.processedImage;\n                          const originalImage = result === null || result === void 0 ? void 0 : result.originalImage;\n                          return processedImage && result !== null && result !== void 0 && result.isRoad ? /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"image-thumbnail-container\",\n                            onClick: () => handleThumbnailClick({\n                              originalImage: originalImage,\n                              processedImage: processedImage,\n                              isRoad: result.isRoad,\n                              filename: detection.filename\n                            }),\n                            title: \"Click to enlarge \\uD83D\\uDD0D\",\n                            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                              src: processedImage,\n                              alt: \"Processed\",\n                              className: \"image-thumbnail\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1610,\n                              columnNumber: 41\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"thumbnail-overlay\",\n                              children: \"\\uD83D\\uDD0D\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1615,\n                              columnNumber: 41\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1600,\n                            columnNumber: 39\n                          }, this) : /*#__PURE__*/_jsxDEV(\"small\", {\n                            className: \"text-muted\",\n                            children: \"No processed image\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1620,\n                            columnNumber: 39\n                          }, this);\n                        })()\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1593,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: `badge ${detection.detectionType === 'potholes' ? 'bg-danger' : detection.detectionType === 'cracks' ? 'bg-warning' : 'bg-info'}`,\n                          children: detection.type\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1625,\n                          columnNumber: 35\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1624,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: detection.pothole_id || detection.crack_id || detection.kerb_id || index + 1\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1632,\n                        columnNumber: 33\n                      }, this), (detectionTableFilter === 'all' || detectionTableFilter === 'potholes') && /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                          children: detection.area_cm2 ? detection.area_cm2.toFixed(2) : 'N/A'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1637,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          children: detection.depth_cm ? detection.depth_cm.toFixed(2) : 'N/A'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1638,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          children: detection.volume ? detection.volume.toFixed(2) : 'N/A'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1639,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          children: detection.volume_range || 'N/A'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1640,\n                          columnNumber: 37\n                        }, this)]\n                      }, void 0, true), (detectionTableFilter === 'all' || detectionTableFilter === 'cracks') && /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                          children: detection.crack_type || 'N/A'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1647,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          children: detection.area_cm2 ? detection.area_cm2.toFixed(2) : 'N/A'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1648,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          children: detection.area_range || 'N/A'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1649,\n                          columnNumber: 37\n                        }, this)]\n                      }, void 0, true), (detectionTableFilter === 'all' || detectionTableFilter === 'kerbs') && /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                          children: detection.kerb_type || 'N/A'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1656,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          children: detection.condition || 'N/A'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1657,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          children: detection.length_m ? detection.length_m.toFixed(2) : 'N/A'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1658,\n                          columnNumber: 37\n                        }, this)]\n                      }, void 0, true), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: detection.confidence ? (detection.confidence * 100).toFixed(1) + '%' : 'N/A'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1662,\n                        columnNumber: 33\n                      }, this)]\n                    }, `${detection.filename}-${detection.detectionType}-${index}`, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1560,\n                      columnNumber: 31\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1558,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1456,\n                  columnNumber: 25\n                }, this);\n              })()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1392,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1310,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1290,\n          columnNumber: 13\n        }, this), batchProcessing && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"batch-processing-status mt-3\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex align-items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"me-3\",\n              children: /*#__PURE__*/_jsxDEV(Spinner, {\n                animation: \"border\",\n                size: \"sm\",\n                role: \"status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1679,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1678,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                className: \"mb-1\",\n                children: [\"Processing images: \", processedCount, \"/\", totalToProcess]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1682,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"progress\",\n                style: {\n                  height: '10px'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"progress-bar\",\n                  role: \"progressbar\",\n                  style: {\n                    width: `${processedCount / totalToProcess * 100}%`\n                  },\n                  \"aria-valuenow\": processedCount,\n                  \"aria-valuemin\": \"0\",\n                  \"aria-valuemax\": totalToProcess\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1684,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1683,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1681,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1677,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1676,\n          columnNumber: 13\n        }, this), !batchProcessing && batchResults.length > 0 && (() => {\n          const totalImages = batchResults.length;\n          const successfulImages = batchResults.filter(r => r.success).length;\n          const failedImages = batchResults.filter(r => !r.success).length;\n          let alertVariant = 'light';\n          let alertClass = '';\n          if (roadClassificationEnabled) {\n            // When classification is enabled, use road/non-road logic\n            const nonRoadImages = batchResults.filter(r => !r.isRoad).length;\n            const nonRoadPercentage = totalImages > 0 ? nonRoadImages / totalImages * 100 : 0;\n            if (totalImages > 0) {\n              if (nonRoadPercentage === 0) {\n                // 100% road detection - Green\n                alertVariant = 'success';\n              } else if (nonRoadPercentage === 100) {\n                // 100% non-road detection - Red\n                alertVariant = 'danger';\n              } else {\n                // Combined detection (mixed results) - Light Orange\n                alertVariant = 'warning';\n                alertClass = 'summary-light-orange';\n              }\n            }\n          } else {\n            // When classification is disabled, use success/failure logic\n            if (failedImages === 0) {\n              // All successful - Green\n              alertVariant = 'success';\n            } else if (successfulImages === 0) {\n              // All failed - Red\n              alertVariant = 'danger';\n            } else {\n              // Mixed results - Warning\n              alertVariant = 'warning';\n            }\n          }\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"batch-complete-status mt-4\",\n            children: /*#__PURE__*/_jsxDEV(Alert, {\n              variant: alertVariant,\n              className: alertClass,\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-check-circle me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1746,\n                columnNumber: 19\n              }, this), \"Processed \", batchResults.length, \" images.\", roadClassificationEnabled ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [batchResults.filter(r => r.success && r.processed).length, \" road images processed,\", batchResults.filter(r => r.success && !r.processed).length, \" non-road images detected,\", batchResults.filter(r => !r.success).length, \" failed.\"]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [batchResults.filter(r => r.success).length, \" images processed successfully,\", batchResults.filter(r => !r.success).length, \" failed.\"]\n              }, void 0, true)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1745,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1744,\n            columnNumber: 15\n          }, this);\n        })(), !batchProcessing && batchResults.length > 0 && roadClassificationEnabled && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"image-status-table mt-4\",\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex justify-content-between align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                  className: \"mb-0\",\n                  children: \"Image Processing Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1771,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"filter-buttons\",\n                  children: [/*#__PURE__*/_jsxDEV(Button, {\n                    variant: imageFilter === 'all' ? 'primary' : 'outline-primary',\n                    size: \"sm\",\n                    className: \"me-2\",\n                    onClick: () => setImageFilter('all'),\n                    children: \"Show All Images\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1773,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    variant: imageFilter === 'road' ? 'success' : 'outline-success',\n                    size: \"sm\",\n                    className: \"me-2\",\n                    onClick: () => setImageFilter('road'),\n                    children: \"Show Only Road Images\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1781,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    variant: imageFilter === 'non-road' ? 'danger' : 'outline-danger',\n                    size: \"sm\",\n                    onClick: () => setImageFilter('non-road'),\n                    children: \"Show Only Non-Road Images\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1789,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1772,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1770,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1769,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"table-responsive\",\n                children: /*#__PURE__*/_jsxDEV(\"table\", {\n                  className: \"table table-striped\",\n                  children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                    children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                        children: \"Image\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1804,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        children: \"Detection Status\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1805,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1803,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1802,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                    children: batchResults.filter(result => {\n                      if (imageFilter === 'road') return result.isRoad;\n                      if (imageFilter === 'non-road') return !result.isRoad;\n                      return true; // 'all'\n                    }).map((result, index) => {\n                      const filename = result.filename;\n                      const isRoad = result.isRoad;\n\n                      // Get image from stored processed data\n                      let imagePreview = null;\n                      let imageData = null;\n                      if (processedImagesData[filename]) {\n                        // Use stored processed image data\n                        imagePreview = processedImagesData[filename].originalImage;\n                        imageData = processedImagesData[filename];\n                        console.log('Found stored data for:', filename, 'hasImage:', !!imagePreview);\n                      } else if (imagePreviewsMap[filename]) {\n                        // Fallback to current preview (for any remaining unprocessed images)\n                        imagePreview = imagePreviewsMap[filename];\n                        imageData = {\n                          originalImage: imagePreview,\n                          processedImage: null,\n                          results: null,\n                          isRoad: isRoad\n                        };\n                        console.log('Using fallback data for:', filename, 'hasImage:', !!imagePreview);\n                      } else {\n                        console.log('No image data found for:', filename);\n                      }\n                      return /*#__PURE__*/_jsxDEV(\"tr\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"d-flex align-items-center\",\n                            children: [imagePreview ? /*#__PURE__*/_jsxDEV(\"img\", {\n                              src: imagePreview,\n                              alt: `Thumbnail ${index + 1}`,\n                              className: \"img-thumbnail me-2\",\n                              style: {\n                                width: '60px',\n                                height: '60px',\n                                objectFit: 'cover',\n                                cursor: 'pointer'\n                              },\n                              onClick: () => handleThumbnailClick(imageData),\n                              title: \"Click to view full size\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1847,\n                              columnNumber: 39\n                            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"img-thumbnail me-2 d-flex align-items-center justify-content-center\",\n                              style: {\n                                width: '60px',\n                                height: '60px',\n                                backgroundColor: '#f8f9fa',\n                                border: '1px solid #dee2e6'\n                              },\n                              children: /*#__PURE__*/_jsxDEV(\"small\", {\n                                className: \"text-muted\",\n                                children: \"No Image\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1870,\n                                columnNumber: 41\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1861,\n                              columnNumber: 39\n                            }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                              className: \"text-muted\",\n                              children: filename\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1873,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1845,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1844,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          children: /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: `badge ${isRoad ? 'bg-success' : 'bg-danger'}`,\n                            children: isRoad ? 'Road' : 'Non-Road'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1877,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1876,\n                          columnNumber: 33\n                        }, this)]\n                      }, filename, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1843,\n                        columnNumber: 31\n                      }, this);\n                    })\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1808,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1801,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1800,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1799,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1768,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1767,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 932,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Tab, {\n        eventKey: \"video\",\n        title: \"Video Detection\",\n        children: /*#__PURE__*/_jsxDEV(VideoDefectDetection, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1898,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1897,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Tab, {\n        eventKey: \"information\",\n        title: \"Information\",\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"About Pavement Analysis\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1904,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"The Pavement Analysis module uses advanced computer vision to detect and analyze various types of pavement defects and features:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1905,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"1. Potholes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1910,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Potholes are bowl-shaped holes of various sizes in the road surface that can be a serious hazard to vehicles. The system detects potholes and calculates:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1911,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Area in square centimeters\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1916,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Depth in centimeters\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1917,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Volume\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1918,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Classification by size (Small, Medium, Large)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1919,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1915,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"2. Alligator Cracks\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1922,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Alligator cracks are a series of interconnected cracks creating a pattern resembling an alligator's scales. These indicate underlying structural weakness. The system identifies multiple types of cracks including:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1923,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Alligator Cracks\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1929,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Edge Cracks\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1930,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Hairline Cracks\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1931,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Longitudinal Cracks\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1932,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Transverse Cracks\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1933,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1928,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"3. Kerbs\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1936,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Kerbs are raised edges along a street or path that define boundaries between roadways and other areas. The system identifies different kerb conditions including:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1937,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Normal/Good Kerbs - Structurally sound and properly visible\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1942,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Faded Kerbs - Reduced visibility due to worn paint or weathering\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1943,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Damaged Kerbs - Physically damaged or broken kerbs requiring repair\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1944,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1941,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"Location Services & GPS Data\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1947,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"When using the live camera option, the application can capture GPS coordinates to provide precise geolocation data for detected defects. This helps in:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1948,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Accurately mapping defect locations\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1953,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Creating location-based reports\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1954,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Enabling field teams to find specific issues\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1955,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Tracking defect patterns by geographic area\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1956,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1952,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n              children: \"Location Requirements:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1959,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Secure Connection:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1961,\n                  columnNumber: 21\n                }, this), \" Location services require HTTPS\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1961,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Browser Permissions:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1962,\n                  columnNumber: 21\n                }, this), \" You must allow location access when prompted\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1962,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Safari Users:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1963,\n                  columnNumber: 21\n                }, this), \" Enable location services in Safari settings\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1963,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Mobile Devices:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1964,\n                  columnNumber: 21\n                }, this), \" Ensure location services are enabled in device settings\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1964,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1960,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"alert alert-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-info-circle me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1968,\n                  columnNumber: 21\n                }, this), \"Troubleshooting Location Issues\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1968,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"If location access is denied:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1969,\n                  columnNumber: 20\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1969,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                className: \"mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Safari:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1971,\n                    columnNumber: 23\n                  }, this), \" Settings \\u2192 Privacy & Security \\u2192 Location Services\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1971,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Chrome:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1972,\n                    columnNumber: 23\n                  }, this), \" Settings \\u2192 Privacy and security \\u2192 Site Settings \\u2192 Location\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1972,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Firefox:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1973,\n                    columnNumber: 23\n                  }, this), \" Settings \\u2192 Privacy & Security \\u2192 Permissions \\u2192 Location\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1973,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1970,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"On mobile devices:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1975,\n                  columnNumber: 20\n                }, this), \" Also check your device's location settings and ensure the browser has location permission.\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1975,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1967,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"How to Use This Module\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1978,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ol\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Select the detection type (Potholes, Alligator Cracks, or Kerbs)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1980,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Upload an image or use the camera to capture a photo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1981,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"If using the camera, allow location access when prompted for GPS coordinates\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1982,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Click the Detect button to analyze the image\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1983,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Review the detection results and measurements\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1984,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1979,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"The detected defects are automatically recorded in the database for tracking and analysis in the Dashboard module.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1987,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1903,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1902,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1901,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 927,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showImageModal,\n      onHide: () => setShowImageModal(false),\n      size: \"lg\",\n      centered: true,\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-image me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2047,\n            columnNumber: 13\n          }, this), \"Image View \", (selectedImageData === null || selectedImageData === void 0 ? void 0 : selectedImageData.filename) && /*#__PURE__*/_jsxDEV(\"small\", {\n            className: \"text-muted\",\n            children: [\"- \", selectedImageData.filename]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2049,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2046,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2045,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: selectedImageData && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-camera me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2058,\n                columnNumber: 19\n              }, this), \"Original Image\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2057,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n              src: selectedImageData.originalImage,\n              alt: \"Original Image\",\n              className: \"img-fluid\",\n              style: {\n                maxHeight: '400px',\n                borderRadius: '8px',\n                border: '2px solid #dee2e6',\n                boxShadow: '0 4px 8px rgba(0,0,0,0.1)'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2061,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2056,\n            columnNumber: 15\n          }, this), selectedImageData.processedImage && selectedImageData.isRoad && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-search me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2076,\n                columnNumber: 21\n              }, this), \"Processed Image (Detection Results)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2075,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n              src: selectedImageData.processedImage,\n              alt: \"Processed Image\",\n              className: \"img-fluid\",\n              style: {\n                maxHeight: '400px',\n                borderRadius: '8px',\n                border: '2px solid #28a745',\n                boxShadow: '0 4px 8px rgba(0,0,0,0.1)'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2079,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2074,\n            columnNumber: 17\n          }, this), !selectedImageData.isRoad && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-3\",\n            children: /*#__PURE__*/_jsxDEV(Alert, {\n              variant: \"info\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-info-circle me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2095,\n                columnNumber: 21\n              }, this), \"This image was classified as non-road and therefore no defect detection was performed.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2094,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2093,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2055,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2053,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => setShowImageModal(false),\n          children: \"Close\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2104,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2103,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2039,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 925,\n    columnNumber: 5\n  }, this);\n};\n\n// Add CSS styles for the enhanced detection table\n_s(Pavement, \"9309vVGWV8iTfPf2IpUInT38j6Y=\", false, function () {\n  return [useResponsive];\n});\n_c = Pavement;\nconst styles = `\n  .detection-table-container {\n    max-height: 600px;\n    overflow-y: auto;\n  }\n\n  .detection-table-container th {\n    position: sticky;\n    top: 0;\n    background-color: #f8f9fa;\n    z-index: 10;\n  }\n\n  .detection-table-container th:hover {\n    background-color: #e9ecef;\n  }\n\n  .detection-summary-cards .card {\n    transition: transform 0.2s ease-in-out;\n  }\n\n  .detection-summary-cards .card:hover {\n    transform: translateY(-2px);\n  }\n\n  .table-responsive {\n    border-radius: 8px;\n    box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n  }\n\n  .badge {\n    font-size: 0.75em;\n  }\n\n  @media (max-width: 768px) {\n    .detection-summary-cards .col-md-3 {\n      margin-bottom: 1rem;\n    }\n\n    .d-flex.gap-2.flex-wrap {\n      flex-direction: column;\n    }\n\n    .d-flex.gap-2.flex-wrap .btn {\n      margin-bottom: 0.5rem;\n    }\n  }\n`;\n\n// Inject styles into the document head\nif (typeof document !== 'undefined') {\n  const styleSheet = document.createElement('style');\n  styleSheet.innerText = styles;\n  document.head.appendChild(styleSheet);\n}\nexport default Pavement;\nvar _c;\n$RefreshReg$(_c, \"Pavement\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "Container", "Card", "<PERSON><PERSON>", "Form", "Tabs", "Tab", "<PERSON><PERSON>", "Spinner", "OverlayTrigger", "Popover", "Modal", "axios", "Webcam", "useResponsive", "VideoDefectDetection", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Pavement", "_s", "activeTab", "setActiveTab", "detectionType", "setDetectionType", "imageFiles", "setImageFiles", "imagePreviewsMap", "setImagePreviewsMap", "imageLocationMap", "setImageLocationMap", "currentImageIndex", "setCurrentImageIndex", "processedImage", "setProcessedImage", "results", "setResults", "loading", "setLoading", "error", "setError", "cameraActive", "setCameraActive", "coordinates", "setCoordinates", "cameraOrientation", "setCameraOrientation", "locationPermission", "setLocationPermission", "locationError", "setLocationError", "locationLoading", "setLocationLoading", "batchResults", "setBatchResults", "batchProcessing", "setBatchProcessing", "processedCount", "setProcessedCount", "processedImagesData", "setProcessedImagesData", "showClassificationModal", "setShowClassificationModal", "classificationError", "setClassificationError", "totalToProcess", "setTotalToProcess", "showImageModal", "setShowImageModal", "selectedImageData", "setSelectedImageData", "imageFilter", "setImageFilter", "roadClassificationEnabled", "setRoadClassificationEnabled", "detectionTableFilter", "setDetectionTableFilter", "sortConfig", "setSortConfig", "key", "direction", "webcamRef", "fileInputRef", "isMobile", "reminderPopover", "id", "style", "max<PERSON><PERSON><PERSON>", "children", "Header", "as", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Body", "marginBottom", "paddingLeft", "checkLocationPermission", "navigator", "permissions", "query", "permission", "name", "state", "err", "console", "warn", "requestLocation", "Promise", "resolve", "reject", "geolocation", "Error", "window", "isSecureContext", "options", "enableHighAccuracy", "timeout", "maximumAge", "getCurrentPosition", "position", "errorMessage", "code", "PERMISSION_DENIED", "POSITION_UNAVAILABLE", "TIMEOUT", "message", "handleLocationRequest", "permissionState", "errorMsg", "latitude", "longitude", "coords", "formattedCoords", "toFixed", "log", "accuracy", "includes", "handleFileChange", "e", "files", "Array", "from", "target", "length", "for<PERSON>ach", "file", "reader", "FileReader", "onloadend", "prev", "result", "readAsDataURL", "handleCapture", "imageSrc", "current", "getScreenshot", "timestamp", "Date", "toISOString", "filename", "captureCoordinates", "getCurrentImageLocation", "Object", "keys", "currentFilename", "toggleCamera", "newCameraState", "toggleCameraOrientation", "handleClassificationError", "handleProcess", "userString", "sessionStorage", "getItem", "user", "JSON", "parse", "currentImagePreview", "values", "imageCoordinates", "filenames", "requestData", "image", "username", "role", "skip_road_classification", "endpoint", "response", "post", "data", "success", "_response$data$classi", "isProcessed", "processed", "isRoad", "classification", "is_road", "processed_image", "detectionResults", "potholes", "cracks", "kerbs", "batchResult", "originalImage", "detectionCounts", "total", "value", "isClassificationError", "_error$response", "_error$response$data", "handleProcessAll", "i", "imageData", "_response$data$classi2", "push", "_error$response2", "_error$response2$data", "setTimeout", "processedRoadImages", "filter", "r", "firstProcessedRoadImage", "processedData", "handleReset", "handleThumbnailClick", "handleSort", "sortDetections", "detections", "sort", "a", "b", "aValue", "bValue", "localeCompare", "exportToCSV", "allDetections", "pothole", "type", "pothole_id", "area_cm2", "depth_cm", "volume", "volume_range", "crack_type", "area_range", "kerb_type", "condition", "length_m", "confidence", "crack", "crack_id", "kerb", "kerb_id", "alert", "headers", "csv<PERSON><PERSON>nt", "join", "map", "detection", "blob", "Blob", "link", "document", "createElement", "url", "URL", "createObjectURL", "setAttribute", "split", "visibility", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "permissionWatcher", "watchPermissions", "addEventListener", "then", "p", "removeEventListener", "hasProcessed", "setHasProcessed", "handleToggleRoadClassification", "className", "active<PERSON><PERSON>", "onSelect", "k", "eventKey", "title", "Group", "Label", "Select", "onChange", "trigger", "placement", "overlay", "rootClose", "cursor", "display", "src", "alt", "width", "height", "fontSize", "fontWeight", "color", "delay", "show", "hide", "marginLeft", "alignItems", "justifyContent", "zIndex", "onClick", "backgroundColor", "borderRadius", "transition", "border", "top", "left", "boxShadow", "transform", "userSelect", "variant", "disabled", "animation", "size", "accept", "ref", "multiple", "Heading", "whiteSpace", "audio", "screenshotFormat", "videoConstraints", "facingMode", "entries", "preview", "index", "stopPropagation", "newFiles", "_", "newPreviewsMap", "newLocationMap", "Math", "max", "some", "_result$detectionCoun", "reduce", "sum", "_result$detectionCoun2", "_result$detectionCoun3", "_result$detectionCoun4", "_result$detectionCoun5", "textAlign", "find", "totalImages", "successfulImages", "failedImages", "alertVariant", "alertClass", "nonRoadImages", "nonRoadPercentage", "imagePreview", "objectFit", "onHide", "centered", "closeButton", "Title", "maxHeight", "Footer", "_c", "styles", "styleSheet", "innerText", "head", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Deep Learning/LTAGIT/LTA/frontend/src/pages/Pavement.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>ton, Form, Tabs, Tab, <PERSON><PERSON>, Spinner, OverlayTrigger, Popover, Modal } from 'react-bootstrap';\nimport axios from 'axios';\nimport Webcam from 'react-webcam';\nimport './Pavement.css';\nimport useResponsive from '../hooks/useResponsive';\nimport VideoDefectDetection from '../components/VideoDefectDetection';\n\n\nconst Pavement = () => {\n  const [activeTab, setActiveTab] = useState('detection');\n  const [detectionType, setDetectionType] = useState('all');\n  const [imageFiles, setImageFiles] = useState([]);\n  const [imagePreviewsMap, setImagePreviewsMap] = useState({});\n  const [imageLocationMap, setImageLocationMap] = useState({});\n  const [currentImageIndex, setCurrentImageIndex] = useState(0);\n  const [processedImage, setProcessedImage] = useState(null);\n  const [results, setResults] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [cameraActive, setCameraActive] = useState(false);\n  const [coordinates, setCoordinates] = useState('Not Available');\n  const [cameraOrientation, setCameraOrientation] = useState('environment');\n  const [locationPermission, setLocationPermission] = useState('unknown');\n  const [locationError, setLocationError] = useState('');\n  const [locationLoading, setLocationLoading] = useState(false);\n  \n  // Add state for batch processing results\n  const [batchResults, setBatchResults] = useState([]);\n  const [batchProcessing, setBatchProcessing] = useState(false);\n  const [processedCount, setProcessedCount] = useState(0);\n  \n  // Add state for storing processed images for results table\n  const [processedImagesData, setProcessedImagesData] = useState({});\n\n  // Add state for classification error modal\n  const [showClassificationModal, setShowClassificationModal] = useState(false);\n  const [classificationError, setClassificationError] = useState('');\n  const [totalToProcess, setTotalToProcess] = useState(0);\n  \n  // Add state for image modal\n  const [showImageModal, setShowImageModal] = useState(false);\n  const [selectedImageData, setSelectedImageData] = useState(null);\n\n  // Add state for image status table filtering\n  const [imageFilter, setImageFilter] = useState('all'); // 'all', 'road', 'non-road'\n\n\n\n  // Add state for road classification toggle (default to false for better user experience)\n  const [roadClassificationEnabled, setRoadClassificationEnabled] = useState(false);\n  // Add state for enhanced detection results table\n  const [detectionTableFilter, setDetectionTableFilter] = useState('all'); // 'all', 'potholes', 'cracks', 'kerbs'\n  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });\n\n  // Auto-clear is always enabled - no toggle needed\n  \n  const webcamRef = useRef(null);\n  const fileInputRef = useRef(null);\n  const { isMobile } = useResponsive();\n\n  // Create the popover content\n  const reminderPopover = (\n    <Popover id=\"reminder-popover\" style={{ maxWidth: '300px' }}>\n      <Popover.Header as=\"h3\">📸 Image Upload Guidelines</Popover.Header>\n      <Popover.Body>\n        <p style={{ marginBottom: '10px' }}>\n          Please ensure your uploaded images are:\n        </p>\n        <ul style={{ marginBottom: '0', paddingLeft: '20px' }}>\n          <li>Focused directly on the road surface</li>\n          <li>Well-lit and clear</li>\n          <li>Showing the entire area of concern</li>\n          <li>Taken from a reasonable distance to capture context</li>\n        </ul>\n      </Popover.Body>\n    </Popover>\n  );\n\n  // Safari-compatible geolocation permission check\n  const checkLocationPermission = async () => {\n    if (!navigator.permissions || !navigator.permissions.query) {\n      // Fallback for older browsers\n      return 'prompt';\n    }\n    \n    try {\n      const permission = await navigator.permissions.query({ name: 'geolocation' });\n      return permission.state;\n    } catch (err) {\n      console.warn('Permission API not supported or failed:', err);\n      return 'prompt';\n    }\n  };\n\n  // Safari-compatible geolocation request\n  const requestLocation = () => {\n    return new Promise((resolve, reject) => {\n      // Check if geolocation is supported\n      if (!navigator.geolocation) {\n        reject(new Error('Geolocation is not supported by this browser'));\n        return;\n      }\n\n      // Check if we're in a secure context (HTTPS)\n      if (!window.isSecureContext) {\n        reject(new Error('Geolocation requires a secure context (HTTPS)'));\n        return;\n      }\n\n      const options = {\n        enableHighAccuracy: true,\n        timeout: 15000, // 15 seconds timeout\n        maximumAge: 60000 // Accept cached position up to 1 minute old\n      };\n\n      navigator.geolocation.getCurrentPosition(\n        (position) => {\n          resolve(position);\n        },\n        (error) => {\n          let errorMessage = 'Unable to retrieve location';\n          \n          switch (error.code) {\n            case error.PERMISSION_DENIED:\n              errorMessage = 'Location access denied. Please enable location permissions in your browser settings.';\n              break;\n            case error.POSITION_UNAVAILABLE:\n              errorMessage = 'Location information is unavailable. Please try again.';\n              break;\n            case error.TIMEOUT:\n              errorMessage = 'Location request timed out. Please try again.';\n              break;\n            default:\n              errorMessage = `Location error: ${error.message}`;\n              break;\n          }\n          \n          reject(new Error(errorMessage));\n        },\n        options\n      );\n    });\n  };\n\n  // Enhanced location handler with Safari-specific fixes\n  const handleLocationRequest = async () => {\n    setLocationLoading(true);\n    setLocationError('');\n    \n    try {\n      // First check permission state\n      const permissionState = await checkLocationPermission();\n      setLocationPermission(permissionState);\n      \n      // If permission is denied, provide user guidance\n      if (permissionState === 'denied') {\n        const errorMsg = 'Location access denied. To enable location access:\\n' +\n                        '• Safari: Settings > Privacy & Security > Location Services\\n' +\n                        '• Chrome: Settings > Privacy > Location\\n' +\n                        '• Firefox: Settings > Privacy > Location\\n' +\n                        'Then refresh this page and try again.';\n        setLocationError(errorMsg);\n        setCoordinates('Permission Denied');\n        return;\n      }\n      \n      // Request location\n      const position = await requestLocation();\n      const { latitude, longitude } = position.coords;\n      \n      // Format coordinates with better precision\n      const formattedCoords = `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;\n      setCoordinates(formattedCoords);\n      setLocationPermission('granted');\n      setLocationError('');\n      \n      console.log('Location acquired:', { latitude, longitude, accuracy: position.coords.accuracy });\n      \n    } catch (error) {\n      console.error('Location request failed:', error);\n      setLocationError(error.message);\n      setCoordinates('Location Error');\n      \n      // Update permission state based on error\n      if (error.message.includes('denied')) {\n        setLocationPermission('denied');\n      }\n    } finally {\n      setLocationLoading(false);\n    }\n  };\n\n  // Handle multiple file input change\n  const handleFileChange = (e) => {\n    const files = Array.from(e.target.files);\n    if (files.length > 0) {\n      setImageFiles([...imageFiles, ...files]);\n      \n      // Create previews and location data for each file\n      files.forEach(file => {\n        const reader = new FileReader();\n        reader.onloadend = () => {\n          setImagePreviewsMap(prev => ({\n            ...prev,\n            [file.name]: reader.result\n          }));\n        };\n        reader.readAsDataURL(file);\n        \n        // Store location as \"Not Available\" for uploaded files\n        setImageLocationMap(prev => ({\n          ...prev,\n          [file.name]: 'Not Available'\n        }));\n      });\n      \n      // Reset results\n      setProcessedImage(null);\n      setResults(null);\n      setError('');\n    }\n  };\n\n  // Handle camera capture with location validation\n  const handleCapture = async () => {\n    const imageSrc = webcamRef.current.getScreenshot();\n    if (imageSrc) {\n      // If we don't have location data, try to get it before capturing\n      if (coordinates === 'Not Available' || coordinates === 'Location Error') {\n        await handleLocationRequest();\n      }\n      \n      const timestamp = new Date().toISOString();\n      const filename = `camera_capture_${timestamp}.jpg`;\n      const captureCoordinates = coordinates; // Capture current coordinates\n      \n      setImageFiles([...imageFiles, filename]);\n      setImagePreviewsMap(prev => ({\n        ...prev,\n        [filename]: imageSrc\n      }));\n      setImageLocationMap(prev => ({\n        ...prev,\n        [filename]: captureCoordinates\n      }));\n      setCurrentImageIndex(imageFiles.length);\n      \n      setProcessedImage(null);\n      setResults(null);\n      setError('');\n      \n      // Log capture with current coordinates\n      console.log('Photo captured with coordinates:', captureCoordinates);\n    }\n  };\n\n  // Get location data for currently selected image\n  const getCurrentImageLocation = () => {\n    if (Object.keys(imagePreviewsMap).length === 0) {\n      return coordinates; // Use current coordinates if no images\n    }\n    \n    const currentFilename = Object.keys(imagePreviewsMap)[currentImageIndex];\n    return imageLocationMap[currentFilename] || 'Not Available';\n  };\n\n  // Toggle camera with improved location handling\n  const toggleCamera = async () => {\n    const newCameraState = !cameraActive;\n    setCameraActive(newCameraState);\n    \n    if (newCameraState) {\n      // Get location when camera is activated\n      await handleLocationRequest();\n    } else {\n      // Only reset location if no images are captured\n      // This preserves location data for captured images\n      if (Object.keys(imagePreviewsMap).length === 0) {\n        setCoordinates('Not Available');\n        setLocationError('');\n        setLocationPermission('unknown');\n      }\n    }\n  };\n\n  // Toggle camera orientation (front/back) for mobile devices\n  const toggleCameraOrientation = () => {\n    setCameraOrientation(prev => prev === 'environment' ? 'user' : 'environment');\n  };\n\n  // Helper function to handle classification errors\n  const handleClassificationError = (errorMessage) => {\n    setClassificationError(errorMessage);\n    setShowClassificationModal(true);\n    setError(''); // Clear general error since we're showing specific modal\n  };\n\n  // Process image for detection\n  const handleProcess = async () => {\n    setLoading(true);\n    setError('');\n\n    try {\n      // Get user info from session storage\n      const userString = sessionStorage.getItem('user');\n      const user = userString ? JSON.parse(userString) : null;\n\n      // Get the currently selected image\n      const currentImagePreview = Object.values(imagePreviewsMap)[currentImageIndex];\n\n      if (!currentImagePreview) {\n        setError('No image selected for processing');\n        setLoading(false);\n        return;\n      }\n\n      // Get coordinates for the current image\n      const imageCoordinates = getCurrentImageLocation();\n\n      // Get the current image filename\n      const filenames = Object.keys(imagePreviewsMap);\n      const currentFilename = filenames[currentImageIndex];\n\n      // Prepare request data\n      const requestData = {\n        image: currentImagePreview,\n        coordinates: imageCoordinates,\n        username: user?.username || 'Unknown',\n        role: user?.role || 'Unknown',\n        skip_road_classification: !roadClassificationEnabled\n      };\n\n      // Determine endpoint based on detection type\n      let endpoint;\n      switch(detectionType) {\n        case 'all':\n          endpoint = '/api/pavement/detect-all';\n          break;\n        case 'potholes':\n          endpoint = '/api/pavement/detect-potholes';\n          break;\n        case 'cracks':\n          endpoint = '/api/pavement/detect-cracks';\n          break;\n        case 'kerbs':\n          endpoint = '/api/pavement/detect-kerbs';\n          break;\n        default:\n          endpoint = '/api/pavement/detect-all';\n      }\n\n      // Make API request\n      const response = await axios.post(endpoint, requestData);\n\n      // Handle response\n      if (response.data.success) {\n        // Check if the image was actually processed (contains road) or just classified\n        const isProcessed = response.data.processed !== false;\n        const isRoad = response.data.classification?.is_road || false;\n\n        // Set the processed image and results for display\n        setProcessedImage(response.data.processed_image);\n        setResults(response.data);\n\n        // Extract detailed detection results for table display\n        const detectionResults = {\n          potholes: response.data.potholes || [],\n          cracks: response.data.cracks || [],\n          kerbs: response.data.kerbs || []\n        };\n\n        // Create batch result entry for the status table\n        const batchResult = {\n          filename: currentFilename,\n          success: true,\n          processed: isProcessed,\n          isRoad: isRoad,\n          classification: response.data.classification,\n          processedImage: response.data.processed_image,\n          originalImage: currentImagePreview, // Add original image data\n          data: response.data,\n          detectionResults: detectionResults,\n          detectionCounts: {\n            potholes: detectionResults.potholes.length,\n            cracks: detectionResults.cracks.length,\n            kerbs: detectionResults.kerbs.length,\n            total: detectionResults.potholes.length + detectionResults.cracks.length + detectionResults.kerbs.length\n          }\n        };\n\n        // Update batch results to show the status table\n        setBatchResults([batchResult]);\n\n        // Auto-clear uploaded image icons after successful single image processing\n        // Store the processed image data before clearing (for both road and non-road)\n        setProcessedImagesData(prev => ({\n          ...prev,\n          [currentFilename]: {\n            originalImage: currentImagePreview,\n            processedImage: isRoad ? response.data.processed_image : null,\n            results: response.data,\n            isRoad: isRoad\n          }\n        }));\n          \n          // Clear image previews and files but keep results\n          setImageFiles([]);\n          setImagePreviewsMap({});\n          setImageLocationMap({});\n          setCurrentImageIndex(0);\n          \n          // Reset coordinates when clearing all images\n          setCoordinates('Not Available');\n          setLocationError('');\n          setLocationPermission('unknown');\n          \n          if (fileInputRef.current) {\n            fileInputRef.current.value = '';\n          }\n      } else {\n        const errorMessage = response.data.message || 'Detection failed';\n\n        // Create batch result entry for failed processing\n        const batchResult = {\n          filename: currentFilename,\n          success: false,\n          processed: false,\n          isRoad: false,\n          error: errorMessage,\n          isClassificationError: errorMessage.includes('No road detected')\n        };\n\n        // Update batch results to show the status table\n        setBatchResults([batchResult]);\n\n        setError(errorMessage);\n      }\n    } catch (error) {\n      const errorMessage = error.response?.data?.message || 'An error occurred during detection. Please try again.';\n\n      // Get the current image filename for batch results\n      const filenames = Object.keys(imagePreviewsMap);\n      const currentFilename = filenames[currentImageIndex];\n\n      // Create batch result entry for error case\n      const batchResult = {\n        filename: currentFilename,\n        success: false,\n        processed: false,\n        isRoad: false,\n        error: errorMessage,\n        isClassificationError: errorMessage.includes('No road detected')\n      };\n\n      // Update batch results to show the status table\n      setBatchResults([batchResult]);\n\n      // Check if this is a classification error (no road detected)\n      if (errorMessage.includes('No road detected')) {\n        handleClassificationError(errorMessage);\n      } else {\n        setError(errorMessage);\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Add a new function to process all images\n  const handleProcessAll = async () => {\n    if (Object.keys(imagePreviewsMap).length === 0) {\n      setError('No images to process');\n      return;\n    }\n\n    setBatchProcessing(true);\n    setError('');\n    setBatchResults([]);\n    setProcessedCount(0);\n    setTotalToProcess(Object.keys(imagePreviewsMap).length);\n    \n    // Get user info from session storage\n    const userString = sessionStorage.getItem('user');\n    const user = userString ? JSON.parse(userString) : null;\n    \n    try {\n      // Determine endpoint based on detection type\n      let endpoint;\n      switch(detectionType) {\n        case 'all':\n          endpoint = '/api/pavement/detect-all';\n          break;\n        case 'potholes':\n          endpoint = '/api/pavement/detect-potholes';\n          break;\n        case 'cracks':\n          endpoint = '/api/pavement/detect-cracks';\n          break;\n        case 'kerbs':\n          endpoint = '/api/pavement/detect-kerbs';\n          break;\n        default:\n          endpoint = '/api/pavement/detect-all';\n      }\n      \n      const results = [];\n      const filenames = Object.keys(imagePreviewsMap);\n      \n      // Process each image sequentially and display immediately\n      for (let i = 0; i < filenames.length; i++) {\n        const filename = filenames[i];\n        const imageData = imagePreviewsMap[filename];\n        \n        try {\n          // Update current image index to show which image is being processed\n          setCurrentImageIndex(i);\n          \n          // Get coordinates for this specific image\n          const imageCoordinates = imageLocationMap[filename] || 'Not Available';\n          \n          // Prepare request data\n          const requestData = {\n            image: imageData,\n            coordinates: imageCoordinates,\n            username: user?.username || 'Unknown',\n            role: user?.role || 'Unknown',\n            skip_road_classification: !roadClassificationEnabled\n          };\n          \n          // Make API request\n          const response = await axios.post(endpoint, requestData);\n          \n          if (response.data.success) {\n            // Check if the image was actually processed (contains road) or just classified\n            const isProcessed = response.data.processed !== false;\n            const isRoad = response.data.classification?.is_road || false;\n\n            if (isProcessed && isRoad) {\n              // Road image that was processed - display the results\n              setProcessedImage(response.data.processed_image);\n              setResults(response.data);\n            }\n\n            // Extract detailed detection results for table display\n            const detectionResults = {\n              potholes: response.data.potholes || [],\n              cracks: response.data.cracks || [],\n              kerbs: response.data.kerbs || []\n            };\n\n            results.push({\n              filename,\n              success: true,\n              processed: isProcessed,\n              isRoad: isRoad,\n              classification: response.data.classification,\n              processedImage: response.data.processed_image,\n              originalImage: imageData, // Add original image data\n              data: response.data,\n              detectionResults: detectionResults,\n              detectionCounts: {\n                potholes: detectionResults.potholes.length,\n                cracks: detectionResults.cracks.length,\n                kerbs: detectionResults.kerbs.length,\n                total: detectionResults.potholes.length + detectionResults.cracks.length + detectionResults.kerbs.length\n              }\n            });\n          } else {\n            const errorMessage = response.data.message || 'Detection failed';\n            results.push({\n              filename,\n              success: false,\n              processed: false,\n              isRoad: false,\n              error: errorMessage,\n              isClassificationError: errorMessage.includes('No road detected')\n            });\n          }\n        } catch (error) {\n          const errorMessage = error.response?.data?.message || 'An error occurred during detection';\n          results.push({\n            filename,\n            success: false,\n            processed: false,\n            isRoad: false,\n            error: errorMessage,\n            isClassificationError: errorMessage.includes('No road detected')\n          });\n        }\n        \n        // Update progress\n        setProcessedCount(prev => prev + 1);\n        \n        // Pause briefly to allow user to see the result before moving to next image\n        // Only pause if not on the last image\n        if (i < filenames.length - 1) {\n          await new Promise(resolve => setTimeout(resolve, 2000)); // 2 second pause\n        }\n      }\n      \n      // Store final results\n      setBatchResults(results);\n\n      // After batch processing is complete, display the first successfully processed road image\n      const processedRoadImages = results.filter(r => r.success && r.processed && r.isRoad);\n      if (processedRoadImages.length > 0) {\n        const firstProcessedRoadImage = processedRoadImages[0];\n        setProcessedImage(firstProcessedRoadImage.processedImage);\n        setResults(firstProcessedRoadImage.data);\n\n        // Set the current image index to 0 (first processed road image)\n        setCurrentImageIndex(0);\n      } else {\n        // No road images were processed, clear the display\n        setProcessedImage(null);\n        setResults(null);\n        setCurrentImageIndex(0);\n      }\n\n      // Auto-clear uploaded image icons after processing is complete\n      // Store processed images data before clearing (for both road and non-road)\n      const processedData = {};\n      results.forEach(result => {\n        if (result.success) {\n          const originalImage = imagePreviewsMap[result.filename];\n          processedData[result.filename] = {\n            originalImage: originalImage,\n            processedImage: result.isRoad ? result.processedImage : null,\n            results: result.data,\n            isRoad: result.isRoad\n          };\n          console.log('Storing image data for:', result.filename, 'isRoad:', result.isRoad, 'hasOriginalImage:', !!originalImage);\n        }\n      });\n      setProcessedImagesData(prev => ({ ...prev, ...processedData }));\n      \n      // Clear image previews and files but keep results\n      setImageFiles([]);\n      setImagePreviewsMap({});\n      setImageLocationMap({});\n      setCurrentImageIndex(0);\n      \n      // Reset coordinates when clearing all images\n      setCoordinates('Not Available');\n      setLocationError('');\n      setLocationPermission('unknown');\n      \n      if (fileInputRef.current) {\n        fileInputRef.current.value = '';\n      }\n\n    } catch (error) {\n      setError('Failed to process batch: ' + (error.message || 'Unknown error'));\n    } finally {\n      setBatchProcessing(false);\n    }\n  };\n\n  // Reset detection\n  const handleReset = () => {\n    setImageFiles([]);\n    setImagePreviewsMap({});\n    setImageLocationMap({});\n    setCurrentImageIndex(0);\n    setProcessedImage(null);\n    setResults(null);\n    setError('');\n    setBatchResults([]);\n    setProcessedCount(0);\n    setTotalToProcess(0);\n    setProcessedImagesData({});\n    \n    // Reset coordinates when clearing all images\n    setCoordinates('Not Available');\n    setLocationError('');\n    setLocationPermission('unknown');\n    \n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n\n\n\n\n\n  // Add function to handle thumbnail clicks\n  const handleThumbnailClick = (imageData) => {\n    setSelectedImageData(imageData);\n    setShowImageModal(true);\n  };\n\n  // Add sorting function for detection results\n  const handleSort = (key) => {\n    let direction = 'asc';\n    if (sortConfig.key === key && sortConfig.direction === 'asc') {\n      direction = 'desc';\n    }\n    setSortConfig({ key, direction });\n  };\n\n  // Function to sort detection results\n  const sortDetections = (detections) => {\n    if (!sortConfig.key) return detections;\n\n    return [...detections].sort((a, b) => {\n      let aValue = a[sortConfig.key];\n      let bValue = b[sortConfig.key];\n\n      // Handle numeric values\n      if (typeof aValue === 'number' && typeof bValue === 'number') {\n        return sortConfig.direction === 'asc' ? aValue - bValue : bValue - aValue;\n      }\n\n      // Handle string values\n      if (typeof aValue === 'string' && typeof bValue === 'string') {\n        return sortConfig.direction === 'asc'\n          ? aValue.localeCompare(bValue)\n          : bValue.localeCompare(aValue);\n      }\n\n      // Handle null/undefined values\n      if (aValue == null && bValue == null) return 0;\n      if (aValue == null) return sortConfig.direction === 'asc' ? 1 : -1;\n      if (bValue == null) return sortConfig.direction === 'asc' ? -1 : 1;\n\n      return 0;\n    });\n  };\n\n  // Function to export detection results to CSV\n  const exportToCSV = () => {\n    // Flatten all detection results\n    const allDetections = [];\n\n    batchResults.forEach(result => {\n      if (result.success && result.processed && result.detectionResults) {\n        const { potholes, cracks, kerbs } = result.detectionResults;\n\n        // Add potholes\n        potholes.forEach(pothole => {\n          allDetections.push({\n            filename: result.filename,\n            type: 'Pothole',\n            id: pothole.pothole_id,\n            area_cm2: pothole.area_cm2,\n            depth_cm: pothole.depth_cm,\n            volume: pothole.volume,\n            volume_range: pothole.volume_range,\n            crack_type: '',\n            area_range: '',\n            kerb_type: '',\n            condition: '',\n            length_m: '',\n            confidence: pothole.confidence\n          });\n        });\n\n        // Add cracks\n        cracks.forEach(crack => {\n          allDetections.push({\n            filename: result.filename,\n            type: 'Crack',\n            id: crack.crack_id,\n            area_cm2: crack.area_cm2,\n            depth_cm: '',\n            volume: '',\n            volume_range: '',\n            crack_type: crack.crack_type,\n            area_range: crack.area_range,\n            kerb_type: '',\n            condition: '',\n            length_m: '',\n            confidence: crack.confidence\n          });\n        });\n\n        // Add kerbs\n        kerbs.forEach(kerb => {\n          allDetections.push({\n            filename: result.filename,\n            type: 'Kerb',\n            id: kerb.kerb_id,\n            area_cm2: '',\n            depth_cm: '',\n            volume: '',\n            volume_range: '',\n            crack_type: '',\n            area_range: '',\n            kerb_type: kerb.kerb_type,\n            condition: kerb.condition,\n            length_m: kerb.length_m,\n            confidence: kerb.confidence\n          });\n        });\n      }\n    });\n\n    if (allDetections.length === 0) {\n      alert('No detection results to export.');\n      return;\n    }\n\n    // Create CSV content\n    const headers = [\n      'Image Filename',\n      'Detection Type',\n      'ID',\n      'Area (cm²)',\n      'Depth (cm)',\n      'Volume (cm³)',\n      'Volume Range',\n      'Crack Type',\n      'Area Range',\n      'Kerb Type',\n      'Condition',\n      'Length (m)',\n      'Confidence'\n    ];\n\n    const csvContent = [\n      headers.join(','),\n      ...allDetections.map(detection => [\n        detection.filename,\n        detection.type,\n        detection.id || '',\n        detection.area_cm2 || '',\n        detection.depth_cm || '',\n        detection.volume || '',\n        detection.volume_range || '',\n        detection.crack_type || '',\n        detection.area_range || '',\n        detection.kerb_type || '',\n        detection.condition || '',\n        detection.length_m || '',\n        detection.confidence || ''\n      ].join(','))\n    ].join('\\n');\n\n    // Create and download file\n    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\n    const link = document.createElement('a');\n    const url = URL.createObjectURL(blob);\n    link.setAttribute('href', url);\n    link.setAttribute('download', `pavement_detection_results_${new Date().toISOString().split('T')[0]}.csv`);\n    link.style.visibility = 'hidden';\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  };\n\n\n\n\n\n  // Handle location permission changes\n  useEffect(() => {\n    if (cameraActive && locationPermission === 'unknown') {\n      // Try to get location when camera is first activated\n      handleLocationRequest();\n    }\n  }, [cameraActive]);\n\n  // Listen for permission changes if supported\n  useEffect(() => {\n    let permissionWatcher = null;\n    \n    const watchPermissions = async () => {\n      try {\n        if (navigator.permissions && navigator.permissions.query) {\n          const permission = await navigator.permissions.query({ name: 'geolocation' });\n          \n          permissionWatcher = () => {\n            setLocationPermission(permission.state);\n            if (permission.state === 'granted' && cameraActive && coordinates === 'Not Available') {\n              handleLocationRequest();\n            }\n          };\n          \n          permission.addEventListener('change', permissionWatcher);\n        }\n      } catch (err) {\n        console.warn('Permission watching not supported:', err);\n      }\n    };\n    \n    watchPermissions();\n    \n    return () => {\n      if (permissionWatcher) {\n        try {\n          const permission = navigator.permissions.query({ name: 'geolocation' });\n          permission.then(p => p.removeEventListener('change', permissionWatcher));\n        } catch (err) {\n          console.warn('Error removing permission listener:', err);\n        }\n      }\n    };\n  }, [cameraActive, coordinates]);\n\n  // Force re-render when current image changes to update location display\n  useEffect(() => {\n    // This effect ensures the UI updates when switching between images\n    // The getCurrentImageLocation function will return the correct location for the selected image\n  }, [currentImageIndex, imageLocationMap]);\n\n  // Helper to track if any images have been processed\n  const [hasProcessed, setHasProcessed] = useState(false);\n\n  // Update: Clear batchResults if toggle is changed after processing\n  const handleToggleRoadClassification = () => {\n    setRoadClassificationEnabled(prev => {\n      // If images have been processed, clear results when toggling\n      if (hasProcessed) {\n        setBatchResults([]);\n        setHasProcessed(false);\n      }\n      return !prev;\n    });\n  };\n\n\n  return (\n    <Container className=\"pavement-page\">\n      \n      <Tabs\n        activeKey={activeTab}\n        onSelect={(k) => setActiveTab(k)}\n        className=\"mb-3\"\n      >\n        <Tab eventKey=\"detection\" title=\"Image Detection\">\n          <Card className=\"mb-3\">\n            <Card.Body className=\"py-3\">\n              <Form.Group className=\"mb-3\">\n                <Form.Label className=\"mb-1\">Detection Type</Form.Label>\n                <Form.Select \n                  value={detectionType}\n                  onChange={(e) => setDetectionType(e.target.value)}\n                >\n                  <option value=\"all\">All (Potholes + Cracks + Kerbs)</option>\n                  <option value=\"potholes\">Potholes</option>\n                  <option value=\"cracks\">Alligator Cracks</option>\n                  <option value=\"kerbs\">Kerbs</option>\n                </Form.Select>\n              </Form.Group>\n\n              {/* Sticky note reminder and road classification toggle */}\n              <div className=\"d-flex align-items-start gap-2 mb-3\">\n                <OverlayTrigger\n                  trigger=\"click\"\n                  placement=\"right\"\n                  overlay={reminderPopover}\n                  rootClose\n                >\n                  <div\n                    className=\"sticky-note-icon\"\n                    style={{ cursor: 'pointer', display: 'inline-block' }}\n                  >\n                    <img\n                      src=\"/remindericon.svg\"\n                      alt=\"Image Upload Guidelines\"\n                      style={{ width: '28px', height: '28px' }}\n                    />\n                  </div>\n                </OverlayTrigger>\n\n                {/* Road Classification Toggle - Improved Design */}\n                <div className=\"road-classification-control\">\n                  <div className=\"d-flex align-items-center justify-content-between mb-1\">\n                    <span className=\"me-2\" style={{ fontSize: '0.9rem', fontWeight: '500', color: '#495057' }}>\n                      Road Classification\n                    </span>\n                      <OverlayTrigger\n                        placement=\"right\"\n                        delay={{ show: 200, hide: 100 }}\n                        overlay={\n                          <Popover id=\"road-classification-detailed-info\" style={{ maxWidth: '350px' }}>\n                            <Popover.Header as=\"h3\">\n                              <i className=\"fas fa-brain me-2 text-primary\"></i>\n                              Road Classification Feature\n                            </Popover.Header>\n                            <Popover.Body>\n                              <div className=\"mb-2\">\n                                <div className=\"mb-1\">\n                                  <i className=\"fas fa-toggle-on text-success me-2\"></i>\n                                  <strong>ENABLED (ON):</strong>\n                                </div>\n                                <div style={{ fontSize: '12px', color: '#6c757d', marginLeft: '20px' }}>\n                                  • AI analyzes images for road content first<br/>\n                                  • Only road images get defect detection<br/>\n                                  • More accurate results, slightly slower\n                                </div>\n                              </div>\n\n                              <div className=\"mb-2\">\n                                <div className=\"mb-1\">\n                                  <i className=\"fas fa-toggle-off text-secondary me-2\"></i>\n                                  <strong>DISABLED (OFF):</strong>\n                                </div>\n                                <div style={{ fontSize: '12px', color: '#6c757d', marginLeft: '20px' }}>\n                                  • All images processed directly<br/>\n                                  • No road verification step<br/>\n                                  • Faster processing, may have false positives\n                                </div>\n                              </div>\n\n                              <div className=\"alert alert-info py-2 px-2 mb-0\" style={{ fontSize: '11px' }}>\n                                <i className=\"fas fa-lightbulb me-1\"></i>\n                                <strong>Recommendation:</strong> Keep enabled for mixed image types.\n                                Disable only when all images contain roads and speed is priority.\n                              </div>\n                            </Popover.Body>\n                          </Popover>\n                        }\n                      >\n                        <span className=\"info-icon-wrapper\">\n                          <span className=\"road-classification-info-icon\"\n                             style={{\n                               fontSize: '14px',\n                               cursor: 'help',\n                               color: '#007bff',\n                               display: 'inline-flex',\n                               alignItems: 'center',\n                               justifyContent: 'center',\n                               position: 'relative',\n                               zIndex: '1000',\n                               fontWeight: 'bold'\n                             }}\n                          >i</span>\n                        </span>\n                      </OverlayTrigger>\n                    </div>\n                    <div className=\"d-flex align-items-center\">\n                      <div\n                        className=\"toggle-switch me-2\"\n                        onClick={handleToggleRoadClassification}\n                        style={{\n                          width: '60px',\n                          height: '30px',\n                          backgroundColor: roadClassificationEnabled ? '#28a745' : '#6c757d',\n                          borderRadius: '15px',\n                          position: 'relative',\n                          cursor: 'pointer',\n                          transition: 'background-color 0.3s ease',\n                          border: '2px solid transparent'\n                        }}\n                      >\n                        <div\n                          className=\"toggle-slider\"\n                          style={{\n                            width: '22px',\n                            height: '22px',\n                            backgroundColor: 'white',\n                            borderRadius: '50%',\n                            position: 'absolute',\n                            top: '2px',\n                            left: roadClassificationEnabled ? '34px' : '2px',\n                            transition: 'left 0.3s ease',\n                            boxShadow: '0 2px 4px rgba(0,0,0,0.2)'\n                          }}\n                        />\n                        <span\n                          style={{\n                            position: 'absolute',\n                            top: '50%',\n                            left: roadClassificationEnabled ? '8px' : '32px',\n                            transform: 'translateY(-50%)',\n                            fontSize: '10px',\n                            fontWeight: '600',\n                            color: 'white',\n                            transition: 'all 0.3s ease',\n                            userSelect: 'none'\n                          }}\n                        >\n                          {roadClassificationEnabled ? 'ON' : 'OFF'}\n                        </span>\n                      </div>\n                      <small className=\"text-muted\" style={{ fontSize: '11px' }}>\n                        {roadClassificationEnabled ? \"Only road images processed\" : \"All images processed\"}\n                      </small>\n                    </div>\n                  </div>\n                  \n\n                </div>\n\n              <div className=\"mb-3\">\n                <Form.Label>Image Source</Form.Label>\n                <div className=\"d-flex gap-2 mb-2\">\n                  <Button \n                    variant={cameraActive ? \"primary\" : \"outline-primary\"}\n                    onClick={toggleCamera}\n                    disabled={locationLoading}\n                  >\n                    {locationLoading ? (\n                      <>\n                        <Spinner as=\"span\" animation=\"border\" size=\"sm\" role=\"status\" aria-hidden=\"true\" />\n                        <span className=\"ms-2\">Getting Location...</span>\n                      </>\n                    ) : (\n                      cameraActive ? \"Disable Camera\" : \"Enable Camera\"\n                    )}\n                  </Button>\n                  <div className=\"file-input-container\">\n                    <label className=\"file-input-label\">\n                      Upload Image\n                      <input\n                        type=\"file\"\n                        className=\"file-input\"\n                        accept=\"image/*\"\n                        onChange={handleFileChange}\n                        ref={fileInputRef}\n                        disabled={cameraActive}\n                        multiple\n                      />\n                    </label>\n                  </div>\n                </div>\n                \n                {/* Location Status Display */}\n                {cameraActive && (\n                  <div className=\"location-status mb-3\">\n                    <small className=\"text-muted\">\n                      <strong>Location Status:</strong> \n                      {locationPermission === 'granted' && <span className=\"text-success ms-1\">✓ Enabled</span>}\n                      {locationPermission === 'denied' && <span className=\"text-danger ms-1\">✗ Denied</span>}\n                      {locationPermission === 'prompt' && <span className=\"text-warning ms-1\">⚠ Requesting...</span>}\n                      {locationPermission === 'unknown' && <span className=\"text-secondary ms-1\">? Unknown</span>}\n                    </small>\n                    {(coordinates !== 'Not Available' || Object.keys(imagePreviewsMap).length > 0) && (\n                      <div className=\"mt-1\">\n                        <small className=\"text-muted\">\n                          <strong>Current Location:</strong> <span className=\"text-primary\">{coordinates}</span>\n                        </small>\n                        {Object.keys(imagePreviewsMap).length > 0 && (\n                          <div className=\"mt-1\">\n                            <small className=\"text-muted\">\n                              <strong>Selected Image Location:</strong> <span className=\"text-primary\">{getCurrentImageLocation()}</span>\n                            </small>\n                          </div>\n                        )}\n                      </div>\n                    )}\n                    {locationError && (\n                      <Alert variant=\"warning\" className=\"mt-2 mb-0\" style={{ fontSize: '0.875rem' }}>\n                        <Alert.Heading as=\"h6\">Location Access Issue</Alert.Heading>\n                        <div style={{ whiteSpace: 'pre-line' }}>{locationError}</div>\n                        <hr />\n                        <div className=\"d-flex justify-content-end\">\n                          <Button variant=\"outline-warning\" size=\"sm\" onClick={handleLocationRequest}>\n                            Retry Location Access\n                          </Button>\n                        </div>\n                      </Alert>\n                    )}\n                  </div>\n                )}\n              </div>\n\n              {cameraActive && (\n                <div className=\"webcam-container mb-3\">\n                  <Webcam\n                    audio={false}\n                    ref={webcamRef}\n                    screenshotFormat=\"image/jpeg\"\n                    className=\"webcam\"\n                    videoConstraints={{\n                      width: 640,\n                      height: 480,\n                      facingMode: cameraOrientation\n                    }}\n                  />\n                  {isMobile && (\n                    <Button \n                      variant=\"outline-secondary\" \n                      onClick={toggleCameraOrientation}\n                      className=\"mt-2 mb-2\"\n                      size=\"sm\"\n                    >\n                      Rotate Camera\n                    </Button>\n                  )}\n                  <Button \n                    variant=\"success\" \n                    onClick={handleCapture}\n                    className=\"mt-2\"\n                  >\n                    Capture Photo\n                  </Button>\n                </div>\n              )}\n\n              {Object.keys(imagePreviewsMap).length > 0 && (\n                <div className=\"image-preview-container mb-3\">\n                  <h5>Previews</h5>\n                  <div className=\"image-gallery\">\n                    {Object.entries(imagePreviewsMap).map(([name, preview], index) => (\n                      <div \n                        key={name} \n                        className={`image-thumbnail ${index === currentImageIndex ? 'selected' : ''}`}\n                        onClick={() => setCurrentImageIndex(index)}\n                      >\n                        <img \n                          src={preview} \n                          alt={`Preview ${index + 1}`} \n                          className=\"img-thumbnail\" \n                        />\n                        <button \n                          className=\"btn btn-sm btn-danger remove-image\" \n                          onClick={(e) => {\n                            e.stopPropagation();\n                            const newFiles = imageFiles.filter((_, i) => i !== index);\n                            const newPreviewsMap = {...imagePreviewsMap};\n                            const newLocationMap = {...imageLocationMap};\n                            delete newPreviewsMap[name];\n                            delete newLocationMap[name];\n                            setImageFiles(newFiles);\n                            setImagePreviewsMap(newPreviewsMap);\n                            setImageLocationMap(newLocationMap);\n                            if (currentImageIndex >= newFiles.length) {\n                              setCurrentImageIndex(Math.max(0, newFiles.length - 1));\n                            }\n                          }}\n                        >\n                          ×\n                        </button>\n                      </div>\n                    ))}\n                  </div>\n                  <div className=\"current-image-preview\">\n                    {Object.values(imagePreviewsMap)[currentImageIndex] && (\n                      <img \n                        src={Object.values(imagePreviewsMap)[currentImageIndex]} \n                        alt=\"Current Preview\" \n                        className=\"image-preview img-fluid\" \n                      />\n                    )}\n                  </div>\n                </div>\n              )}\n\n              {error && <Alert variant=\"danger\">{error}</Alert>}\n\n              <div className=\"d-flex gap-2 mb-3\">\n                <Button \n                  variant=\"primary\" \n                  onClick={handleProcess}\n                  disabled={Object.keys(imagePreviewsMap).length === 0 || loading || batchProcessing}\n                >\n                  {loading ? (\n                    <>\n                      <Spinner as=\"span\" animation=\"border\" size=\"sm\" role=\"status\" aria-hidden=\"true\" />\n                      <span className=\"ms-2\">Detecting...</span>\n                    </>\n                  ) : (\n                    `Detect Current Image`\n                  )}\n                </Button>\n                \n                <Button \n                  variant=\"success\" \n                  onClick={handleProcessAll}\n                  disabled={Object.keys(imagePreviewsMap).length === 0 || loading || batchProcessing}\n                >\n                  {batchProcessing ? (\n                    <>\n                      <Spinner as=\"span\" animation=\"border\" size=\"sm\" role=\"status\" aria-hidden=\"true\" />\n                      <span className=\"ms-2\">Processing {processedCount}/{totalToProcess}</span>\n                    </>\n                  ) : (\n                    `Process All Images`\n                  )}\n                </Button>\n                \n                <Button \n                  variant=\"secondary\" \n                  onClick={handleReset}\n                  disabled={loading || batchProcessing}\n                >\n                  Reset\n                </Button>\n              </div>\n            </Card.Body>\n          </Card>\n\n\n          {/* Enhanced Detection Results Table */}\n          {batchResults.some(result => result.success && result.processed && result.detectionCounts?.total > 0) && (\n            <Card className=\"mb-4\">\n              <Card.Header>\n                <div className=\"d-flex justify-content-between align-items-center\">\n                  <h5 className=\"mb-0\">\n                    <i className=\"fas fa-table me-2\"></i>\n                    Detailed Detection Results\n                  </h5>\n                  <div className=\"d-flex gap-2\">\n                    <Button\n                      variant=\"success\"\n                      size=\"sm\"\n                      onClick={exportToCSV}\n                      title=\"Export results to CSV\"\n                    >\n                      <i className=\"fas fa-download me-1\"></i>\n                      Export CSV\n                    </Button>\n                  </div>\n                </div>\n              </Card.Header>\n              <Card.Body>\n                  {/* Filter Controls */}\n                  <div className=\"mb-3\">\n                    <div className=\"d-flex gap-2 flex-wrap\">\n                      <Button\n                        variant={detectionTableFilter === 'all' ? 'primary' : 'outline-primary'}\n                        size=\"sm\"\n                        onClick={() => setDetectionTableFilter('all')}\n                      >\n                        All Detections\n                      </Button>\n                      <Button\n                        variant={detectionTableFilter === 'potholes' ? 'danger' : 'outline-danger'}\n                        size=\"sm\"\n                        onClick={() => setDetectionTableFilter('potholes')}\n                      >\n                        Potholes Only\n                      </Button>\n                      <Button\n                        variant={detectionTableFilter === 'cracks' ? 'warning' : 'outline-warning'}\n                        size=\"sm\"\n                        onClick={() => setDetectionTableFilter('cracks')}\n                      >\n                        Cracks Only\n                      </Button>\n                      <Button\n                        variant={detectionTableFilter === 'kerbs' ? 'info' : 'outline-info'}\n                        size=\"sm\"\n                        onClick={() => setDetectionTableFilter('kerbs')}\n                      >\n                        Kerbs Only\n                      </Button>\n                    </div>\n                  </div>\n\n                  {/* Summary Statistics */}\n                  <div className=\"mb-4 detection-summary-cards\">\n                    <div className=\"row\">\n                      <div className=\"col-md-3\">\n                        <div className=\"card bg-danger text-white\">\n                          <div className=\"card-body text-center py-2\">\n                            <h6 className=\"mb-1\">Total Potholes</h6>\n                            <h4 className=\"mb-0\">\n                              {batchResults.reduce((sum, result) => sum + (result.detectionCounts?.potholes || 0), 0)}\n                            </h4>\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"col-md-3\">\n                        <div className=\"card bg-warning text-white\">\n                          <div className=\"card-body text-center py-2\">\n                            <h6 className=\"mb-1\">Total Cracks</h6>\n                            <h4 className=\"mb-0\">\n                              {batchResults.reduce((sum, result) => sum + (result.detectionCounts?.cracks || 0), 0)}\n                            </h4>\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"col-md-3\">\n                        <div className=\"card bg-info text-white\">\n                          <div className=\"card-body text-center py-2\">\n                            <h6 className=\"mb-1\">Total Kerbs</h6>\n                            <h4 className=\"mb-0\">\n                              {batchResults.reduce((sum, result) => sum + (result.detectionCounts?.kerbs || 0), 0)}\n                            </h4>\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"col-md-3\">\n                        <div className=\"card bg-success text-white\">\n                          <div className=\"card-body text-center py-2\">\n                            <h6 className=\"mb-1\">Total Detections</h6>\n                            <h4 className=\"mb-0\">\n                              {batchResults.reduce((sum, result) => sum + (result.detectionCounts?.total || 0), 0)}\n                            </h4>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Detailed Results Table */}\n                  <div className=\"table-responsive detection-table-container\">\n                    {(() => {\n                      // Flatten all detection results into a single array\n                      const allDetections = [];\n\n                      batchResults.forEach(result => {\n                        if (result.success && result.processed && result.detectionResults) {\n                          const { potholes, cracks, kerbs } = result.detectionResults;\n\n                          // Add potholes\n                          if (detectionTableFilter === 'all' || detectionTableFilter === 'potholes') {\n                            potholes.forEach(pothole => {\n                              allDetections.push({\n                                ...pothole,\n                                type: 'Pothole',\n                                filename: result.filename,\n                                detectionType: 'potholes'\n                              });\n                            });\n                          }\n\n                          // Add cracks\n                          if (detectionTableFilter === 'all' || detectionTableFilter === 'cracks') {\n                            cracks.forEach(crack => {\n                              allDetections.push({\n                                ...crack,\n                                type: 'Crack',\n                                filename: result.filename,\n                                detectionType: 'cracks'\n                              });\n                            });\n                          }\n\n                          // Add kerbs\n                          if (detectionTableFilter === 'all' || detectionTableFilter === 'kerbs') {\n                            kerbs.forEach(kerb => {\n                              allDetections.push({\n                                ...kerb,\n                                type: 'Kerb',\n                                filename: result.filename,\n                                detectionType: 'kerbs'\n                              });\n                            });\n                          }\n                        }\n                      });\n\n                      if (allDetections.length === 0) {\n                        return (\n                          <div className=\"text-center py-5\">\n                            <div className=\"mb-3\">\n                              <i className=\"fas fa-search fa-3x text-muted\"></i>\n                            </div>\n                            <h6 className=\"text-muted\">No detections found</h6>\n                            <p className=\"text-muted mb-0\">\n                              {detectionTableFilter === 'all'\n                                ? 'No defects were detected in the processed images.'\n                                : `No ${detectionTableFilter} were detected in the processed images.`}\n                            </p>\n                          </div>\n                        );\n                      }\n\n                      return (\n                        <table className=\"table table-striped table-bordered\">\n                          <thead>\n                            <tr>\n                              <th>Original Image</th>\n                              <th>Processed Image</th>\n                              <th\n                                style={{ cursor: 'pointer' }}\n                                onClick={() => handleSort('detectionType')}\n                              >\n                                Type {sortConfig.key === 'detectionType' && (\n                                  <i className={`fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`}></i>\n                                )}\n                              </th>\n                              <th>ID</th>\n                              {(detectionTableFilter === 'all' || detectionTableFilter === 'potholes') && (\n                                <>\n                                  <th\n                                    style={{ cursor: 'pointer' }}\n                                    onClick={() => handleSort('area_cm2')}\n                                  >\n                                    Area (cm²) {sortConfig.key === 'area_cm2' && (\n                                      <i className={`fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`}></i>\n                                    )}\n                                  </th>\n                                  <th\n                                    style={{ cursor: 'pointer' }}\n                                    onClick={() => handleSort('depth_cm')}\n                                  >\n                                    Depth (cm) {sortConfig.key === 'depth_cm' && (\n                                      <i className={`fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`}></i>\n                                    )}\n                                  </th>\n                                  <th\n                                    style={{ cursor: 'pointer' }}\n                                    onClick={() => handleSort('volume')}\n                                  >\n                                    Volume (cm³) {sortConfig.key === 'volume' && (\n                                      <i className={`fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`}></i>\n                                    )}\n                                  </th>\n                                  <th>Volume Range</th>\n                                </>\n                              )}\n                              {(detectionTableFilter === 'all' || detectionTableFilter === 'cracks') && (\n                                <>\n                                  <th\n                                    style={{ cursor: 'pointer' }}\n                                    onClick={() => handleSort('crack_type')}\n                                  >\n                                    Crack Type {sortConfig.key === 'crack_type' && (\n                                      <i className={`fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`}></i>\n                                    )}\n                                  </th>\n                                  <th\n                                    style={{ cursor: 'pointer' }}\n                                    onClick={() => handleSort('area_cm2')}\n                                  >\n                                    Area (cm²) {sortConfig.key === 'area_cm2' && (\n                                      <i className={`fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`}></i>\n                                    )}\n                                  </th>\n                                  <th>Area Range</th>\n                                </>\n                              )}\n                              {(detectionTableFilter === 'all' || detectionTableFilter === 'kerbs') && (\n                                <>\n                                  <th\n                                    style={{ cursor: 'pointer' }}\n                                    onClick={() => handleSort('kerb_type')}\n                                  >\n                                    Kerb Type {sortConfig.key === 'kerb_type' && (\n                                      <i className={`fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`}></i>\n                                    )}\n                                  </th>\n                                  <th\n                                    style={{ cursor: 'pointer' }}\n                                    onClick={() => handleSort('condition')}\n                                  >\n                                    Condition {sortConfig.key === 'condition' && (\n                                      <i className={`fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`}></i>\n                                    )}\n                                  </th>\n                                  <th\n                                    style={{ cursor: 'pointer' }}\n                                    onClick={() => handleSort('length_m')}\n                                  >\n                                    Length (m) {sortConfig.key === 'length_m' && (\n                                      <i className={`fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`}></i>\n                                    )}\n                                  </th>\n                                </>\n                              )}\n                              <th\n                                style={{ cursor: 'pointer' }}\n                                onClick={() => handleSort('confidence')}\n                              >\n                                Confidence {sortConfig.key === 'confidence' && (\n                                  <i className={`fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`}></i>\n                                )}\n                              </th>\n                            </tr>\n                          </thead>\n                          <tbody>\n                            {sortDetections(allDetections).map((detection, index) => (\n                              <tr key={`${detection.filename}-${detection.detectionType}-${index}`}>\n                                {/* Original Image Column */}\n                                <td style={{ width: '120px', textAlign: 'center' }}>\n                                  {(() => {\n                                    const result = batchResults.find(r => r.filename === detection.filename);\n                                    const originalImage = result?.originalImage;\n                                    return originalImage ? (\n                                      <div\n                                        className=\"image-thumbnail-container\"\n                                        onClick={() => handleThumbnailClick({\n                                          originalImage: originalImage,\n                                          processedImage: result?.processedImage,\n                                          isRoad: result?.isRoad,\n                                          filename: detection.filename\n                                        })}\n                                        title=\"Click to enlarge 🔍\"\n                                      >\n                                        <img\n                                          src={originalImage}\n                                          alt=\"Original\"\n                                          className=\"image-thumbnail\"\n                                        />\n                                        <div className=\"thumbnail-overlay\">\n                                          🔍\n                                        </div>\n                                      </div>\n                                    ) : (\n                                      <small className=\"text-muted\">No image</small>\n                                    );\n                                  })()}\n                                </td>\n\n                                {/* Processed Image Column */}\n                                <td style={{ width: '120px', textAlign: 'center' }}>\n                                  {(() => {\n                                    const result = batchResults.find(r => r.filename === detection.filename);\n                                    const processedImage = result?.processedImage;\n                                    const originalImage = result?.originalImage;\n\n                                    return processedImage && result?.isRoad ? (\n                                      <div\n                                        className=\"image-thumbnail-container\"\n                                        onClick={() => handleThumbnailClick({\n                                          originalImage: originalImage,\n                                          processedImage: processedImage,\n                                          isRoad: result.isRoad,\n                                          filename: detection.filename\n                                        })}\n                                        title=\"Click to enlarge 🔍\"\n                                      >\n                                        <img\n                                          src={processedImage}\n                                          alt=\"Processed\"\n                                          className=\"image-thumbnail\"\n                                        />\n                                        <div className=\"thumbnail-overlay\">\n                                          🔍\n                                        </div>\n                                      </div>\n                                    ) : (\n                                      <small className=\"text-muted\">No processed image</small>\n                                    );\n                                  })()}\n                                </td>\n                                <td>\n                                  <span className={`badge ${\n                                    detection.detectionType === 'potholes' ? 'bg-danger' :\n                                    detection.detectionType === 'cracks' ? 'bg-warning' : 'bg-info'\n                                  }`}>\n                                    {detection.type}\n                                  </span>\n                                </td>\n                                <td>{detection.pothole_id || detection.crack_id || detection.kerb_id || index + 1}</td>\n\n                                {/* Pothole-specific columns */}\n                                {(detectionTableFilter === 'all' || detectionTableFilter === 'potholes') && (\n                                  <>\n                                    <td>{detection.area_cm2 ? detection.area_cm2.toFixed(2) : 'N/A'}</td>\n                                    <td>{detection.depth_cm ? detection.depth_cm.toFixed(2) : 'N/A'}</td>\n                                    <td>{detection.volume ? detection.volume.toFixed(2) : 'N/A'}</td>\n                                    <td>{detection.volume_range || 'N/A'}</td>\n                                  </>\n                                )}\n\n                                {/* Crack-specific columns */}\n                                {(detectionTableFilter === 'all' || detectionTableFilter === 'cracks') && (\n                                  <>\n                                    <td>{detection.crack_type || 'N/A'}</td>\n                                    <td>{detection.area_cm2 ? detection.area_cm2.toFixed(2) : 'N/A'}</td>\n                                    <td>{detection.area_range || 'N/A'}</td>\n                                  </>\n                                )}\n\n                                {/* Kerb-specific columns */}\n                                {(detectionTableFilter === 'all' || detectionTableFilter === 'kerbs') && (\n                                  <>\n                                    <td>{detection.kerb_type || 'N/A'}</td>\n                                    <td>{detection.condition || 'N/A'}</td>\n                                    <td>{detection.length_m ? detection.length_m.toFixed(2) : 'N/A'}</td>\n                                  </>\n                                )}\n\n                                <td>{detection.confidence ? (detection.confidence * 100).toFixed(1) + '%' : 'N/A'}</td>\n                              </tr>\n                            ))}\n                          </tbody>\n                        </table>\n                      );\n                    })()}\n                  </div>\n                </Card.Body>\n            </Card>\n          )}\n\n          {/* Batch processing status indicator */}\n          {batchProcessing && (\n            <div className=\"batch-processing-status mt-3\">\n              <div className=\"d-flex align-items-center\">\n                <div className=\"me-3\">\n                  <Spinner animation=\"border\" size=\"sm\" role=\"status\" />\n                </div>\n                <div>\n                  <h6 className=\"mb-1\">Processing images: {processedCount}/{totalToProcess}</h6>\n                  <div className=\"progress\" style={{ height: '10px' }}>\n                    <div \n                      className=\"progress-bar\" \n                      role=\"progressbar\" \n                      style={{ width: `${(processedCount / totalToProcess) * 100}%` }}\n                      aria-valuenow={processedCount}\n                      aria-valuemin=\"0\" \n                      aria-valuemax={totalToProcess}\n                    />\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n\n\n\n\n\n          {/* Batch Processing Summary */}\n          {!batchProcessing && batchResults.length > 0 && (() => {\n            const totalImages = batchResults.length;\n            const successfulImages = batchResults.filter(r => r.success).length;\n            const failedImages = batchResults.filter(r => !r.success).length;\n\n            let alertVariant = 'light';\n            let alertClass = '';\n\n            if (roadClassificationEnabled) {\n              // When classification is enabled, use road/non-road logic\n              const nonRoadImages = batchResults.filter(r => !r.isRoad).length;\n              const nonRoadPercentage = totalImages > 0 ? (nonRoadImages / totalImages) * 100 : 0;\n\n              if (totalImages > 0) {\n                if (nonRoadPercentage === 0) {\n                  // 100% road detection - Green\n                  alertVariant = 'success';\n                } else if (nonRoadPercentage === 100) {\n                  // 100% non-road detection - Red\n                  alertVariant = 'danger';\n                } else {\n                  // Combined detection (mixed results) - Light Orange\n                  alertVariant = 'warning';\n                  alertClass = 'summary-light-orange';\n                }\n              }\n            } else {\n              // When classification is disabled, use success/failure logic\n              if (failedImages === 0) {\n                // All successful - Green\n                alertVariant = 'success';\n              } else if (successfulImages === 0) {\n                // All failed - Red\n                alertVariant = 'danger';\n              } else {\n                // Mixed results - Warning\n                alertVariant = 'warning';\n              }\n            }\n\n            return (\n              <div className=\"batch-complete-status mt-4\">\n                <Alert variant={alertVariant} className={alertClass}>\n                  <i className=\"fas fa-check-circle me-2\"></i>\n                  Processed {batchResults.length} images.\n                  {roadClassificationEnabled ? (\n                    <>\n                      {batchResults.filter(r => r.success && r.processed).length} road images processed,\n                      {batchResults.filter(r => r.success && !r.processed).length} non-road images detected,\n                      {batchResults.filter(r => !r.success).length} failed.\n                    </>\n                  ) : (\n                    <>\n                      {batchResults.filter(r => r.success).length} images processed successfully,\n                      {batchResults.filter(r => !r.success).length} failed.\n                    </>\n                  )}\n                </Alert>\n              </div>\n            );\n          })()}\n\n          {/* Image Status Table - Only show when road classification is enabled */}\n          {!batchProcessing && batchResults.length > 0 && roadClassificationEnabled && (\n            <div className=\"image-status-table mt-4\">\n              <Card>\n                <Card.Header>\n                  <div className=\"d-flex justify-content-between align-items-center\">\n                    <h5 className=\"mb-0\">Image Processing Status</h5>\n                    <div className=\"filter-buttons\">\n                      <Button\n                        variant={imageFilter === 'all' ? 'primary' : 'outline-primary'}\n                        size=\"sm\"\n                        className=\"me-2\"\n                        onClick={() => setImageFilter('all')}\n                      >\n                        Show All Images\n                      </Button>\n                      <Button\n                        variant={imageFilter === 'road' ? 'success' : 'outline-success'}\n                        size=\"sm\"\n                        className=\"me-2\"\n                        onClick={() => setImageFilter('road')}\n                      >\n                        Show Only Road Images\n                      </Button>\n                      <Button\n                        variant={imageFilter === 'non-road' ? 'danger' : 'outline-danger'}\n                        size=\"sm\"\n                        onClick={() => setImageFilter('non-road')}\n                      >\n                        Show Only Non-Road Images\n                      </Button>\n                    </div>\n                  </div>\n                </Card.Header>\n                <Card.Body>\n                  <div className=\"table-responsive\">\n                    <table className=\"table table-striped\">\n                      <thead>\n                        <tr>\n                          <th>Image</th>\n                          <th>Detection Status</th>\n                        </tr>\n                      </thead>\n                      <tbody>\n                        {batchResults\n                          .filter(result => {\n                            if (imageFilter === 'road') return result.isRoad;\n                            if (imageFilter === 'non-road') return !result.isRoad;\n                            return true; // 'all'\n                          })\n                          .map((result, index) => {\n                            const filename = result.filename;\n                            const isRoad = result.isRoad;\n                            \n                            // Get image from stored processed data\n                            let imagePreview = null;\n                            let imageData = null;\n                            \n                            if (processedImagesData[filename]) {\n                              // Use stored processed image data\n                              imagePreview = processedImagesData[filename].originalImage;\n                              imageData = processedImagesData[filename];\n                              console.log('Found stored data for:', filename, 'hasImage:', !!imagePreview);\n                            } else if (imagePreviewsMap[filename]) {\n                              // Fallback to current preview (for any remaining unprocessed images)\n                              imagePreview = imagePreviewsMap[filename];\n                              imageData = {\n                                originalImage: imagePreview,\n                                processedImage: null,\n                                results: null,\n                                isRoad: isRoad\n                              };\n                              console.log('Using fallback data for:', filename, 'hasImage:', !!imagePreview);\n                            } else {\n                              console.log('No image data found for:', filename);\n                            }\n\n                            return (\n                              <tr key={filename}>\n                                <td>\n                                  <div className=\"d-flex align-items-center\">\n                                    {imagePreview ? (\n                                      <img\n                                        src={imagePreview}\n                                        alt={`Thumbnail ${index + 1}`}\n                                        className=\"img-thumbnail me-2\"\n                                        style={{ \n                                          width: '60px', \n                                          height: '60px', \n                                          objectFit: 'cover',\n                                          cursor: 'pointer'\n                                        }}\n                                        onClick={() => handleThumbnailClick(imageData)}\n                                        title=\"Click to view full size\"\n                                      />\n                                    ) : (\n                                      <div \n                                        className=\"img-thumbnail me-2 d-flex align-items-center justify-content-center\"\n                                        style={{ \n                                          width: '60px', \n                                          height: '60px', \n                                          backgroundColor: '#f8f9fa',\n                                          border: '1px solid #dee2e6'\n                                        }}\n                                      >\n                                        <small className=\"text-muted\">No Image</small>\n                                      </div>\n                                    )}\n                                    <small className=\"text-muted\">{filename}</small>\n                                  </div>\n                                </td>\n                                <td>\n                                  <span className={`badge ${isRoad ? 'bg-success' : 'bg-danger'}`}>\n                                    {isRoad ? 'Road' : 'Non-Road'}\n                                  </span>\n                                </td>\n                              </tr>\n                            );\n                          })}\n                      </tbody>\n                    </table>\n                  </div>\n\n\n\n\n                </Card.Body>\n              </Card>\n            </div>\n          )}\n        </Tab>\n        \n        <Tab eventKey=\"video\" title=\"Video Detection\">\n          <VideoDefectDetection />\n        </Tab>\n        \n        <Tab eventKey=\"information\" title=\"Information\">\n          <Card>\n            <Card.Body>\n              <h4>About Pavement Analysis</h4>\n              <p>\n                The Pavement Analysis module uses advanced computer vision to detect and analyze \n                various types of pavement defects and features:\n              </p>\n              \n              <h5>1. Potholes</h5>\n              <p>\n                Potholes are bowl-shaped holes of various sizes in the road surface that can be a \n                serious hazard to vehicles. The system detects potholes and calculates:\n              </p>\n              <ul>\n                <li>Area in square centimeters</li>\n                <li>Depth in centimeters</li>\n                <li>Volume</li>\n                <li>Classification by size (Small, Medium, Large)</li>\n              </ul>\n              \n              <h5>2. Alligator Cracks</h5>\n              <p>\n                Alligator cracks are a series of interconnected cracks creating a pattern resembling \n                an alligator's scales. These indicate underlying structural weakness. The system \n                identifies multiple types of cracks including:\n              </p>\n              <ul>\n                <li>Alligator Cracks</li>\n                <li>Edge Cracks</li>\n                <li>Hairline Cracks</li>\n                <li>Longitudinal Cracks</li>\n                <li>Transverse Cracks</li>\n              </ul>\n              \n              <h5>3. Kerbs</h5>\n              <p>\n                Kerbs are raised edges along a street or path that define boundaries between roadways \n                and other areas. The system identifies different kerb conditions including:\n              </p>\n              <ul>\n                <li>Normal/Good Kerbs - Structurally sound and properly visible</li>\n                <li>Faded Kerbs - Reduced visibility due to worn paint or weathering</li>\n                <li>Damaged Kerbs - Physically damaged or broken kerbs requiring repair</li>\n              </ul>\n              \n              <h5>Location Services & GPS Data</h5>\n              <p>\n                When using the live camera option, the application can capture GPS coordinates \n                to provide precise geolocation data for detected defects. This helps in:\n              </p>\n              <ul>\n                <li>Accurately mapping defect locations</li>\n                <li>Creating location-based reports</li>\n                <li>Enabling field teams to find specific issues</li>\n                <li>Tracking defect patterns by geographic area</li>\n              </ul>\n              \n              <h6>Location Requirements:</h6>\n              <ul>\n                <li><strong>Secure Connection:</strong> Location services require HTTPS</li>\n                <li><strong>Browser Permissions:</strong> You must allow location access when prompted</li>\n                <li><strong>Safari Users:</strong> Enable location services in Safari settings</li>\n                <li><strong>Mobile Devices:</strong> Ensure location services are enabled in device settings</li>\n              </ul>\n              \n              <div className=\"alert alert-info\">\n                <h6><i className=\"fas fa-info-circle me-2\"></i>Troubleshooting Location Issues</h6>\n                <p><strong>If location access is denied:</strong></p>\n                <ul className=\"mb-2\">\n                  <li><strong>Safari:</strong> Settings → Privacy & Security → Location Services</li>\n                  <li><strong>Chrome:</strong> Settings → Privacy and security → Site Settings → Location</li>\n                  <li><strong>Firefox:</strong> Settings → Privacy & Security → Permissions → Location</li>\n                </ul>\n                <p><strong>On mobile devices:</strong> Also check your device's location settings and ensure the browser has location permission.</p>\n              </div>\n\n              <h5>How to Use This Module</h5>\n              <ol>\n                <li>Select the detection type (Potholes, Alligator Cracks, or Kerbs)</li>\n                <li>Upload an image or use the camera to capture a photo</li>\n                <li>If using the camera, allow location access when prompted for GPS coordinates</li>\n                <li>Click the Detect button to analyze the image</li>\n                <li>Review the detection results and measurements</li>\n              </ol>\n              \n              <p>\n                The detected defects are automatically recorded in the database for tracking \n                and analysis in the Dashboard module.\n              </p>\n            </Card.Body>\n          </Card>\n        </Tab>\n      </Tabs>\n\n      {/* Classification Error Modal */}\n      {/* / <Modal\n        show={showClassificationModal}\n        onHide={() => setShowClassificationModal(false)}\n        centered\n      >\n        <Modal.Header closeButton>\n          <Modal.Title>\n            <i className=\"fas fa-exclamation-triangle text-warning me-2\"></i>\n            Road Detection Failed\n          </Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          <div className=\"text-center\">\n            <div className=\"mb-3\">\n              <i className=\"fas fa-road fa-3x text-muted\"></i>\n            </div>\n            <h5 className=\"text-danger mb-3\">No Road Detected</h5>\n            <p className=\"mb-3\">\n              {classificationError || 'The uploaded image does not appear to contain a road. Please upload an image that clearly shows a road surface for defect detection.'}\n            </p>\n            <div className=\"alert alert-info\">\n              <strong>Tips for better results:</strong>\n              <ul className=\"mb-0 mt-2 text-start\">\n                <li>Ensure the image clearly shows a road surface</li>\n                <li>Avoid images with only buildings, sky, or vegetation</li>\n                <li>Make sure the road takes up a significant portion of the image</li>\n                <li>Use good lighting conditions for clearer road visibility</li>\n              </ul>\n            </div>\n          </div>\n        </Modal.Body>\n        <Modal.Footer>\n          <Button\n            variant=\"primary\"\n            onClick={() => setShowClassificationModal(false)}\n          >\n            Try Another Image\n          </Button>\n        </Modal.Footer>\n      </Modal> */}\n\n      {/* Image Modal for Full-Size View */}\n      <Modal\n        show={showImageModal}\n        onHide={() => setShowImageModal(false)}\n        size=\"lg\"\n        centered\n      >\n        <Modal.Header closeButton>\n          <Modal.Title>\n            <i className=\"fas fa-image me-2\"></i>\n            Image View {selectedImageData?.filename && (\n              <small className=\"text-muted\">- {selectedImageData.filename}</small>\n            )}\n          </Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          {selectedImageData && (\n            <div className=\"text-center\">\n              <div className=\"mb-4\">\n                <h6 className=\"mb-3\">\n                  <i className=\"fas fa-camera me-2\"></i>\n                  Original Image\n                </h6>\n                <img\n                  src={selectedImageData.originalImage}\n                  alt=\"Original Image\"\n                  className=\"img-fluid\"\n                  style={{\n                    maxHeight: '400px',\n                    borderRadius: '8px',\n                    border: '2px solid #dee2e6',\n                    boxShadow: '0 4px 8px rgba(0,0,0,0.1)'\n                  }}\n                />\n              </div>\n              {selectedImageData.processedImage && selectedImageData.isRoad && (\n                <div className=\"mt-4\">\n                  <h6 className=\"mb-3\">\n                    <i className=\"fas fa-search me-2\"></i>\n                    Processed Image (Detection Results)\n                  </h6>\n                  <img\n                    src={selectedImageData.processedImage}\n                    alt=\"Processed Image\"\n                    className=\"img-fluid\"\n                    style={{\n                      maxHeight: '400px',\n                      borderRadius: '8px',\n                      border: '2px solid #28a745',\n                      boxShadow: '0 4px 8px rgba(0,0,0,0.1)'\n                    }}\n                  />\n                </div>\n              )}\n              {!selectedImageData.isRoad && (\n                <div className=\"mt-3\">\n                  <Alert variant=\"info\">\n                    <i className=\"fas fa-info-circle me-2\"></i>\n                    This image was classified as non-road and therefore no defect detection was performed.\n                  </Alert>\n                </div>\n              )}\n            </div>\n          )}\n        </Modal.Body>\n        <Modal.Footer>\n          <Button\n            variant=\"secondary\"\n            onClick={() => setShowImageModal(false)}\n          >\n            Close\n          </Button>\n        </Modal.Footer>\n      </Modal>\n    </Container>\n  );\n};\n\n// Add CSS styles for the enhanced detection table\nconst styles = `\n  .detection-table-container {\n    max-height: 600px;\n    overflow-y: auto;\n  }\n\n  .detection-table-container th {\n    position: sticky;\n    top: 0;\n    background-color: #f8f9fa;\n    z-index: 10;\n  }\n\n  .detection-table-container th:hover {\n    background-color: #e9ecef;\n  }\n\n  .detection-summary-cards .card {\n    transition: transform 0.2s ease-in-out;\n  }\n\n  .detection-summary-cards .card:hover {\n    transform: translateY(-2px);\n  }\n\n  .table-responsive {\n    border-radius: 8px;\n    box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n  }\n\n  .badge {\n    font-size: 0.75em;\n  }\n\n  @media (max-width: 768px) {\n    .detection-summary-cards .col-md-3 {\n      margin-bottom: 1rem;\n    }\n\n    .d-flex.gap-2.flex-wrap {\n      flex-direction: column;\n    }\n\n    .d-flex.gap-2.flex-wrap .btn {\n      margin-bottom: 0.5rem;\n    }\n  }\n`;\n\n// Inject styles into the document head\nif (typeof document !== 'undefined') {\n  const styleSheet = document.createElement('style');\n\n  styleSheet.innerText = styles;\n  document.head.appendChild(styleSheet);\n}\n\nexport default Pavement;\n \n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,SAAS,EAAEC,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAEC,IAAI,EAAEC,GAAG,EAAEC,KAAK,EAAEC,OAAO,EAAEC,cAAc,EAAEC,OAAO,EAAEC,KAAK,QAAQ,iBAAiB;AAC1H,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAO,gBAAgB;AACvB,OAAOC,aAAa,MAAM,wBAAwB;AAClD,OAAOC,oBAAoB,MAAM,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAGtE,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGzB,QAAQ,CAAC,WAAW,CAAC;EACvD,MAAM,CAAC0B,aAAa,EAAEC,gBAAgB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC4B,UAAU,EAAEC,aAAa,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC8B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/B,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACgC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACkC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC;EAC7D,MAAM,CAACoC,cAAc,EAAEC,iBAAiB,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACsC,OAAO,EAAEC,UAAU,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwC,OAAO,EAAEC,UAAU,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0C,KAAK,EAAEC,QAAQ,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC4C,YAAY,EAAEC,eAAe,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC8C,WAAW,EAAEC,cAAc,CAAC,GAAG/C,QAAQ,CAAC,eAAe,CAAC;EAC/D,MAAM,CAACgD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGjD,QAAQ,CAAC,aAAa,CAAC;EACzE,MAAM,CAACkD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGnD,QAAQ,CAAC,SAAS,CAAC;EACvE,MAAM,CAACoD,aAAa,EAAEC,gBAAgB,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACsD,eAAe,EAAEC,kBAAkB,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAM,CAACwD,YAAY,EAAEC,eAAe,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC0D,eAAe,EAAEC,kBAAkB,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC4D,cAAc,EAAEC,iBAAiB,CAAC,GAAG7D,QAAQ,CAAC,CAAC,CAAC;;EAEvD;EACA,MAAM,CAAC8D,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG/D,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAElE;EACA,MAAM,CAACgE,uBAAuB,EAAEC,0BAA0B,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;EAC7E,MAAM,CAACkE,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGnE,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAACoE,cAAc,EAAEC,iBAAiB,CAAC,GAAGrE,QAAQ,CAAC,CAAC,CAAC;;EAEvD;EACA,MAAM,CAACsE,cAAc,EAAEC,iBAAiB,CAAC,GAAGvE,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACwE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGzE,QAAQ,CAAC,IAAI,CAAC;;EAEhE;EACA,MAAM,CAAC0E,WAAW,EAAEC,cAAc,CAAC,GAAG3E,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;;EAIvD;EACA,MAAM,CAAC4E,yBAAyB,EAAEC,4BAA4B,CAAC,GAAG7E,QAAQ,CAAC,KAAK,CAAC;EACjF;EACA,MAAM,CAAC8E,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG/E,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACzE,MAAM,CAACgF,UAAU,EAAEC,aAAa,CAAC,GAAGjF,QAAQ,CAAC;IAAEkF,GAAG,EAAE,IAAI;IAAEC,SAAS,EAAE;EAAM,CAAC,CAAC;;EAE7E;;EAEA,MAAMC,SAAS,GAAGnF,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMoF,YAAY,GAAGpF,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM;IAAEqF;EAAS,CAAC,GAAGtE,aAAa,CAAC,CAAC;;EAEpC;EACA,MAAMuE,eAAe,gBACnBpE,OAAA,CAACP,OAAO;IAAC4E,EAAE,EAAC,kBAAkB;IAACC,KAAK,EAAE;MAAEC,QAAQ,EAAE;IAAQ,CAAE;IAAAC,QAAA,gBAC1DxE,OAAA,CAACP,OAAO,CAACgF,MAAM;MAACC,EAAE,EAAC,IAAI;MAAAF,QAAA,EAAC;IAA0B;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB,CAAC,eACnE9E,OAAA,CAACP,OAAO,CAACsF,IAAI;MAAAP,QAAA,gBACXxE,OAAA;QAAGsE,KAAK,EAAE;UAAEU,YAAY,EAAE;QAAO,CAAE;QAAAR,QAAA,EAAC;MAEpC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJ9E,OAAA;QAAIsE,KAAK,EAAE;UAAEU,YAAY,EAAE,GAAG;UAAEC,WAAW,EAAE;QAAO,CAAE;QAAAT,QAAA,gBACpDxE,OAAA;UAAAwE,QAAA,EAAI;QAAoC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7C9E,OAAA;UAAAwE,QAAA,EAAI;QAAkB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3B9E,OAAA;UAAAwE,QAAA,EAAI;QAAkC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3C9E,OAAA;UAAAwE,QAAA,EAAI;QAAmD;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CACV;;EAED;EACA,MAAMI,uBAAuB,GAAG,MAAAA,CAAA,KAAY;IAC1C,IAAI,CAACC,SAAS,CAACC,WAAW,IAAI,CAACD,SAAS,CAACC,WAAW,CAACC,KAAK,EAAE;MAC1D;MACA,OAAO,QAAQ;IACjB;IAEA,IAAI;MACF,MAAMC,UAAU,GAAG,MAAMH,SAAS,CAACC,WAAW,CAACC,KAAK,CAAC;QAAEE,IAAI,EAAE;MAAc,CAAC,CAAC;MAC7E,OAAOD,UAAU,CAACE,KAAK;IACzB,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZC,OAAO,CAACC,IAAI,CAAC,yCAAyC,EAAEF,GAAG,CAAC;MAC5D,OAAO,QAAQ;IACjB;EACF,CAAC;;EAED;EACA,MAAMG,eAAe,GAAGA,CAAA,KAAM;IAC5B,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtC;MACA,IAAI,CAACZ,SAAS,CAACa,WAAW,EAAE;QAC1BD,MAAM,CAAC,IAAIE,KAAK,CAAC,8CAA8C,CAAC,CAAC;QACjE;MACF;;MAEA;MACA,IAAI,CAACC,MAAM,CAACC,eAAe,EAAE;QAC3BJ,MAAM,CAAC,IAAIE,KAAK,CAAC,+CAA+C,CAAC,CAAC;QAClE;MACF;MAEA,MAAMG,OAAO,GAAG;QACdC,kBAAkB,EAAE,IAAI;QACxBC,OAAO,EAAE,KAAK;QAAE;QAChBC,UAAU,EAAE,KAAK,CAAC;MACpB,CAAC;MAEDpB,SAAS,CAACa,WAAW,CAACQ,kBAAkB,CACrCC,QAAQ,IAAK;QACZX,OAAO,CAACW,QAAQ,CAAC;MACnB,CAAC,EACAlF,KAAK,IAAK;QACT,IAAImF,YAAY,GAAG,6BAA6B;QAEhD,QAAQnF,KAAK,CAACoF,IAAI;UAChB,KAAKpF,KAAK,CAACqF,iBAAiB;YAC1BF,YAAY,GAAG,sFAAsF;YACrG;UACF,KAAKnF,KAAK,CAACsF,oBAAoB;YAC7BH,YAAY,GAAG,wDAAwD;YACvE;UACF,KAAKnF,KAAK,CAACuF,OAAO;YAChBJ,YAAY,GAAG,+CAA+C;YAC9D;UACF;YACEA,YAAY,GAAG,mBAAmBnF,KAAK,CAACwF,OAAO,EAAE;YACjD;QACJ;QAEAhB,MAAM,CAAC,IAAIE,KAAK,CAACS,YAAY,CAAC,CAAC;MACjC,CAAC,EACDN,OACF,CAAC;IACH,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMY,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC5E,kBAAkB,CAAC,IAAI,CAAC;IACxBF,gBAAgB,CAAC,EAAE,CAAC;IAEpB,IAAI;MACF;MACA,MAAM+E,eAAe,GAAG,MAAM/B,uBAAuB,CAAC,CAAC;MACvDlD,qBAAqB,CAACiF,eAAe,CAAC;;MAEtC;MACA,IAAIA,eAAe,KAAK,QAAQ,EAAE;QAChC,MAAMC,QAAQ,GAAG,sDAAsD,GACvD,+DAA+D,GAC/D,2CAA2C,GAC3C,4CAA4C,GAC5C,uCAAuC;QACvDhF,gBAAgB,CAACgF,QAAQ,CAAC;QAC1BtF,cAAc,CAAC,mBAAmB,CAAC;QACnC;MACF;;MAEA;MACA,MAAM6E,QAAQ,GAAG,MAAMb,eAAe,CAAC,CAAC;MACxC,MAAM;QAAEuB,QAAQ;QAAEC;MAAU,CAAC,GAAGX,QAAQ,CAACY,MAAM;;MAE/C;MACA,MAAMC,eAAe,GAAG,GAAGH,QAAQ,CAACI,OAAO,CAAC,CAAC,CAAC,KAAKH,SAAS,CAACG,OAAO,CAAC,CAAC,CAAC,EAAE;MACzE3F,cAAc,CAAC0F,eAAe,CAAC;MAC/BtF,qBAAqB,CAAC,SAAS,CAAC;MAChCE,gBAAgB,CAAC,EAAE,CAAC;MAEpBwD,OAAO,CAAC8B,GAAG,CAAC,oBAAoB,EAAE;QAAEL,QAAQ;QAAEC,SAAS;QAAEK,QAAQ,EAAEhB,QAAQ,CAACY,MAAM,CAACI;MAAS,CAAC,CAAC;IAEhG,CAAC,CAAC,OAAOlG,KAAK,EAAE;MACdmE,OAAO,CAACnE,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDW,gBAAgB,CAACX,KAAK,CAACwF,OAAO,CAAC;MAC/BnF,cAAc,CAAC,gBAAgB,CAAC;;MAEhC;MACA,IAAIL,KAAK,CAACwF,OAAO,CAACW,QAAQ,CAAC,QAAQ,CAAC,EAAE;QACpC1F,qBAAqB,CAAC,QAAQ,CAAC;MACjC;IACF,CAAC,SAAS;MACRI,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAMuF,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAMC,KAAK,GAAGC,KAAK,CAACC,IAAI,CAACH,CAAC,CAACI,MAAM,CAACH,KAAK,CAAC;IACxC,IAAIA,KAAK,CAACI,MAAM,GAAG,CAAC,EAAE;MACpBvH,aAAa,CAAC,CAAC,GAAGD,UAAU,EAAE,GAAGoH,KAAK,CAAC,CAAC;;MAExC;MACAA,KAAK,CAACK,OAAO,CAACC,IAAI,IAAI;QACpB,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;QAC/BD,MAAM,CAACE,SAAS,GAAG,MAAM;UACvB1H,mBAAmB,CAAC2H,IAAI,KAAK;YAC3B,GAAGA,IAAI;YACP,CAACJ,IAAI,CAAC5C,IAAI,GAAG6C,MAAM,CAACI;UACtB,CAAC,CAAC,CAAC;QACL,CAAC;QACDJ,MAAM,CAACK,aAAa,CAACN,IAAI,CAAC;;QAE1B;QACArH,mBAAmB,CAACyH,IAAI,KAAK;UAC3B,GAAGA,IAAI;UACP,CAACJ,IAAI,CAAC5C,IAAI,GAAG;QACf,CAAC,CAAC,CAAC;MACL,CAAC,CAAC;;MAEF;MACArE,iBAAiB,CAAC,IAAI,CAAC;MACvBE,UAAU,CAAC,IAAI,CAAC;MAChBI,QAAQ,CAAC,EAAE,CAAC;IACd;EACF,CAAC;;EAED;EACA,MAAMkH,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,MAAMC,QAAQ,GAAG1E,SAAS,CAAC2E,OAAO,CAACC,aAAa,CAAC,CAAC;IAClD,IAAIF,QAAQ,EAAE;MACZ;MACA,IAAIhH,WAAW,KAAK,eAAe,IAAIA,WAAW,KAAK,gBAAgB,EAAE;QACvE,MAAMqF,qBAAqB,CAAC,CAAC;MAC/B;MAEA,MAAM8B,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAC1C,MAAMC,QAAQ,GAAG,kBAAkBH,SAAS,MAAM;MAClD,MAAMI,kBAAkB,GAAGvH,WAAW,CAAC,CAAC;;MAExCjB,aAAa,CAAC,CAAC,GAAGD,UAAU,EAAEwI,QAAQ,CAAC,CAAC;MACxCrI,mBAAmB,CAAC2H,IAAI,KAAK;QAC3B,GAAGA,IAAI;QACP,CAACU,QAAQ,GAAGN;MACd,CAAC,CAAC,CAAC;MACH7H,mBAAmB,CAACyH,IAAI,KAAK;QAC3B,GAAGA,IAAI;QACP,CAACU,QAAQ,GAAGC;MACd,CAAC,CAAC,CAAC;MACHlI,oBAAoB,CAACP,UAAU,CAACwH,MAAM,CAAC;MAEvC/G,iBAAiB,CAAC,IAAI,CAAC;MACvBE,UAAU,CAAC,IAAI,CAAC;MAChBI,QAAQ,CAAC,EAAE,CAAC;;MAEZ;MACAkE,OAAO,CAAC8B,GAAG,CAAC,kCAAkC,EAAE0B,kBAAkB,CAAC;IACrE;EACF,CAAC;;EAED;EACA,MAAMC,uBAAuB,GAAGA,CAAA,KAAM;IACpC,IAAIC,MAAM,CAACC,IAAI,CAAC1I,gBAAgB,CAAC,CAACsH,MAAM,KAAK,CAAC,EAAE;MAC9C,OAAOtG,WAAW,CAAC,CAAC;IACtB;IAEA,MAAM2H,eAAe,GAAGF,MAAM,CAACC,IAAI,CAAC1I,gBAAgB,CAAC,CAACI,iBAAiB,CAAC;IACxE,OAAOF,gBAAgB,CAACyI,eAAe,CAAC,IAAI,eAAe;EAC7D,CAAC;;EAED;EACA,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,MAAMC,cAAc,GAAG,CAAC/H,YAAY;IACpCC,eAAe,CAAC8H,cAAc,CAAC;IAE/B,IAAIA,cAAc,EAAE;MAClB;MACA,MAAMxC,qBAAqB,CAAC,CAAC;IAC/B,CAAC,MAAM;MACL;MACA;MACA,IAAIoC,MAAM,CAACC,IAAI,CAAC1I,gBAAgB,CAAC,CAACsH,MAAM,KAAK,CAAC,EAAE;QAC9CrG,cAAc,CAAC,eAAe,CAAC;QAC/BM,gBAAgB,CAAC,EAAE,CAAC;QACpBF,qBAAqB,CAAC,SAAS,CAAC;MAClC;IACF;EACF,CAAC;;EAED;EACA,MAAMyH,uBAAuB,GAAGA,CAAA,KAAM;IACpC3H,oBAAoB,CAACyG,IAAI,IAAIA,IAAI,KAAK,aAAa,GAAG,MAAM,GAAG,aAAa,CAAC;EAC/E,CAAC;;EAED;EACA,MAAMmB,yBAAyB,GAAIhD,YAAY,IAAK;IAClD1D,sBAAsB,CAAC0D,YAAY,CAAC;IACpC5D,0BAA0B,CAAC,IAAI,CAAC;IAChCtB,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAChB,CAAC;;EAED;EACA,MAAMmI,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCrI,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF;MACA,MAAMoI,UAAU,GAAGC,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC;MACjD,MAAMC,IAAI,GAAGH,UAAU,GAAGI,IAAI,CAACC,KAAK,CAACL,UAAU,CAAC,GAAG,IAAI;;MAEvD;MACA,MAAMM,mBAAmB,GAAGd,MAAM,CAACe,MAAM,CAACxJ,gBAAgB,CAAC,CAACI,iBAAiB,CAAC;MAE9E,IAAI,CAACmJ,mBAAmB,EAAE;QACxB1I,QAAQ,CAAC,kCAAkC,CAAC;QAC5CF,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA,MAAM8I,gBAAgB,GAAGjB,uBAAuB,CAAC,CAAC;;MAElD;MACA,MAAMkB,SAAS,GAAGjB,MAAM,CAACC,IAAI,CAAC1I,gBAAgB,CAAC;MAC/C,MAAM2I,eAAe,GAAGe,SAAS,CAACtJ,iBAAiB,CAAC;;MAEpD;MACA,MAAMuJ,WAAW,GAAG;QAClBC,KAAK,EAAEL,mBAAmB;QAC1BvI,WAAW,EAAEyI,gBAAgB;QAC7BI,QAAQ,EAAE,CAAAT,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,QAAQ,KAAI,SAAS;QACrCC,IAAI,EAAE,CAAAV,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEU,IAAI,KAAI,SAAS;QAC7BC,wBAAwB,EAAE,CAACjH;MAC7B,CAAC;;MAED;MACA,IAAIkH,QAAQ;MACZ,QAAOpK,aAAa;QAClB,KAAK,KAAK;UACRoK,QAAQ,GAAG,0BAA0B;UACrC;QACF,KAAK,UAAU;UACbA,QAAQ,GAAG,+BAA+B;UAC1C;QACF,KAAK,QAAQ;UACXA,QAAQ,GAAG,6BAA6B;UACxC;QACF,KAAK,OAAO;UACVA,QAAQ,GAAG,4BAA4B;UACvC;QACF;UACEA,QAAQ,GAAG,0BAA0B;MACzC;;MAEA;MACA,MAAMC,QAAQ,GAAG,MAAMjL,KAAK,CAACkL,IAAI,CAACF,QAAQ,EAAEL,WAAW,CAAC;;MAExD;MACA,IAAIM,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QAAA,IAAAC,qBAAA;QACzB;QACA,MAAMC,WAAW,GAAGL,QAAQ,CAACE,IAAI,CAACI,SAAS,KAAK,KAAK;QACrD,MAAMC,MAAM,GAAG,EAAAH,qBAAA,GAAAJ,QAAQ,CAACE,IAAI,CAACM,cAAc,cAAAJ,qBAAA,uBAA5BA,qBAAA,CAA8BK,OAAO,KAAI,KAAK;;QAE7D;QACAnK,iBAAiB,CAAC0J,QAAQ,CAACE,IAAI,CAACQ,eAAe,CAAC;QAChDlK,UAAU,CAACwJ,QAAQ,CAACE,IAAI,CAAC;;QAEzB;QACA,MAAMS,gBAAgB,GAAG;UACvBC,QAAQ,EAAEZ,QAAQ,CAACE,IAAI,CAACU,QAAQ,IAAI,EAAE;UACtCC,MAAM,EAAEb,QAAQ,CAACE,IAAI,CAACW,MAAM,IAAI,EAAE;UAClCC,KAAK,EAAEd,QAAQ,CAACE,IAAI,CAACY,KAAK,IAAI;QAChC,CAAC;;QAED;QACA,MAAMC,WAAW,GAAG;UAClB1C,QAAQ,EAAEK,eAAe;UACzByB,OAAO,EAAE,IAAI;UACbG,SAAS,EAAED,WAAW;UACtBE,MAAM,EAAEA,MAAM;UACdC,cAAc,EAAER,QAAQ,CAACE,IAAI,CAACM,cAAc;UAC5CnK,cAAc,EAAE2J,QAAQ,CAACE,IAAI,CAACQ,eAAe;UAC7CM,aAAa,EAAE1B,mBAAmB;UAAE;UACpCY,IAAI,EAAEF,QAAQ,CAACE,IAAI;UACnBS,gBAAgB,EAAEA,gBAAgB;UAClCM,eAAe,EAAE;YACfL,QAAQ,EAAED,gBAAgB,CAACC,QAAQ,CAACvD,MAAM;YAC1CwD,MAAM,EAAEF,gBAAgB,CAACE,MAAM,CAACxD,MAAM;YACtCyD,KAAK,EAAEH,gBAAgB,CAACG,KAAK,CAACzD,MAAM;YACpC6D,KAAK,EAAEP,gBAAgB,CAACC,QAAQ,CAACvD,MAAM,GAAGsD,gBAAgB,CAACE,MAAM,CAACxD,MAAM,GAAGsD,gBAAgB,CAACG,KAAK,CAACzD;UACpG;QACF,CAAC;;QAED;QACA3F,eAAe,CAAC,CAACqJ,WAAW,CAAC,CAAC;;QAE9B;QACA;QACA/I,sBAAsB,CAAC2F,IAAI,KAAK;UAC9B,GAAGA,IAAI;UACP,CAACe,eAAe,GAAG;YACjBsC,aAAa,EAAE1B,mBAAmB;YAClCjJ,cAAc,EAAEkK,MAAM,GAAGP,QAAQ,CAACE,IAAI,CAACQ,eAAe,GAAG,IAAI;YAC7DnK,OAAO,EAAEyJ,QAAQ,CAACE,IAAI;YACtBK,MAAM,EAAEA;UACV;QACF,CAAC,CAAC,CAAC;;QAED;QACAzK,aAAa,CAAC,EAAE,CAAC;QACjBE,mBAAmB,CAAC,CAAC,CAAC,CAAC;QACvBE,mBAAmB,CAAC,CAAC,CAAC,CAAC;QACvBE,oBAAoB,CAAC,CAAC,CAAC;;QAEvB;QACAY,cAAc,CAAC,eAAe,CAAC;QAC/BM,gBAAgB,CAAC,EAAE,CAAC;QACpBF,qBAAqB,CAAC,SAAS,CAAC;QAEhC,IAAIkC,YAAY,CAAC0E,OAAO,EAAE;UACxB1E,YAAY,CAAC0E,OAAO,CAACmD,KAAK,GAAG,EAAE;QACjC;MACJ,CAAC,MAAM;QACL,MAAMrF,YAAY,GAAGkE,QAAQ,CAACE,IAAI,CAAC/D,OAAO,IAAI,kBAAkB;;QAEhE;QACA,MAAM4E,WAAW,GAAG;UAClB1C,QAAQ,EAAEK,eAAe;UACzByB,OAAO,EAAE,KAAK;UACdG,SAAS,EAAE,KAAK;UAChBC,MAAM,EAAE,KAAK;UACb5J,KAAK,EAAEmF,YAAY;UACnBsF,qBAAqB,EAAEtF,YAAY,CAACgB,QAAQ,CAAC,kBAAkB;QACjE,CAAC;;QAED;QACApF,eAAe,CAAC,CAACqJ,WAAW,CAAC,CAAC;QAE9BnK,QAAQ,CAACkF,YAAY,CAAC;MACxB;IACF,CAAC,CAAC,OAAOnF,KAAK,EAAE;MAAA,IAAA0K,eAAA,EAAAC,oBAAA;MACd,MAAMxF,YAAY,GAAG,EAAAuF,eAAA,GAAA1K,KAAK,CAACqJ,QAAQ,cAAAqB,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBnB,IAAI,cAAAoB,oBAAA,uBAApBA,oBAAA,CAAsBnF,OAAO,KAAI,uDAAuD;;MAE7G;MACA,MAAMsD,SAAS,GAAGjB,MAAM,CAACC,IAAI,CAAC1I,gBAAgB,CAAC;MAC/C,MAAM2I,eAAe,GAAGe,SAAS,CAACtJ,iBAAiB,CAAC;;MAEpD;MACA,MAAM4K,WAAW,GAAG;QAClB1C,QAAQ,EAAEK,eAAe;QACzByB,OAAO,EAAE,KAAK;QACdG,SAAS,EAAE,KAAK;QAChBC,MAAM,EAAE,KAAK;QACb5J,KAAK,EAAEmF,YAAY;QACnBsF,qBAAqB,EAAEtF,YAAY,CAACgB,QAAQ,CAAC,kBAAkB;MACjE,CAAC;;MAED;MACApF,eAAe,CAAC,CAACqJ,WAAW,CAAC,CAAC;;MAE9B;MACA,IAAIjF,YAAY,CAACgB,QAAQ,CAAC,kBAAkB,CAAC,EAAE;QAC7CgC,yBAAyB,CAAChD,YAAY,CAAC;MACzC,CAAC,MAAM;QACLlF,QAAQ,CAACkF,YAAY,CAAC;MACxB;IACF,CAAC,SAAS;MACRpF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM6K,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI/C,MAAM,CAACC,IAAI,CAAC1I,gBAAgB,CAAC,CAACsH,MAAM,KAAK,CAAC,EAAE;MAC9CzG,QAAQ,CAAC,sBAAsB,CAAC;MAChC;IACF;IAEAgB,kBAAkB,CAAC,IAAI,CAAC;IACxBhB,QAAQ,CAAC,EAAE,CAAC;IACZc,eAAe,CAAC,EAAE,CAAC;IACnBI,iBAAiB,CAAC,CAAC,CAAC;IACpBQ,iBAAiB,CAACkG,MAAM,CAACC,IAAI,CAAC1I,gBAAgB,CAAC,CAACsH,MAAM,CAAC;;IAEvD;IACA,MAAM2B,UAAU,GAAGC,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC;IACjD,MAAMC,IAAI,GAAGH,UAAU,GAAGI,IAAI,CAACC,KAAK,CAACL,UAAU,CAAC,GAAG,IAAI;IAEvD,IAAI;MACF;MACA,IAAIe,QAAQ;MACZ,QAAOpK,aAAa;QAClB,KAAK,KAAK;UACRoK,QAAQ,GAAG,0BAA0B;UACrC;QACF,KAAK,UAAU;UACbA,QAAQ,GAAG,+BAA+B;UAC1C;QACF,KAAK,QAAQ;UACXA,QAAQ,GAAG,6BAA6B;UACxC;QACF,KAAK,OAAO;UACVA,QAAQ,GAAG,4BAA4B;UACvC;QACF;UACEA,QAAQ,GAAG,0BAA0B;MACzC;MAEA,MAAMxJ,OAAO,GAAG,EAAE;MAClB,MAAMkJ,SAAS,GAAGjB,MAAM,CAACC,IAAI,CAAC1I,gBAAgB,CAAC;;MAE/C;MACA,KAAK,IAAIyL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/B,SAAS,CAACpC,MAAM,EAAEmE,CAAC,EAAE,EAAE;QACzC,MAAMnD,QAAQ,GAAGoB,SAAS,CAAC+B,CAAC,CAAC;QAC7B,MAAMC,SAAS,GAAG1L,gBAAgB,CAACsI,QAAQ,CAAC;QAE5C,IAAI;UACF;UACAjI,oBAAoB,CAACoL,CAAC,CAAC;;UAEvB;UACA,MAAMhC,gBAAgB,GAAGvJ,gBAAgB,CAACoI,QAAQ,CAAC,IAAI,eAAe;;UAEtE;UACA,MAAMqB,WAAW,GAAG;YAClBC,KAAK,EAAE8B,SAAS;YAChB1K,WAAW,EAAEyI,gBAAgB;YAC7BI,QAAQ,EAAE,CAAAT,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,QAAQ,KAAI,SAAS;YACrCC,IAAI,EAAE,CAAAV,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEU,IAAI,KAAI,SAAS;YAC7BC,wBAAwB,EAAE,CAACjH;UAC7B,CAAC;;UAED;UACA,MAAMmH,QAAQ,GAAG,MAAMjL,KAAK,CAACkL,IAAI,CAACF,QAAQ,EAAEL,WAAW,CAAC;UAExD,IAAIM,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;YAAA,IAAAuB,sBAAA;YACzB;YACA,MAAMrB,WAAW,GAAGL,QAAQ,CAACE,IAAI,CAACI,SAAS,KAAK,KAAK;YACrD,MAAMC,MAAM,GAAG,EAAAmB,sBAAA,GAAA1B,QAAQ,CAACE,IAAI,CAACM,cAAc,cAAAkB,sBAAA,uBAA5BA,sBAAA,CAA8BjB,OAAO,KAAI,KAAK;YAE7D,IAAIJ,WAAW,IAAIE,MAAM,EAAE;cACzB;cACAjK,iBAAiB,CAAC0J,QAAQ,CAACE,IAAI,CAACQ,eAAe,CAAC;cAChDlK,UAAU,CAACwJ,QAAQ,CAACE,IAAI,CAAC;YAC3B;;YAEA;YACA,MAAMS,gBAAgB,GAAG;cACvBC,QAAQ,EAAEZ,QAAQ,CAACE,IAAI,CAACU,QAAQ,IAAI,EAAE;cACtCC,MAAM,EAAEb,QAAQ,CAACE,IAAI,CAACW,MAAM,IAAI,EAAE;cAClCC,KAAK,EAAEd,QAAQ,CAACE,IAAI,CAACY,KAAK,IAAI;YAChC,CAAC;YAEDvK,OAAO,CAACoL,IAAI,CAAC;cACXtD,QAAQ;cACR8B,OAAO,EAAE,IAAI;cACbG,SAAS,EAAED,WAAW;cACtBE,MAAM,EAAEA,MAAM;cACdC,cAAc,EAAER,QAAQ,CAACE,IAAI,CAACM,cAAc;cAC5CnK,cAAc,EAAE2J,QAAQ,CAACE,IAAI,CAACQ,eAAe;cAC7CM,aAAa,EAAES,SAAS;cAAE;cAC1BvB,IAAI,EAAEF,QAAQ,CAACE,IAAI;cACnBS,gBAAgB,EAAEA,gBAAgB;cAClCM,eAAe,EAAE;gBACfL,QAAQ,EAAED,gBAAgB,CAACC,QAAQ,CAACvD,MAAM;gBAC1CwD,MAAM,EAAEF,gBAAgB,CAACE,MAAM,CAACxD,MAAM;gBACtCyD,KAAK,EAAEH,gBAAgB,CAACG,KAAK,CAACzD,MAAM;gBACpC6D,KAAK,EAAEP,gBAAgB,CAACC,QAAQ,CAACvD,MAAM,GAAGsD,gBAAgB,CAACE,MAAM,CAACxD,MAAM,GAAGsD,gBAAgB,CAACG,KAAK,CAACzD;cACpG;YACF,CAAC,CAAC;UACJ,CAAC,MAAM;YACL,MAAMvB,YAAY,GAAGkE,QAAQ,CAACE,IAAI,CAAC/D,OAAO,IAAI,kBAAkB;YAChE5F,OAAO,CAACoL,IAAI,CAAC;cACXtD,QAAQ;cACR8B,OAAO,EAAE,KAAK;cACdG,SAAS,EAAE,KAAK;cAChBC,MAAM,EAAE,KAAK;cACb5J,KAAK,EAAEmF,YAAY;cACnBsF,qBAAqB,EAAEtF,YAAY,CAACgB,QAAQ,CAAC,kBAAkB;YACjE,CAAC,CAAC;UACJ;QACF,CAAC,CAAC,OAAOnG,KAAK,EAAE;UAAA,IAAAiL,gBAAA,EAAAC,qBAAA;UACd,MAAM/F,YAAY,GAAG,EAAA8F,gBAAA,GAAAjL,KAAK,CAACqJ,QAAQ,cAAA4B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB1B,IAAI,cAAA2B,qBAAA,uBAApBA,qBAAA,CAAsB1F,OAAO,KAAI,oCAAoC;UAC1F5F,OAAO,CAACoL,IAAI,CAAC;YACXtD,QAAQ;YACR8B,OAAO,EAAE,KAAK;YACdG,SAAS,EAAE,KAAK;YAChBC,MAAM,EAAE,KAAK;YACb5J,KAAK,EAAEmF,YAAY;YACnBsF,qBAAqB,EAAEtF,YAAY,CAACgB,QAAQ,CAAC,kBAAkB;UACjE,CAAC,CAAC;QACJ;;QAEA;QACAhF,iBAAiB,CAAC6F,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;;QAEnC;QACA;QACA,IAAI6D,CAAC,GAAG/B,SAAS,CAACpC,MAAM,GAAG,CAAC,EAAE;UAC5B,MAAM,IAAIpC,OAAO,CAACC,OAAO,IAAI4G,UAAU,CAAC5G,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;QAC3D;MACF;;MAEA;MACAxD,eAAe,CAACnB,OAAO,CAAC;;MAExB;MACA,MAAMwL,mBAAmB,GAAGxL,OAAO,CAACyL,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC9B,OAAO,IAAI8B,CAAC,CAAC3B,SAAS,IAAI2B,CAAC,CAAC1B,MAAM,CAAC;MACrF,IAAIwB,mBAAmB,CAAC1E,MAAM,GAAG,CAAC,EAAE;QAClC,MAAM6E,uBAAuB,GAAGH,mBAAmB,CAAC,CAAC,CAAC;QACtDzL,iBAAiB,CAAC4L,uBAAuB,CAAC7L,cAAc,CAAC;QACzDG,UAAU,CAAC0L,uBAAuB,CAAChC,IAAI,CAAC;;QAExC;QACA9J,oBAAoB,CAAC,CAAC,CAAC;MACzB,CAAC,MAAM;QACL;QACAE,iBAAiB,CAAC,IAAI,CAAC;QACvBE,UAAU,CAAC,IAAI,CAAC;QAChBJ,oBAAoB,CAAC,CAAC,CAAC;MACzB;;MAEA;MACA;MACA,MAAM+L,aAAa,GAAG,CAAC,CAAC;MACxB5L,OAAO,CAAC+G,OAAO,CAACM,MAAM,IAAI;QACxB,IAAIA,MAAM,CAACuC,OAAO,EAAE;UAClB,MAAMa,aAAa,GAAGjL,gBAAgB,CAAC6H,MAAM,CAACS,QAAQ,CAAC;UACvD8D,aAAa,CAACvE,MAAM,CAACS,QAAQ,CAAC,GAAG;YAC/B2C,aAAa,EAAEA,aAAa;YAC5B3K,cAAc,EAAEuH,MAAM,CAAC2C,MAAM,GAAG3C,MAAM,CAACvH,cAAc,GAAG,IAAI;YAC5DE,OAAO,EAAEqH,MAAM,CAACsC,IAAI;YACpBK,MAAM,EAAE3C,MAAM,CAAC2C;UACjB,CAAC;UACDzF,OAAO,CAAC8B,GAAG,CAAC,yBAAyB,EAAEgB,MAAM,CAACS,QAAQ,EAAE,SAAS,EAAET,MAAM,CAAC2C,MAAM,EAAE,mBAAmB,EAAE,CAAC,CAACS,aAAa,CAAC;QACzH;MACF,CAAC,CAAC;MACFhJ,sBAAsB,CAAC2F,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,GAAGwE;MAAc,CAAC,CAAC,CAAC;;MAE/D;MACArM,aAAa,CAAC,EAAE,CAAC;MACjBE,mBAAmB,CAAC,CAAC,CAAC,CAAC;MACvBE,mBAAmB,CAAC,CAAC,CAAC,CAAC;MACvBE,oBAAoB,CAAC,CAAC,CAAC;;MAEvB;MACAY,cAAc,CAAC,eAAe,CAAC;MAC/BM,gBAAgB,CAAC,EAAE,CAAC;MACpBF,qBAAqB,CAAC,SAAS,CAAC;MAEhC,IAAIkC,YAAY,CAAC0E,OAAO,EAAE;QACxB1E,YAAY,CAAC0E,OAAO,CAACmD,KAAK,GAAG,EAAE;MACjC;IAEF,CAAC,CAAC,OAAOxK,KAAK,EAAE;MACdC,QAAQ,CAAC,2BAA2B,IAAID,KAAK,CAACwF,OAAO,IAAI,eAAe,CAAC,CAAC;IAC5E,CAAC,SAAS;MACRvE,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAMwK,WAAW,GAAGA,CAAA,KAAM;IACxBtM,aAAa,CAAC,EAAE,CAAC;IACjBE,mBAAmB,CAAC,CAAC,CAAC,CAAC;IACvBE,mBAAmB,CAAC,CAAC,CAAC,CAAC;IACvBE,oBAAoB,CAAC,CAAC,CAAC;IACvBE,iBAAiB,CAAC,IAAI,CAAC;IACvBE,UAAU,CAAC,IAAI,CAAC;IAChBI,QAAQ,CAAC,EAAE,CAAC;IACZc,eAAe,CAAC,EAAE,CAAC;IACnBI,iBAAiB,CAAC,CAAC,CAAC;IACpBQ,iBAAiB,CAAC,CAAC,CAAC;IACpBN,sBAAsB,CAAC,CAAC,CAAC,CAAC;;IAE1B;IACAhB,cAAc,CAAC,eAAe,CAAC;IAC/BM,gBAAgB,CAAC,EAAE,CAAC;IACpBF,qBAAqB,CAAC,SAAS,CAAC;IAEhC,IAAIkC,YAAY,CAAC0E,OAAO,EAAE;MACxB1E,YAAY,CAAC0E,OAAO,CAACmD,KAAK,GAAG,EAAE;IACjC;EACF,CAAC;;EAMD;EACA,MAAMkB,oBAAoB,GAAIZ,SAAS,IAAK;IAC1C/I,oBAAoB,CAAC+I,SAAS,CAAC;IAC/BjJ,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAM8J,UAAU,GAAInJ,GAAG,IAAK;IAC1B,IAAIC,SAAS,GAAG,KAAK;IACrB,IAAIH,UAAU,CAACE,GAAG,KAAKA,GAAG,IAAIF,UAAU,CAACG,SAAS,KAAK,KAAK,EAAE;MAC5DA,SAAS,GAAG,MAAM;IACpB;IACAF,aAAa,CAAC;MAAEC,GAAG;MAAEC;IAAU,CAAC,CAAC;EACnC,CAAC;;EAED;EACA,MAAMmJ,cAAc,GAAIC,UAAU,IAAK;IACrC,IAAI,CAACvJ,UAAU,CAACE,GAAG,EAAE,OAAOqJ,UAAU;IAEtC,OAAO,CAAC,GAAGA,UAAU,CAAC,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACpC,IAAIC,MAAM,GAAGF,CAAC,CAACzJ,UAAU,CAACE,GAAG,CAAC;MAC9B,IAAI0J,MAAM,GAAGF,CAAC,CAAC1J,UAAU,CAACE,GAAG,CAAC;;MAE9B;MACA,IAAI,OAAOyJ,MAAM,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,QAAQ,EAAE;QAC5D,OAAO5J,UAAU,CAACG,SAAS,KAAK,KAAK,GAAGwJ,MAAM,GAAGC,MAAM,GAAGA,MAAM,GAAGD,MAAM;MAC3E;;MAEA;MACA,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,QAAQ,EAAE;QAC5D,OAAO5J,UAAU,CAACG,SAAS,KAAK,KAAK,GACjCwJ,MAAM,CAACE,aAAa,CAACD,MAAM,CAAC,GAC5BA,MAAM,CAACC,aAAa,CAACF,MAAM,CAAC;MAClC;;MAEA;MACA,IAAIA,MAAM,IAAI,IAAI,IAAIC,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC;MAC9C,IAAID,MAAM,IAAI,IAAI,EAAE,OAAO3J,UAAU,CAACG,SAAS,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;MAClE,IAAIyJ,MAAM,IAAI,IAAI,EAAE,OAAO5J,UAAU,CAACG,SAAS,KAAK,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;MAElE,OAAO,CAAC;IACV,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM2J,WAAW,GAAGA,CAAA,KAAM;IACxB;IACA,MAAMC,aAAa,GAAG,EAAE;IAExBvL,YAAY,CAAC6F,OAAO,CAACM,MAAM,IAAI;MAC7B,IAAIA,MAAM,CAACuC,OAAO,IAAIvC,MAAM,CAAC0C,SAAS,IAAI1C,MAAM,CAAC+C,gBAAgB,EAAE;QACjE,MAAM;UAAEC,QAAQ;UAAEC,MAAM;UAAEC;QAAM,CAAC,GAAGlD,MAAM,CAAC+C,gBAAgB;;QAE3D;QACAC,QAAQ,CAACtD,OAAO,CAAC2F,OAAO,IAAI;UAC1BD,aAAa,CAACrB,IAAI,CAAC;YACjBtD,QAAQ,EAAET,MAAM,CAACS,QAAQ;YACzB6E,IAAI,EAAE,SAAS;YACfzJ,EAAE,EAAEwJ,OAAO,CAACE,UAAU;YACtBC,QAAQ,EAAEH,OAAO,CAACG,QAAQ;YAC1BC,QAAQ,EAAEJ,OAAO,CAACI,QAAQ;YAC1BC,MAAM,EAAEL,OAAO,CAACK,MAAM;YACtBC,YAAY,EAAEN,OAAO,CAACM,YAAY;YAClCC,UAAU,EAAE,EAAE;YACdC,UAAU,EAAE,EAAE;YACdC,SAAS,EAAE,EAAE;YACbC,SAAS,EAAE,EAAE;YACbC,QAAQ,EAAE,EAAE;YACZC,UAAU,EAAEZ,OAAO,CAACY;UACtB,CAAC,CAAC;QACJ,CAAC,CAAC;;QAEF;QACAhD,MAAM,CAACvD,OAAO,CAACwG,KAAK,IAAI;UACtBd,aAAa,CAACrB,IAAI,CAAC;YACjBtD,QAAQ,EAAET,MAAM,CAACS,QAAQ;YACzB6E,IAAI,EAAE,OAAO;YACbzJ,EAAE,EAAEqK,KAAK,CAACC,QAAQ;YAClBX,QAAQ,EAAEU,KAAK,CAACV,QAAQ;YACxBC,QAAQ,EAAE,EAAE;YACZC,MAAM,EAAE,EAAE;YACVC,YAAY,EAAE,EAAE;YAChBC,UAAU,EAAEM,KAAK,CAACN,UAAU;YAC5BC,UAAU,EAAEK,KAAK,CAACL,UAAU;YAC5BC,SAAS,EAAE,EAAE;YACbC,SAAS,EAAE,EAAE;YACbC,QAAQ,EAAE,EAAE;YACZC,UAAU,EAAEC,KAAK,CAACD;UACpB,CAAC,CAAC;QACJ,CAAC,CAAC;;QAEF;QACA/C,KAAK,CAACxD,OAAO,CAAC0G,IAAI,IAAI;UACpBhB,aAAa,CAACrB,IAAI,CAAC;YACjBtD,QAAQ,EAAET,MAAM,CAACS,QAAQ;YACzB6E,IAAI,EAAE,MAAM;YACZzJ,EAAE,EAAEuK,IAAI,CAACC,OAAO;YAChBb,QAAQ,EAAE,EAAE;YACZC,QAAQ,EAAE,EAAE;YACZC,MAAM,EAAE,EAAE;YACVC,YAAY,EAAE,EAAE;YAChBC,UAAU,EAAE,EAAE;YACdC,UAAU,EAAE,EAAE;YACdC,SAAS,EAAEM,IAAI,CAACN,SAAS;YACzBC,SAAS,EAAEK,IAAI,CAACL,SAAS;YACzBC,QAAQ,EAAEI,IAAI,CAACJ,QAAQ;YACvBC,UAAU,EAAEG,IAAI,CAACH;UACnB,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,IAAIb,aAAa,CAAC3F,MAAM,KAAK,CAAC,EAAE;MAC9B6G,KAAK,CAAC,iCAAiC,CAAC;MACxC;IACF;;IAEA;IACA,MAAMC,OAAO,GAAG,CACd,gBAAgB,EAChB,gBAAgB,EAChB,IAAI,EACJ,YAAY,EACZ,YAAY,EACZ,cAAc,EACd,cAAc,EACd,YAAY,EACZ,YAAY,EACZ,WAAW,EACX,WAAW,EACX,YAAY,EACZ,YAAY,CACb;IAED,MAAMC,UAAU,GAAG,CACjBD,OAAO,CAACE,IAAI,CAAC,GAAG,CAAC,EACjB,GAAGrB,aAAa,CAACsB,GAAG,CAACC,SAAS,IAAI,CAChCA,SAAS,CAAClG,QAAQ,EAClBkG,SAAS,CAACrB,IAAI,EACdqB,SAAS,CAAC9K,EAAE,IAAI,EAAE,EAClB8K,SAAS,CAACnB,QAAQ,IAAI,EAAE,EACxBmB,SAAS,CAAClB,QAAQ,IAAI,EAAE,EACxBkB,SAAS,CAACjB,MAAM,IAAI,EAAE,EACtBiB,SAAS,CAAChB,YAAY,IAAI,EAAE,EAC5BgB,SAAS,CAACf,UAAU,IAAI,EAAE,EAC1Be,SAAS,CAACd,UAAU,IAAI,EAAE,EAC1Bc,SAAS,CAACb,SAAS,IAAI,EAAE,EACzBa,SAAS,CAACZ,SAAS,IAAI,EAAE,EACzBY,SAAS,CAACX,QAAQ,IAAI,EAAE,EACxBW,SAAS,CAACV,UAAU,IAAI,EAAE,CAC3B,CAACQ,IAAI,CAAC,GAAG,CAAC,CAAC,CACb,CAACA,IAAI,CAAC,IAAI,CAAC;;IAEZ;IACA,MAAMG,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACL,UAAU,CAAC,EAAE;MAAElB,IAAI,EAAE;IAA0B,CAAC,CAAC;IACxE,MAAMwB,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxC,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACP,IAAI,CAAC;IACrCE,IAAI,CAACM,YAAY,CAAC,MAAM,EAAEH,GAAG,CAAC;IAC9BH,IAAI,CAACM,YAAY,CAAC,UAAU,EAAE,8BAA8B,IAAI7G,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC6G,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;IACzGP,IAAI,CAAChL,KAAK,CAACwL,UAAU,GAAG,QAAQ;IAChCP,QAAQ,CAACQ,IAAI,CAACC,WAAW,CAACV,IAAI,CAAC;IAC/BA,IAAI,CAACW,KAAK,CAAC,CAAC;IACZV,QAAQ,CAACQ,IAAI,CAACG,WAAW,CAACZ,IAAI,CAAC;EACjC,CAAC;;EAMD;EACAvQ,SAAS,CAAC,MAAM;IACd,IAAI0C,YAAY,IAAIM,kBAAkB,KAAK,SAAS,EAAE;MACpD;MACAiF,qBAAqB,CAAC,CAAC;IACzB;EACF,CAAC,EAAE,CAACvF,YAAY,CAAC,CAAC;;EAElB;EACA1C,SAAS,CAAC,MAAM;IACd,IAAIoR,iBAAiB,GAAG,IAAI;IAE5B,MAAMC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACnC,IAAI;QACF,IAAIjL,SAAS,CAACC,WAAW,IAAID,SAAS,CAACC,WAAW,CAACC,KAAK,EAAE;UACxD,MAAMC,UAAU,GAAG,MAAMH,SAAS,CAACC,WAAW,CAACC,KAAK,CAAC;YAAEE,IAAI,EAAE;UAAc,CAAC,CAAC;UAE7E4K,iBAAiB,GAAGA,CAAA,KAAM;YACxBnO,qBAAqB,CAACsD,UAAU,CAACE,KAAK,CAAC;YACvC,IAAIF,UAAU,CAACE,KAAK,KAAK,SAAS,IAAI/D,YAAY,IAAIE,WAAW,KAAK,eAAe,EAAE;cACrFqF,qBAAqB,CAAC,CAAC;YACzB;UACF,CAAC;UAED1B,UAAU,CAAC+K,gBAAgB,CAAC,QAAQ,EAAEF,iBAAiB,CAAC;QAC1D;MACF,CAAC,CAAC,OAAO1K,GAAG,EAAE;QACZC,OAAO,CAACC,IAAI,CAAC,oCAAoC,EAAEF,GAAG,CAAC;MACzD;IACF,CAAC;IAED2K,gBAAgB,CAAC,CAAC;IAElB,OAAO,MAAM;MACX,IAAID,iBAAiB,EAAE;QACrB,IAAI;UACF,MAAM7K,UAAU,GAAGH,SAAS,CAACC,WAAW,CAACC,KAAK,CAAC;YAAEE,IAAI,EAAE;UAAc,CAAC,CAAC;UACvED,UAAU,CAACgL,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,mBAAmB,CAAC,QAAQ,EAAEL,iBAAiB,CAAC,CAAC;QAC1E,CAAC,CAAC,OAAO1K,GAAG,EAAE;UACZC,OAAO,CAACC,IAAI,CAAC,qCAAqC,EAAEF,GAAG,CAAC;QAC1D;MACF;IACF,CAAC;EACH,CAAC,EAAE,CAAChE,YAAY,EAAEE,WAAW,CAAC,CAAC;;EAE/B;EACA5C,SAAS,CAAC,MAAM;IACd;IACA;EAAA,CACD,EAAE,CAACgC,iBAAiB,EAAEF,gBAAgB,CAAC,CAAC;;EAEzC;EACA,MAAM,CAAC4P,YAAY,EAAEC,eAAe,CAAC,GAAG7R,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAM8R,8BAA8B,GAAGA,CAAA,KAAM;IAC3CjN,4BAA4B,CAAC6E,IAAI,IAAI;MACnC;MACA,IAAIkI,YAAY,EAAE;QAChBnO,eAAe,CAAC,EAAE,CAAC;QACnBoO,eAAe,CAAC,KAAK,CAAC;MACxB;MACA,OAAO,CAACnI,IAAI;IACd,CAAC,CAAC;EACJ,CAAC;EAGD,oBACEvI,OAAA,CAAChB,SAAS;IAAC4R,SAAS,EAAC,eAAe;IAAApM,QAAA,gBAElCxE,OAAA,CAACZ,IAAI;MACHyR,SAAS,EAAExQ,SAAU;MACrByQ,QAAQ,EAAGC,CAAC,IAAKzQ,YAAY,CAACyQ,CAAC,CAAE;MACjCH,SAAS,EAAC,MAAM;MAAApM,QAAA,gBAEhBxE,OAAA,CAACX,GAAG;QAAC2R,QAAQ,EAAC,WAAW;QAACC,KAAK,EAAC,iBAAiB;QAAAzM,QAAA,gBAC/CxE,OAAA,CAACf,IAAI;UAAC2R,SAAS,EAAC,MAAM;UAAApM,QAAA,eACpBxE,OAAA,CAACf,IAAI,CAAC8F,IAAI;YAAC6L,SAAS,EAAC,MAAM;YAAApM,QAAA,gBACzBxE,OAAA,CAACb,IAAI,CAAC+R,KAAK;cAACN,SAAS,EAAC,MAAM;cAAApM,QAAA,gBAC1BxE,OAAA,CAACb,IAAI,CAACgS,KAAK;gBAACP,SAAS,EAAC,MAAM;gBAAApM,QAAA,EAAC;cAAc;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACxD9E,OAAA,CAACb,IAAI,CAACiS,MAAM;gBACVrF,KAAK,EAAExL,aAAc;gBACrB8Q,QAAQ,EAAGzJ,CAAC,IAAKpH,gBAAgB,CAACoH,CAAC,CAACI,MAAM,CAAC+D,KAAK,CAAE;gBAAAvH,QAAA,gBAElDxE,OAAA;kBAAQ+L,KAAK,EAAC,KAAK;kBAAAvH,QAAA,EAAC;gBAA+B;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5D9E,OAAA;kBAAQ+L,KAAK,EAAC,UAAU;kBAAAvH,QAAA,EAAC;gBAAQ;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1C9E,OAAA;kBAAQ+L,KAAK,EAAC,QAAQ;kBAAAvH,QAAA,EAAC;gBAAgB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChD9E,OAAA;kBAAQ+L,KAAK,EAAC,OAAO;kBAAAvH,QAAA,EAAC;gBAAK;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAGb9E,OAAA;cAAK4Q,SAAS,EAAC,qCAAqC;cAAApM,QAAA,gBAClDxE,OAAA,CAACR,cAAc;gBACb8R,OAAO,EAAC,OAAO;gBACfC,SAAS,EAAC,OAAO;gBACjBC,OAAO,EAAEpN,eAAgB;gBACzBqN,SAAS;gBAAAjN,QAAA,eAETxE,OAAA;kBACE4Q,SAAS,EAAC,kBAAkB;kBAC5BtM,KAAK,EAAE;oBAAEoN,MAAM,EAAE,SAAS;oBAAEC,OAAO,EAAE;kBAAe,CAAE;kBAAAnN,QAAA,eAEtDxE,OAAA;oBACE4R,GAAG,EAAC,mBAAmB;oBACvBC,GAAG,EAAC,yBAAyB;oBAC7BvN,KAAK,EAAE;sBAAEwN,KAAK,EAAE,MAAM;sBAAEC,MAAM,EAAE;oBAAO;kBAAE;oBAAApN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC,eAGjB9E,OAAA;gBAAK4Q,SAAS,EAAC,6BAA6B;gBAAApM,QAAA,gBAC1CxE,OAAA;kBAAK4Q,SAAS,EAAC,wDAAwD;kBAAApM,QAAA,gBACrExE,OAAA;oBAAM4Q,SAAS,EAAC,MAAM;oBAACtM,KAAK,EAAE;sBAAE0N,QAAQ,EAAE,QAAQ;sBAAEC,UAAU,EAAE,KAAK;sBAAEC,KAAK,EAAE;oBAAU,CAAE;oBAAA1N,QAAA,EAAC;kBAE3F;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACL9E,OAAA,CAACR,cAAc;oBACb+R,SAAS,EAAC,OAAO;oBACjBY,KAAK,EAAE;sBAAEC,IAAI,EAAE,GAAG;sBAAEC,IAAI,EAAE;oBAAI,CAAE;oBAChCb,OAAO,eACLxR,OAAA,CAACP,OAAO;sBAAC4E,EAAE,EAAC,mCAAmC;sBAACC,KAAK,EAAE;wBAAEC,QAAQ,EAAE;sBAAQ,CAAE;sBAAAC,QAAA,gBAC3ExE,OAAA,CAACP,OAAO,CAACgF,MAAM;wBAACC,EAAE,EAAC,IAAI;wBAAAF,QAAA,gBACrBxE,OAAA;0BAAG4Q,SAAS,EAAC;wBAAgC;0BAAAjM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,+BAEpD;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAgB,CAAC,eACjB9E,OAAA,CAACP,OAAO,CAACsF,IAAI;wBAAAP,QAAA,gBACXxE,OAAA;0BAAK4Q,SAAS,EAAC,MAAM;0BAAApM,QAAA,gBACnBxE,OAAA;4BAAK4Q,SAAS,EAAC,MAAM;4BAAApM,QAAA,gBACnBxE,OAAA;8BAAG4Q,SAAS,EAAC;4BAAoC;8BAAAjM,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACtD9E,OAAA;8BAAAwE,QAAA,EAAQ;4BAAa;8BAAAG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC3B,CAAC,eACN9E,OAAA;4BAAKsE,KAAK,EAAE;8BAAE0N,QAAQ,EAAE,MAAM;8BAAEE,KAAK,EAAE,SAAS;8BAAEI,UAAU,EAAE;4BAAO,CAAE;4BAAA9N,QAAA,GAAC,kDAC3B,eAAAxE,OAAA;8BAAA2E,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,gDACT,eAAA9E,OAAA;8BAAA2E,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,iDAE9C;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eAEN9E,OAAA;0BAAK4Q,SAAS,EAAC,MAAM;0BAAApM,QAAA,gBACnBxE,OAAA;4BAAK4Q,SAAS,EAAC,MAAM;4BAAApM,QAAA,gBACnBxE,OAAA;8BAAG4Q,SAAS,EAAC;4BAAuC;8BAAAjM,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACzD9E,OAAA;8BAAAwE,QAAA,EAAQ;4BAAe;8BAAAG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC7B,CAAC,eACN9E,OAAA;4BAAKsE,KAAK,EAAE;8BAAE0N,QAAQ,EAAE,MAAM;8BAAEE,KAAK,EAAE,SAAS;8BAAEI,UAAU,EAAE;4BAAO,CAAE;4BAAA9N,QAAA,GAAC,sCACvC,eAAAxE,OAAA;8BAAA2E,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,oCACT,eAAA9E,OAAA;8BAAA2E,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,sDAElC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eAEN9E,OAAA;0BAAK4Q,SAAS,EAAC,iCAAiC;0BAACtM,KAAK,EAAE;4BAAE0N,QAAQ,EAAE;0BAAO,CAAE;0BAAAxN,QAAA,gBAC3ExE,OAAA;4BAAG4Q,SAAS,EAAC;0BAAuB;4BAAAjM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACzC9E,OAAA;4BAAAwE,QAAA,EAAQ;0BAAe;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,0GAElC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CACV;oBAAAN,QAAA,eAEDxE,OAAA;sBAAM4Q,SAAS,EAAC,mBAAmB;sBAAApM,QAAA,eACjCxE,OAAA;wBAAM4Q,SAAS,EAAC,+BAA+B;wBAC5CtM,KAAK,EAAE;0BACL0N,QAAQ,EAAE,MAAM;0BAChBN,MAAM,EAAE,MAAM;0BACdQ,KAAK,EAAE,SAAS;0BAChBP,OAAO,EAAE,aAAa;0BACtBY,UAAU,EAAE,QAAQ;0BACpBC,cAAc,EAAE,QAAQ;0BACxB/L,QAAQ,EAAE,UAAU;0BACpBgM,MAAM,EAAE,MAAM;0BACdR,UAAU,EAAE;wBACd,CAAE;wBAAAzN,QAAA,EACJ;sBAAC;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC,eACN9E,OAAA;kBAAK4Q,SAAS,EAAC,2BAA2B;kBAAApM,QAAA,gBACxCxE,OAAA;oBACE4Q,SAAS,EAAC,oBAAoB;oBAC9B8B,OAAO,EAAE/B,8BAA+B;oBACxCrM,KAAK,EAAE;sBACLwN,KAAK,EAAE,MAAM;sBACbC,MAAM,EAAE,MAAM;sBACdY,eAAe,EAAElP,yBAAyB,GAAG,SAAS,GAAG,SAAS;sBAClEmP,YAAY,EAAE,MAAM;sBACpBnM,QAAQ,EAAE,UAAU;sBACpBiL,MAAM,EAAE,SAAS;sBACjBmB,UAAU,EAAE,4BAA4B;sBACxCC,MAAM,EAAE;oBACV,CAAE;oBAAAtO,QAAA,gBAEFxE,OAAA;sBACE4Q,SAAS,EAAC,eAAe;sBACzBtM,KAAK,EAAE;wBACLwN,KAAK,EAAE,MAAM;wBACbC,MAAM,EAAE,MAAM;wBACdY,eAAe,EAAE,OAAO;wBACxBC,YAAY,EAAE,KAAK;wBACnBnM,QAAQ,EAAE,UAAU;wBACpBsM,GAAG,EAAE,KAAK;wBACVC,IAAI,EAAEvP,yBAAyB,GAAG,MAAM,GAAG,KAAK;wBAChDoP,UAAU,EAAE,gBAAgB;wBAC5BI,SAAS,EAAE;sBACb;oBAAE;sBAAAtO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACF9E,OAAA;sBACEsE,KAAK,EAAE;wBACLmC,QAAQ,EAAE,UAAU;wBACpBsM,GAAG,EAAE,KAAK;wBACVC,IAAI,EAAEvP,yBAAyB,GAAG,KAAK,GAAG,MAAM;wBAChDyP,SAAS,EAAE,kBAAkB;wBAC7BlB,QAAQ,EAAE,MAAM;wBAChBC,UAAU,EAAE,KAAK;wBACjBC,KAAK,EAAE,OAAO;wBACdW,UAAU,EAAE,eAAe;wBAC3BM,UAAU,EAAE;sBACd,CAAE;sBAAA3O,QAAA,EAEDf,yBAAyB,GAAG,IAAI,GAAG;oBAAK;sBAAAkB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACN9E,OAAA;oBAAO4Q,SAAS,EAAC,YAAY;oBAACtM,KAAK,EAAE;sBAAE0N,QAAQ,EAAE;oBAAO,CAAE;oBAAAxN,QAAA,EACvDf,yBAAyB,GAAG,4BAA4B,GAAG;kBAAsB;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGH,CAAC,eAER9E,OAAA;cAAK4Q,SAAS,EAAC,MAAM;cAAApM,QAAA,gBACnBxE,OAAA,CAACb,IAAI,CAACgS,KAAK;gBAAA3M,QAAA,EAAC;cAAY;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACrC9E,OAAA;gBAAK4Q,SAAS,EAAC,mBAAmB;gBAAApM,QAAA,gBAChCxE,OAAA,CAACd,MAAM;kBACLkU,OAAO,EAAE3R,YAAY,GAAG,SAAS,GAAG,iBAAkB;kBACtDiR,OAAO,EAAEnJ,YAAa;kBACtB8J,QAAQ,EAAElR,eAAgB;kBAAAqC,QAAA,EAEzBrC,eAAe,gBACdnC,OAAA,CAAAE,SAAA;oBAAAsE,QAAA,gBACExE,OAAA,CAACT,OAAO;sBAACmF,EAAE,EAAC,MAAM;sBAAC4O,SAAS,EAAC,QAAQ;sBAACC,IAAI,EAAC,IAAI;sBAAC9I,IAAI,EAAC,QAAQ;sBAAC,eAAY;oBAAM;sBAAA9F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACnF9E,OAAA;sBAAM4Q,SAAS,EAAC,MAAM;sBAAApM,QAAA,EAAC;oBAAmB;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA,eACjD,CAAC,GAEHrD,YAAY,GAAG,gBAAgB,GAAG;gBACnC;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC,eACT9E,OAAA;kBAAK4Q,SAAS,EAAC,sBAAsB;kBAAApM,QAAA,eACnCxE,OAAA;oBAAO4Q,SAAS,EAAC,kBAAkB;oBAAApM,QAAA,GAAC,cAElC,eAAAxE,OAAA;sBACE8N,IAAI,EAAC,MAAM;sBACX8C,SAAS,EAAC,YAAY;sBACtB4C,MAAM,EAAC,SAAS;sBAChBnC,QAAQ,EAAE1J,gBAAiB;sBAC3B8L,GAAG,EAAEvP,YAAa;sBAClBmP,QAAQ,EAAE5R,YAAa;sBACvBiS,QAAQ;oBAAA;sBAAA/O,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGLrD,YAAY,iBACXzB,OAAA;gBAAK4Q,SAAS,EAAC,sBAAsB;gBAAApM,QAAA,gBACnCxE,OAAA;kBAAO4Q,SAAS,EAAC,YAAY;kBAAApM,QAAA,gBAC3BxE,OAAA;oBAAAwE,QAAA,EAAQ;kBAAgB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EAChC/C,kBAAkB,KAAK,SAAS,iBAAI/B,OAAA;oBAAM4Q,SAAS,EAAC,mBAAmB;oBAAApM,QAAA,EAAC;kBAAS;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EACxF/C,kBAAkB,KAAK,QAAQ,iBAAI/B,OAAA;oBAAM4Q,SAAS,EAAC,kBAAkB;oBAAApM,QAAA,EAAC;kBAAQ;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EACrF/C,kBAAkB,KAAK,QAAQ,iBAAI/B,OAAA;oBAAM4Q,SAAS,EAAC,mBAAmB;oBAAApM,QAAA,EAAC;kBAAe;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EAC7F/C,kBAAkB,KAAK,SAAS,iBAAI/B,OAAA;oBAAM4Q,SAAS,EAAC,qBAAqB;oBAAApM,QAAA,EAAC;kBAAS;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtF,CAAC,EACP,CAACnD,WAAW,KAAK,eAAe,IAAIyH,MAAM,CAACC,IAAI,CAAC1I,gBAAgB,CAAC,CAACsH,MAAM,GAAG,CAAC,kBAC3EjI,OAAA;kBAAK4Q,SAAS,EAAC,MAAM;kBAAApM,QAAA,gBACnBxE,OAAA;oBAAO4Q,SAAS,EAAC,YAAY;oBAAApM,QAAA,gBAC3BxE,OAAA;sBAAAwE,QAAA,EAAQ;oBAAiB;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,eAAA9E,OAAA;sBAAM4Q,SAAS,EAAC,cAAc;sBAAApM,QAAA,EAAE7C;oBAAW;sBAAAgD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjF,CAAC,EACPsE,MAAM,CAACC,IAAI,CAAC1I,gBAAgB,CAAC,CAACsH,MAAM,GAAG,CAAC,iBACvCjI,OAAA;oBAAK4Q,SAAS,EAAC,MAAM;oBAAApM,QAAA,eACnBxE,OAAA;sBAAO4Q,SAAS,EAAC,YAAY;sBAAApM,QAAA,gBAC3BxE,OAAA;wBAAAwE,QAAA,EAAQ;sBAAwB;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,eAAA9E,OAAA;wBAAM4Q,SAAS,EAAC,cAAc;wBAAApM,QAAA,EAAE2E,uBAAuB,CAAC;sBAAC;wBAAAxE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN,EACA7C,aAAa,iBACZjC,OAAA,CAACV,KAAK;kBAAC8T,OAAO,EAAC,SAAS;kBAACxC,SAAS,EAAC,WAAW;kBAACtM,KAAK,EAAE;oBAAE0N,QAAQ,EAAE;kBAAW,CAAE;kBAAAxN,QAAA,gBAC7ExE,OAAA,CAACV,KAAK,CAACqU,OAAO;oBAACjP,EAAE,EAAC,IAAI;oBAAAF,QAAA,EAAC;kBAAqB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAe,CAAC,eAC5D9E,OAAA;oBAAKsE,KAAK,EAAE;sBAAEsP,UAAU,EAAE;oBAAW,CAAE;oBAAApP,QAAA,EAAEvC;kBAAa;oBAAA0C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC7D9E,OAAA;oBAAA2E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACN9E,OAAA;oBAAK4Q,SAAS,EAAC,4BAA4B;oBAAApM,QAAA,eACzCxE,OAAA,CAACd,MAAM;sBAACkU,OAAO,EAAC,iBAAiB;sBAACG,IAAI,EAAC,IAAI;sBAACb,OAAO,EAAE1L,qBAAsB;sBAAAxC,QAAA,EAAC;oBAE5E;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CACR;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EAELrD,YAAY,iBACXzB,OAAA;cAAK4Q,SAAS,EAAC,uBAAuB;cAAApM,QAAA,gBACpCxE,OAAA,CAACJ,MAAM;gBACLiU,KAAK,EAAE,KAAM;gBACbJ,GAAG,EAAExP,SAAU;gBACf6P,gBAAgB,EAAC,YAAY;gBAC7BlD,SAAS,EAAC,QAAQ;gBAClBmD,gBAAgB,EAAE;kBAChBjC,KAAK,EAAE,GAAG;kBACVC,MAAM,EAAE,GAAG;kBACXiC,UAAU,EAAEnS;gBACd;cAAE;gBAAA8C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACDX,QAAQ,iBACPnE,OAAA,CAACd,MAAM;gBACLkU,OAAO,EAAC,mBAAmB;gBAC3BV,OAAO,EAAEjJ,uBAAwB;gBACjCmH,SAAS,EAAC,WAAW;gBACrB2C,IAAI,EAAC,IAAI;gBAAA/O,QAAA,EACV;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACT,eACD9E,OAAA,CAACd,MAAM;gBACLkU,OAAO,EAAC,SAAS;gBACjBV,OAAO,EAAEhK,aAAc;gBACvBkI,SAAS,EAAC,MAAM;gBAAApM,QAAA,EACjB;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN,EAEAsE,MAAM,CAACC,IAAI,CAAC1I,gBAAgB,CAAC,CAACsH,MAAM,GAAG,CAAC,iBACvCjI,OAAA;cAAK4Q,SAAS,EAAC,8BAA8B;cAAApM,QAAA,gBAC3CxE,OAAA;gBAAAwE,QAAA,EAAI;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjB9E,OAAA;gBAAK4Q,SAAS,EAAC,eAAe;gBAAApM,QAAA,EAC3B4E,MAAM,CAAC6K,OAAO,CAACtT,gBAAgB,CAAC,CAACuO,GAAG,CAAC,CAAC,CAAC3J,IAAI,EAAE2O,OAAO,CAAC,EAAEC,KAAK,kBAC3DnU,OAAA;kBAEE4Q,SAAS,EAAE,mBAAmBuD,KAAK,KAAKpT,iBAAiB,GAAG,UAAU,GAAG,EAAE,EAAG;kBAC9E2R,OAAO,EAAEA,CAAA,KAAM1R,oBAAoB,CAACmT,KAAK,CAAE;kBAAA3P,QAAA,gBAE3CxE,OAAA;oBACE4R,GAAG,EAAEsC,OAAQ;oBACbrC,GAAG,EAAE,WAAWsC,KAAK,GAAG,CAAC,EAAG;oBAC5BvD,SAAS,EAAC;kBAAe;oBAAAjM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CAAC,eACF9E,OAAA;oBACE4Q,SAAS,EAAC,oCAAoC;oBAC9C8B,OAAO,EAAG9K,CAAC,IAAK;sBACdA,CAAC,CAACwM,eAAe,CAAC,CAAC;sBACnB,MAAMC,QAAQ,GAAG5T,UAAU,CAACmM,MAAM,CAAC,CAAC0H,CAAC,EAAElI,CAAC,KAAKA,CAAC,KAAK+H,KAAK,CAAC;sBACzD,MAAMI,cAAc,GAAG;wBAAC,GAAG5T;sBAAgB,CAAC;sBAC5C,MAAM6T,cAAc,GAAG;wBAAC,GAAG3T;sBAAgB,CAAC;sBAC5C,OAAO0T,cAAc,CAAChP,IAAI,CAAC;sBAC3B,OAAOiP,cAAc,CAACjP,IAAI,CAAC;sBAC3B7E,aAAa,CAAC2T,QAAQ,CAAC;sBACvBzT,mBAAmB,CAAC2T,cAAc,CAAC;sBACnCzT,mBAAmB,CAAC0T,cAAc,CAAC;sBACnC,IAAIzT,iBAAiB,IAAIsT,QAAQ,CAACpM,MAAM,EAAE;wBACxCjH,oBAAoB,CAACyT,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEL,QAAQ,CAACpM,MAAM,GAAG,CAAC,CAAC,CAAC;sBACxD;oBACF,CAAE;oBAAAzD,QAAA,EACH;kBAED;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA,GA3BJS,IAAI;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA4BN,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN9E,OAAA;gBAAK4Q,SAAS,EAAC,uBAAuB;gBAAApM,QAAA,EACnC4E,MAAM,CAACe,MAAM,CAACxJ,gBAAgB,CAAC,CAACI,iBAAiB,CAAC,iBACjDf,OAAA;kBACE4R,GAAG,EAAExI,MAAM,CAACe,MAAM,CAACxJ,gBAAgB,CAAC,CAACI,iBAAiB,CAAE;kBACxD8Q,GAAG,EAAC,iBAAiB;kBACrBjB,SAAS,EAAC;gBAAyB;kBAAAjM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC;cACF;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN,EAEAvD,KAAK,iBAAIvB,OAAA,CAACV,KAAK;cAAC8T,OAAO,EAAC,QAAQ;cAAA5O,QAAA,EAAEjD;YAAK;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAEjD9E,OAAA;cAAK4Q,SAAS,EAAC,mBAAmB;cAAApM,QAAA,gBAChCxE,OAAA,CAACd,MAAM;gBACLkU,OAAO,EAAC,SAAS;gBACjBV,OAAO,EAAE/I,aAAc;gBACvB0J,QAAQ,EAAEjK,MAAM,CAACC,IAAI,CAAC1I,gBAAgB,CAAC,CAACsH,MAAM,KAAK,CAAC,IAAI5G,OAAO,IAAIkB,eAAgB;gBAAAiC,QAAA,EAElFnD,OAAO,gBACNrB,OAAA,CAAAE,SAAA;kBAAAsE,QAAA,gBACExE,OAAA,CAACT,OAAO;oBAACmF,EAAE,EAAC,MAAM;oBAAC4O,SAAS,EAAC,QAAQ;oBAACC,IAAI,EAAC,IAAI;oBAAC9I,IAAI,EAAC,QAAQ;oBAAC,eAAY;kBAAM;oBAAA9F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACnF9E,OAAA;oBAAM4Q,SAAS,EAAC,MAAM;oBAAApM,QAAA,EAAC;kBAAY;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA,eAC1C,CAAC,GAEH;cACD;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC,eAET9E,OAAA,CAACd,MAAM;gBACLkU,OAAO,EAAC,SAAS;gBACjBV,OAAO,EAAEvG,gBAAiB;gBAC1BkH,QAAQ,EAAEjK,MAAM,CAACC,IAAI,CAAC1I,gBAAgB,CAAC,CAACsH,MAAM,KAAK,CAAC,IAAI5G,OAAO,IAAIkB,eAAgB;gBAAAiC,QAAA,EAElFjC,eAAe,gBACdvC,OAAA,CAAAE,SAAA;kBAAAsE,QAAA,gBACExE,OAAA,CAACT,OAAO;oBAACmF,EAAE,EAAC,MAAM;oBAAC4O,SAAS,EAAC,QAAQ;oBAACC,IAAI,EAAC,IAAI;oBAAC9I,IAAI,EAAC,QAAQ;oBAAC,eAAY;kBAAM;oBAAA9F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACnF9E,OAAA;oBAAM4Q,SAAS,EAAC,MAAM;oBAAApM,QAAA,GAAC,aAAW,EAAC/B,cAAc,EAAC,GAAC,EAACQ,cAAc;kBAAA;oBAAA0B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA,eAC1E,CAAC,GAEH;cACD;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC,eAET9E,OAAA,CAACd,MAAM;gBACLkU,OAAO,EAAC,WAAW;gBACnBV,OAAO,EAAE1F,WAAY;gBACrBqG,QAAQ,EAAEhS,OAAO,IAAIkB,eAAgB;gBAAAiC,QAAA,EACtC;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,EAINzC,YAAY,CAACsS,IAAI,CAACnM,MAAM;UAAA,IAAAoM,qBAAA;UAAA,OAAIpM,MAAM,CAACuC,OAAO,IAAIvC,MAAM,CAAC0C,SAAS,IAAI,EAAA0J,qBAAA,GAAApM,MAAM,CAACqD,eAAe,cAAA+I,qBAAA,uBAAtBA,qBAAA,CAAwB9I,KAAK,IAAG,CAAC;QAAA,EAAC,iBACnG9L,OAAA,CAACf,IAAI;UAAC2R,SAAS,EAAC,MAAM;UAAApM,QAAA,gBACpBxE,OAAA,CAACf,IAAI,CAACwF,MAAM;YAAAD,QAAA,eACVxE,OAAA;cAAK4Q,SAAS,EAAC,mDAAmD;cAAApM,QAAA,gBAChExE,OAAA;gBAAI4Q,SAAS,EAAC,MAAM;gBAAApM,QAAA,gBAClBxE,OAAA;kBAAG4Q,SAAS,EAAC;gBAAmB;kBAAAjM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,8BAEvC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL9E,OAAA;gBAAK4Q,SAAS,EAAC,cAAc;gBAAApM,QAAA,eAC3BxE,OAAA,CAACd,MAAM;kBACLkU,OAAO,EAAC,SAAS;kBACjBG,IAAI,EAAC,IAAI;kBACTb,OAAO,EAAE/E,WAAY;kBACrBsD,KAAK,EAAC,uBAAuB;kBAAAzM,QAAA,gBAE7BxE,OAAA;oBAAG4Q,SAAS,EAAC;kBAAsB;oBAAAjM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,cAE1C;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC,eACd9E,OAAA,CAACf,IAAI,CAAC8F,IAAI;YAAAP,QAAA,gBAENxE,OAAA;cAAK4Q,SAAS,EAAC,MAAM;cAAApM,QAAA,eACnBxE,OAAA;gBAAK4Q,SAAS,EAAC,wBAAwB;gBAAApM,QAAA,gBACrCxE,OAAA,CAACd,MAAM;kBACLkU,OAAO,EAAEzP,oBAAoB,KAAK,KAAK,GAAG,SAAS,GAAG,iBAAkB;kBACxE4P,IAAI,EAAC,IAAI;kBACTb,OAAO,EAAEA,CAAA,KAAM9O,uBAAuB,CAAC,KAAK,CAAE;kBAAAY,QAAA,EAC/C;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT9E,OAAA,CAACd,MAAM;kBACLkU,OAAO,EAAEzP,oBAAoB,KAAK,UAAU,GAAG,QAAQ,GAAG,gBAAiB;kBAC3E4P,IAAI,EAAC,IAAI;kBACTb,OAAO,EAAEA,CAAA,KAAM9O,uBAAuB,CAAC,UAAU,CAAE;kBAAAY,QAAA,EACpD;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT9E,OAAA,CAACd,MAAM;kBACLkU,OAAO,EAAEzP,oBAAoB,KAAK,QAAQ,GAAG,SAAS,GAAG,iBAAkB;kBAC3E4P,IAAI,EAAC,IAAI;kBACTb,OAAO,EAAEA,CAAA,KAAM9O,uBAAuB,CAAC,QAAQ,CAAE;kBAAAY,QAAA,EAClD;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT9E,OAAA,CAACd,MAAM;kBACLkU,OAAO,EAAEzP,oBAAoB,KAAK,OAAO,GAAG,MAAM,GAAG,cAAe;kBACpE4P,IAAI,EAAC,IAAI;kBACTb,OAAO,EAAEA,CAAA,KAAM9O,uBAAuB,CAAC,OAAO,CAAE;kBAAAY,QAAA,EACjD;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN9E,OAAA;cAAK4Q,SAAS,EAAC,8BAA8B;cAAApM,QAAA,eAC3CxE,OAAA;gBAAK4Q,SAAS,EAAC,KAAK;gBAAApM,QAAA,gBAClBxE,OAAA;kBAAK4Q,SAAS,EAAC,UAAU;kBAAApM,QAAA,eACvBxE,OAAA;oBAAK4Q,SAAS,EAAC,2BAA2B;oBAAApM,QAAA,eACxCxE,OAAA;sBAAK4Q,SAAS,EAAC,4BAA4B;sBAAApM,QAAA,gBACzCxE,OAAA;wBAAI4Q,SAAS,EAAC,MAAM;wBAAApM,QAAA,EAAC;sBAAc;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACxC9E,OAAA;wBAAI4Q,SAAS,EAAC,MAAM;wBAAApM,QAAA,EACjBnC,YAAY,CAACwS,MAAM,CAAC,CAACC,GAAG,EAAEtM,MAAM;0BAAA,IAAAuM,sBAAA;0BAAA,OAAKD,GAAG,IAAI,EAAAC,sBAAA,GAAAvM,MAAM,CAACqD,eAAe,cAAAkJ,sBAAA,uBAAtBA,sBAAA,CAAwBvJ,QAAQ,KAAI,CAAC,CAAC;wBAAA,GAAE,CAAC;sBAAC;wBAAA7G,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrF,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN9E,OAAA;kBAAK4Q,SAAS,EAAC,UAAU;kBAAApM,QAAA,eACvBxE,OAAA;oBAAK4Q,SAAS,EAAC,4BAA4B;oBAAApM,QAAA,eACzCxE,OAAA;sBAAK4Q,SAAS,EAAC,4BAA4B;sBAAApM,QAAA,gBACzCxE,OAAA;wBAAI4Q,SAAS,EAAC,MAAM;wBAAApM,QAAA,EAAC;sBAAY;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACtC9E,OAAA;wBAAI4Q,SAAS,EAAC,MAAM;wBAAApM,QAAA,EACjBnC,YAAY,CAACwS,MAAM,CAAC,CAACC,GAAG,EAAEtM,MAAM;0BAAA,IAAAwM,sBAAA;0BAAA,OAAKF,GAAG,IAAI,EAAAE,sBAAA,GAAAxM,MAAM,CAACqD,eAAe,cAAAmJ,sBAAA,uBAAtBA,sBAAA,CAAwBvJ,MAAM,KAAI,CAAC,CAAC;wBAAA,GAAE,CAAC;sBAAC;wBAAA9G,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnF,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN9E,OAAA;kBAAK4Q,SAAS,EAAC,UAAU;kBAAApM,QAAA,eACvBxE,OAAA;oBAAK4Q,SAAS,EAAC,yBAAyB;oBAAApM,QAAA,eACtCxE,OAAA;sBAAK4Q,SAAS,EAAC,4BAA4B;sBAAApM,QAAA,gBACzCxE,OAAA;wBAAI4Q,SAAS,EAAC,MAAM;wBAAApM,QAAA,EAAC;sBAAW;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACrC9E,OAAA;wBAAI4Q,SAAS,EAAC,MAAM;wBAAApM,QAAA,EACjBnC,YAAY,CAACwS,MAAM,CAAC,CAACC,GAAG,EAAEtM,MAAM;0BAAA,IAAAyM,sBAAA;0BAAA,OAAKH,GAAG,IAAI,EAAAG,sBAAA,GAAAzM,MAAM,CAACqD,eAAe,cAAAoJ,sBAAA,uBAAtBA,sBAAA,CAAwBvJ,KAAK,KAAI,CAAC,CAAC;wBAAA,GAAE,CAAC;sBAAC;wBAAA/G,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClF,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN9E,OAAA;kBAAK4Q,SAAS,EAAC,UAAU;kBAAApM,QAAA,eACvBxE,OAAA;oBAAK4Q,SAAS,EAAC,4BAA4B;oBAAApM,QAAA,eACzCxE,OAAA;sBAAK4Q,SAAS,EAAC,4BAA4B;sBAAApM,QAAA,gBACzCxE,OAAA;wBAAI4Q,SAAS,EAAC,MAAM;wBAAApM,QAAA,EAAC;sBAAgB;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC1C9E,OAAA;wBAAI4Q,SAAS,EAAC,MAAM;wBAAApM,QAAA,EACjBnC,YAAY,CAACwS,MAAM,CAAC,CAACC,GAAG,EAAEtM,MAAM;0BAAA,IAAA0M,sBAAA;0BAAA,OAAKJ,GAAG,IAAI,EAAAI,sBAAA,GAAA1M,MAAM,CAACqD,eAAe,cAAAqJ,sBAAA,uBAAtBA,sBAAA,CAAwBpJ,KAAK,KAAI,CAAC,CAAC;wBAAA,GAAE,CAAC;sBAAC;wBAAAnH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClF,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN9E,OAAA;cAAK4Q,SAAS,EAAC,4CAA4C;cAAApM,QAAA,EACxD,CAAC,MAAM;gBACN;gBACA,MAAMoJ,aAAa,GAAG,EAAE;gBAExBvL,YAAY,CAAC6F,OAAO,CAACM,MAAM,IAAI;kBAC7B,IAAIA,MAAM,CAACuC,OAAO,IAAIvC,MAAM,CAAC0C,SAAS,IAAI1C,MAAM,CAAC+C,gBAAgB,EAAE;oBACjE,MAAM;sBAAEC,QAAQ;sBAAEC,MAAM;sBAAEC;oBAAM,CAAC,GAAGlD,MAAM,CAAC+C,gBAAgB;;oBAE3D;oBACA,IAAI5H,oBAAoB,KAAK,KAAK,IAAIA,oBAAoB,KAAK,UAAU,EAAE;sBACzE6H,QAAQ,CAACtD,OAAO,CAAC2F,OAAO,IAAI;wBAC1BD,aAAa,CAACrB,IAAI,CAAC;0BACjB,GAAGsB,OAAO;0BACVC,IAAI,EAAE,SAAS;0BACf7E,QAAQ,EAAET,MAAM,CAACS,QAAQ;0BACzB1I,aAAa,EAAE;wBACjB,CAAC,CAAC;sBACJ,CAAC,CAAC;oBACJ;;oBAEA;oBACA,IAAIoD,oBAAoB,KAAK,KAAK,IAAIA,oBAAoB,KAAK,QAAQ,EAAE;sBACvE8H,MAAM,CAACvD,OAAO,CAACwG,KAAK,IAAI;wBACtBd,aAAa,CAACrB,IAAI,CAAC;0BACjB,GAAGmC,KAAK;0BACRZ,IAAI,EAAE,OAAO;0BACb7E,QAAQ,EAAET,MAAM,CAACS,QAAQ;0BACzB1I,aAAa,EAAE;wBACjB,CAAC,CAAC;sBACJ,CAAC,CAAC;oBACJ;;oBAEA;oBACA,IAAIoD,oBAAoB,KAAK,KAAK,IAAIA,oBAAoB,KAAK,OAAO,EAAE;sBACtE+H,KAAK,CAACxD,OAAO,CAAC0G,IAAI,IAAI;wBACpBhB,aAAa,CAACrB,IAAI,CAAC;0BACjB,GAAGqC,IAAI;0BACPd,IAAI,EAAE,MAAM;0BACZ7E,QAAQ,EAAET,MAAM,CAACS,QAAQ;0BACzB1I,aAAa,EAAE;wBACjB,CAAC,CAAC;sBACJ,CAAC,CAAC;oBACJ;kBACF;gBACF,CAAC,CAAC;gBAEF,IAAIqN,aAAa,CAAC3F,MAAM,KAAK,CAAC,EAAE;kBAC9B,oBACEjI,OAAA;oBAAK4Q,SAAS,EAAC,kBAAkB;oBAAApM,QAAA,gBAC/BxE,OAAA;sBAAK4Q,SAAS,EAAC,MAAM;sBAAApM,QAAA,eACnBxE,OAAA;wBAAG4Q,SAAS,EAAC;sBAAgC;wBAAAjM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/C,CAAC,eACN9E,OAAA;sBAAI4Q,SAAS,EAAC,YAAY;sBAAApM,QAAA,EAAC;oBAAmB;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACnD9E,OAAA;sBAAG4Q,SAAS,EAAC,iBAAiB;sBAAApM,QAAA,EAC3Bb,oBAAoB,KAAK,KAAK,GAC3B,mDAAmD,GACnD,MAAMA,oBAAoB;oBAAyC;sBAAAgB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAEV;gBAEA,oBACE9E,OAAA;kBAAO4Q,SAAS,EAAC,oCAAoC;kBAAApM,QAAA,gBACnDxE,OAAA;oBAAAwE,QAAA,eACExE,OAAA;sBAAAwE,QAAA,gBACExE,OAAA;wBAAAwE,QAAA,EAAI;sBAAc;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACvB9E,OAAA;wBAAAwE,QAAA,EAAI;sBAAe;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACxB9E,OAAA;wBACEsE,KAAK,EAAE;0BAAEoN,MAAM,EAAE;wBAAU,CAAE;wBAC7BgB,OAAO,EAAEA,CAAA,KAAMxF,UAAU,CAAC,eAAe,CAAE;wBAAA1I,QAAA,GAC5C,OACM,EAACX,UAAU,CAACE,GAAG,KAAK,eAAe,iBACtC/D,OAAA;0BAAG4Q,SAAS,EAAE,eAAe/M,UAAU,CAACG,SAAS,KAAK,KAAK,GAAG,IAAI,GAAG,MAAM;wBAAQ;0BAAAW,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CACxF;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC,eACL9E,OAAA;wBAAAwE,QAAA,EAAI;sBAAE;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,EACV,CAACnB,oBAAoB,KAAK,KAAK,IAAIA,oBAAoB,KAAK,UAAU,kBACrE3D,OAAA,CAAAE,SAAA;wBAAAsE,QAAA,gBACExE,OAAA;0BACEsE,KAAK,EAAE;4BAAEoN,MAAM,EAAE;0BAAU,CAAE;0BAC7BgB,OAAO,EAAEA,CAAA,KAAMxF,UAAU,CAAC,UAAU,CAAE;0BAAA1I,QAAA,GACvC,gBACY,EAACX,UAAU,CAACE,GAAG,KAAK,UAAU,iBACvC/D,OAAA;4BAAG4Q,SAAS,EAAE,eAAe/M,UAAU,CAACG,SAAS,KAAK,KAAK,GAAG,IAAI,GAAG,MAAM;0BAAQ;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CACxF;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC,eACL9E,OAAA;0BACEsE,KAAK,EAAE;4BAAEoN,MAAM,EAAE;0BAAU,CAAE;0BAC7BgB,OAAO,EAAEA,CAAA,KAAMxF,UAAU,CAAC,UAAU,CAAE;0BAAA1I,QAAA,GACvC,aACY,EAACX,UAAU,CAACE,GAAG,KAAK,UAAU,iBACvC/D,OAAA;4BAAG4Q,SAAS,EAAE,eAAe/M,UAAU,CAACG,SAAS,KAAK,KAAK,GAAG,IAAI,GAAG,MAAM;0BAAQ;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CACxF;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC,eACL9E,OAAA;0BACEsE,KAAK,EAAE;4BAAEoN,MAAM,EAAE;0BAAU,CAAE;0BAC7BgB,OAAO,EAAEA,CAAA,KAAMxF,UAAU,CAAC,QAAQ,CAAE;0BAAA1I,QAAA,GACrC,kBACc,EAACX,UAAU,CAACE,GAAG,KAAK,QAAQ,iBACvC/D,OAAA;4BAAG4Q,SAAS,EAAE,eAAe/M,UAAU,CAACG,SAAS,KAAK,KAAK,GAAG,IAAI,GAAG,MAAM;0BAAQ;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CACxF;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC,eACL9E,OAAA;0BAAAwE,QAAA,EAAI;wBAAY;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC;sBAAA,eACrB,CACH,EACA,CAACnB,oBAAoB,KAAK,KAAK,IAAIA,oBAAoB,KAAK,QAAQ,kBACnE3D,OAAA,CAAAE,SAAA;wBAAAsE,QAAA,gBACExE,OAAA;0BACEsE,KAAK,EAAE;4BAAEoN,MAAM,EAAE;0BAAU,CAAE;0BAC7BgB,OAAO,EAAEA,CAAA,KAAMxF,UAAU,CAAC,YAAY,CAAE;0BAAA1I,QAAA,GACzC,aACY,EAACX,UAAU,CAACE,GAAG,KAAK,YAAY,iBACzC/D,OAAA;4BAAG4Q,SAAS,EAAE,eAAe/M,UAAU,CAACG,SAAS,KAAK,KAAK,GAAG,IAAI,GAAG,MAAM;0BAAQ;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CACxF;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC,eACL9E,OAAA;0BACEsE,KAAK,EAAE;4BAAEoN,MAAM,EAAE;0BAAU,CAAE;0BAC7BgB,OAAO,EAAEA,CAAA,KAAMxF,UAAU,CAAC,UAAU,CAAE;0BAAA1I,QAAA,GACvC,gBACY,EAACX,UAAU,CAACE,GAAG,KAAK,UAAU,iBACvC/D,OAAA;4BAAG4Q,SAAS,EAAE,eAAe/M,UAAU,CAACG,SAAS,KAAK,KAAK,GAAG,IAAI,GAAG,MAAM;0BAAQ;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CACxF;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC,eACL9E,OAAA;0BAAAwE,QAAA,EAAI;wBAAU;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC;sBAAA,eACnB,CACH,EACA,CAACnB,oBAAoB,KAAK,KAAK,IAAIA,oBAAoB,KAAK,OAAO,kBAClE3D,OAAA,CAAAE,SAAA;wBAAAsE,QAAA,gBACExE,OAAA;0BACEsE,KAAK,EAAE;4BAAEoN,MAAM,EAAE;0BAAU,CAAE;0BAC7BgB,OAAO,EAAEA,CAAA,KAAMxF,UAAU,CAAC,WAAW,CAAE;0BAAA1I,QAAA,GACxC,YACW,EAACX,UAAU,CAACE,GAAG,KAAK,WAAW,iBACvC/D,OAAA;4BAAG4Q,SAAS,EAAE,eAAe/M,UAAU,CAACG,SAAS,KAAK,KAAK,GAAG,IAAI,GAAG,MAAM;0BAAQ;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CACxF;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC,eACL9E,OAAA;0BACEsE,KAAK,EAAE;4BAAEoN,MAAM,EAAE;0BAAU,CAAE;0BAC7BgB,OAAO,EAAEA,CAAA,KAAMxF,UAAU,CAAC,WAAW,CAAE;0BAAA1I,QAAA,GACxC,YACW,EAACX,UAAU,CAACE,GAAG,KAAK,WAAW,iBACvC/D,OAAA;4BAAG4Q,SAAS,EAAE,eAAe/M,UAAU,CAACG,SAAS,KAAK,KAAK,GAAG,IAAI,GAAG,MAAM;0BAAQ;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CACxF;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC,eACL9E,OAAA;0BACEsE,KAAK,EAAE;4BAAEoN,MAAM,EAAE;0BAAU,CAAE;0BAC7BgB,OAAO,EAAEA,CAAA,KAAMxF,UAAU,CAAC,UAAU,CAAE;0BAAA1I,QAAA,GACvC,aACY,EAACX,UAAU,CAACE,GAAG,KAAK,UAAU,iBACvC/D,OAAA;4BAAG4Q,SAAS,EAAE,eAAe/M,UAAU,CAACG,SAAS,KAAK,KAAK,GAAG,IAAI,GAAG,MAAM;0BAAQ;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CACxF;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC;sBAAA,eACL,CACH,eACD9E,OAAA;wBACEsE,KAAK,EAAE;0BAAEoN,MAAM,EAAE;wBAAU,CAAE;wBAC7BgB,OAAO,EAAEA,CAAA,KAAMxF,UAAU,CAAC,YAAY,CAAE;wBAAA1I,QAAA,GACzC,aACY,EAACX,UAAU,CAACE,GAAG,KAAK,YAAY,iBACzC/D,OAAA;0BAAG4Q,SAAS,EAAE,eAAe/M,UAAU,CAACG,SAAS,KAAK,KAAK,GAAG,IAAI,GAAG,MAAM;wBAAQ;0BAAAW,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CACxF;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC,eACR9E,OAAA;oBAAAwE,QAAA,EACG2I,cAAc,CAACS,aAAa,CAAC,CAACsB,GAAG,CAAC,CAACC,SAAS,EAAEgF,KAAK,kBAClDnU,OAAA;sBAAAwE,QAAA,gBAEExE,OAAA;wBAAIsE,KAAK,EAAE;0BAAEwN,KAAK,EAAE,OAAO;0BAAEqD,SAAS,EAAE;wBAAS,CAAE;wBAAA3Q,QAAA,EAChD,CAAC,MAAM;0BACN,MAAMgE,MAAM,GAAGnG,YAAY,CAAC+S,IAAI,CAACvI,CAAC,IAAIA,CAAC,CAAC5D,QAAQ,KAAKkG,SAAS,CAAClG,QAAQ,CAAC;0BACxE,MAAM2C,aAAa,GAAGpD,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEoD,aAAa;0BAC3C,OAAOA,aAAa,gBAClB5L,OAAA;4BACE4Q,SAAS,EAAC,2BAA2B;4BACrC8B,OAAO,EAAEA,CAAA,KAAMzF,oBAAoB,CAAC;8BAClCrB,aAAa,EAAEA,aAAa;8BAC5B3K,cAAc,EAAEuH,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEvH,cAAc;8BACtCkK,MAAM,EAAE3C,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE2C,MAAM;8BACtBlC,QAAQ,EAAEkG,SAAS,CAAClG;4BACtB,CAAC,CAAE;4BACHgI,KAAK,EAAC,+BAAqB;4BAAAzM,QAAA,gBAE3BxE,OAAA;8BACE4R,GAAG,EAAEhG,aAAc;8BACnBiG,GAAG,EAAC,UAAU;8BACdjB,SAAS,EAAC;4BAAiB;8BAAAjM,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC5B,CAAC,eACF9E,OAAA;8BAAK4Q,SAAS,EAAC,mBAAmB;8BAAApM,QAAA,EAAC;4BAEnC;8BAAAG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC,gBAEN9E,OAAA;4BAAO4Q,SAAS,EAAC,YAAY;4BAAApM,QAAA,EAAC;0BAAQ;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAC9C;wBACH,CAAC,EAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CAAC,eAGL9E,OAAA;wBAAIsE,KAAK,EAAE;0BAAEwN,KAAK,EAAE,OAAO;0BAAEqD,SAAS,EAAE;wBAAS,CAAE;wBAAA3Q,QAAA,EAChD,CAAC,MAAM;0BACN,MAAMgE,MAAM,GAAGnG,YAAY,CAAC+S,IAAI,CAACvI,CAAC,IAAIA,CAAC,CAAC5D,QAAQ,KAAKkG,SAAS,CAAClG,QAAQ,CAAC;0BACxE,MAAMhI,cAAc,GAAGuH,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEvH,cAAc;0BAC7C,MAAM2K,aAAa,GAAGpD,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEoD,aAAa;0BAE3C,OAAO3K,cAAc,IAAIuH,MAAM,aAANA,MAAM,eAANA,MAAM,CAAE2C,MAAM,gBACrCnL,OAAA;4BACE4Q,SAAS,EAAC,2BAA2B;4BACrC8B,OAAO,EAAEA,CAAA,KAAMzF,oBAAoB,CAAC;8BAClCrB,aAAa,EAAEA,aAAa;8BAC5B3K,cAAc,EAAEA,cAAc;8BAC9BkK,MAAM,EAAE3C,MAAM,CAAC2C,MAAM;8BACrBlC,QAAQ,EAAEkG,SAAS,CAAClG;4BACtB,CAAC,CAAE;4BACHgI,KAAK,EAAC,+BAAqB;4BAAAzM,QAAA,gBAE3BxE,OAAA;8BACE4R,GAAG,EAAE3Q,cAAe;8BACpB4Q,GAAG,EAAC,WAAW;8BACfjB,SAAS,EAAC;4BAAiB;8BAAAjM,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC5B,CAAC,eACF9E,OAAA;8BAAK4Q,SAAS,EAAC,mBAAmB;8BAAApM,QAAA,EAAC;4BAEnC;8BAAAG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC,gBAEN9E,OAAA;4BAAO4Q,SAAS,EAAC,YAAY;4BAAApM,QAAA,EAAC;0BAAkB;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CACxD;wBACH,CAAC,EAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CAAC,eACL9E,OAAA;wBAAAwE,QAAA,eACExE,OAAA;0BAAM4Q,SAAS,EAAE,SACfzB,SAAS,CAAC5O,aAAa,KAAK,UAAU,GAAG,WAAW,GACpD4O,SAAS,CAAC5O,aAAa,KAAK,QAAQ,GAAG,YAAY,GAAG,SAAS,EAC9D;0BAAAiE,QAAA,EACA2K,SAAS,CAACrB;wBAAI;0BAAAnJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACX;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC,eACL9E,OAAA;wBAAAwE,QAAA,EAAK2K,SAAS,CAACpB,UAAU,IAAIoB,SAAS,CAACR,QAAQ,IAAIQ,SAAS,CAACN,OAAO,IAAIsF,KAAK,GAAG;sBAAC;wBAAAxP,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,EAGtF,CAACnB,oBAAoB,KAAK,KAAK,IAAIA,oBAAoB,KAAK,UAAU,kBACrE3D,OAAA,CAAAE,SAAA;wBAAAsE,QAAA,gBACExE,OAAA;0BAAAwE,QAAA,EAAK2K,SAAS,CAACnB,QAAQ,GAAGmB,SAAS,CAACnB,QAAQ,CAACzG,OAAO,CAAC,CAAC,CAAC,GAAG;wBAAK;0BAAA5C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACrE9E,OAAA;0BAAAwE,QAAA,EAAK2K,SAAS,CAAClB,QAAQ,GAAGkB,SAAS,CAAClB,QAAQ,CAAC1G,OAAO,CAAC,CAAC,CAAC,GAAG;wBAAK;0BAAA5C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACrE9E,OAAA;0BAAAwE,QAAA,EAAK2K,SAAS,CAACjB,MAAM,GAAGiB,SAAS,CAACjB,MAAM,CAAC3G,OAAO,CAAC,CAAC,CAAC,GAAG;wBAAK;0BAAA5C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACjE9E,OAAA;0BAAAwE,QAAA,EAAK2K,SAAS,CAAChB,YAAY,IAAI;wBAAK;0BAAAxJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA,eAC1C,CACH,EAGA,CAACnB,oBAAoB,KAAK,KAAK,IAAIA,oBAAoB,KAAK,QAAQ,kBACnE3D,OAAA,CAAAE,SAAA;wBAAAsE,QAAA,gBACExE,OAAA;0BAAAwE,QAAA,EAAK2K,SAAS,CAACf,UAAU,IAAI;wBAAK;0BAAAzJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACxC9E,OAAA;0BAAAwE,QAAA,EAAK2K,SAAS,CAACnB,QAAQ,GAAGmB,SAAS,CAACnB,QAAQ,CAACzG,OAAO,CAAC,CAAC,CAAC,GAAG;wBAAK;0BAAA5C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACrE9E,OAAA;0BAAAwE,QAAA,EAAK2K,SAAS,CAACd,UAAU,IAAI;wBAAK;0BAAA1J,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA,eACxC,CACH,EAGA,CAACnB,oBAAoB,KAAK,KAAK,IAAIA,oBAAoB,KAAK,OAAO,kBAClE3D,OAAA,CAAAE,SAAA;wBAAAsE,QAAA,gBACExE,OAAA;0BAAAwE,QAAA,EAAK2K,SAAS,CAACb,SAAS,IAAI;wBAAK;0BAAA3J,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACvC9E,OAAA;0BAAAwE,QAAA,EAAK2K,SAAS,CAACZ,SAAS,IAAI;wBAAK;0BAAA5J,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACvC9E,OAAA;0BAAAwE,QAAA,EAAK2K,SAAS,CAACX,QAAQ,GAAGW,SAAS,CAACX,QAAQ,CAACjH,OAAO,CAAC,CAAC,CAAC,GAAG;wBAAK;0BAAA5C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA,eACrE,CACH,eAED9E,OAAA;wBAAAwE,QAAA,EAAK2K,SAAS,CAACV,UAAU,GAAG,CAACU,SAAS,CAACV,UAAU,GAAG,GAAG,EAAElH,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG;sBAAK;wBAAA5C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA,GAtGhF,GAAGqK,SAAS,CAAClG,QAAQ,IAAIkG,SAAS,CAAC5O,aAAa,IAAI4T,KAAK,EAAE;sBAAAxP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAuGhE,CACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAEZ,CAAC,EAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CACP,EAGAvC,eAAe,iBACdvC,OAAA;UAAK4Q,SAAS,EAAC,8BAA8B;UAAApM,QAAA,eAC3CxE,OAAA;YAAK4Q,SAAS,EAAC,2BAA2B;YAAApM,QAAA,gBACxCxE,OAAA;cAAK4Q,SAAS,EAAC,MAAM;cAAApM,QAAA,eACnBxE,OAAA,CAACT,OAAO;gBAAC+T,SAAS,EAAC,QAAQ;gBAACC,IAAI,EAAC,IAAI;gBAAC9I,IAAI,EAAC;cAAQ;gBAAA9F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC,eACN9E,OAAA;cAAAwE,QAAA,gBACExE,OAAA;gBAAI4Q,SAAS,EAAC,MAAM;gBAAApM,QAAA,GAAC,qBAAmB,EAAC/B,cAAc,EAAC,GAAC,EAACQ,cAAc;cAAA;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9E9E,OAAA;gBAAK4Q,SAAS,EAAC,UAAU;gBAACtM,KAAK,EAAE;kBAAEyN,MAAM,EAAE;gBAAO,CAAE;gBAAAvN,QAAA,eAClDxE,OAAA;kBACE4Q,SAAS,EAAC,cAAc;kBACxBnG,IAAI,EAAC,aAAa;kBAClBnG,KAAK,EAAE;oBAAEwN,KAAK,EAAE,GAAIrP,cAAc,GAAGQ,cAAc,GAAI,GAAG;kBAAI,CAAE;kBAChE,iBAAeR,cAAe;kBAC9B,iBAAc,GAAG;kBACjB,iBAAeQ;gBAAe;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAOA,CAACvC,eAAe,IAAIF,YAAY,CAAC4F,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM;UACrD,MAAMoN,WAAW,GAAGhT,YAAY,CAAC4F,MAAM;UACvC,MAAMqN,gBAAgB,GAAGjT,YAAY,CAACuK,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC9B,OAAO,CAAC,CAAC9C,MAAM;UACnE,MAAMsN,YAAY,GAAGlT,YAAY,CAACuK,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAAC9B,OAAO,CAAC,CAAC9C,MAAM;UAEhE,IAAIuN,YAAY,GAAG,OAAO;UAC1B,IAAIC,UAAU,GAAG,EAAE;UAEnB,IAAIhS,yBAAyB,EAAE;YAC7B;YACA,MAAMiS,aAAa,GAAGrT,YAAY,CAACuK,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAAC1B,MAAM,CAAC,CAAClD,MAAM;YAChE,MAAM0N,iBAAiB,GAAGN,WAAW,GAAG,CAAC,GAAIK,aAAa,GAAGL,WAAW,GAAI,GAAG,GAAG,CAAC;YAEnF,IAAIA,WAAW,GAAG,CAAC,EAAE;cACnB,IAAIM,iBAAiB,KAAK,CAAC,EAAE;gBAC3B;gBACAH,YAAY,GAAG,SAAS;cAC1B,CAAC,MAAM,IAAIG,iBAAiB,KAAK,GAAG,EAAE;gBACpC;gBACAH,YAAY,GAAG,QAAQ;cACzB,CAAC,MAAM;gBACL;gBACAA,YAAY,GAAG,SAAS;gBACxBC,UAAU,GAAG,sBAAsB;cACrC;YACF;UACF,CAAC,MAAM;YACL;YACA,IAAIF,YAAY,KAAK,CAAC,EAAE;cACtB;cACAC,YAAY,GAAG,SAAS;YAC1B,CAAC,MAAM,IAAIF,gBAAgB,KAAK,CAAC,EAAE;cACjC;cACAE,YAAY,GAAG,QAAQ;YACzB,CAAC,MAAM;cACL;cACAA,YAAY,GAAG,SAAS;YAC1B;UACF;UAEA,oBACExV,OAAA;YAAK4Q,SAAS,EAAC,4BAA4B;YAAApM,QAAA,eACzCxE,OAAA,CAACV,KAAK;cAAC8T,OAAO,EAAEoC,YAAa;cAAC5E,SAAS,EAAE6E,UAAW;cAAAjR,QAAA,gBAClDxE,OAAA;gBAAG4Q,SAAS,EAAC;cAA0B;gBAAAjM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,cAClC,EAACzC,YAAY,CAAC4F,MAAM,EAAC,UAC/B,EAACxE,yBAAyB,gBACxBzD,OAAA,CAAAE,SAAA;gBAAAsE,QAAA,GACGnC,YAAY,CAACuK,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC9B,OAAO,IAAI8B,CAAC,CAAC3B,SAAS,CAAC,CAACjD,MAAM,EAAC,yBAC3D,EAAC5F,YAAY,CAACuK,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC9B,OAAO,IAAI,CAAC8B,CAAC,CAAC3B,SAAS,CAAC,CAACjD,MAAM,EAAC,4BAC5D,EAAC5F,YAAY,CAACuK,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAAC9B,OAAO,CAAC,CAAC9C,MAAM,EAAC,UAC/C;cAAA,eAAE,CAAC,gBAEHjI,OAAA,CAAAE,SAAA;gBAAAsE,QAAA,GACGnC,YAAY,CAACuK,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC9B,OAAO,CAAC,CAAC9C,MAAM,EAAC,iCAC5C,EAAC5F,YAAY,CAACuK,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAAC9B,OAAO,CAAC,CAAC9C,MAAM,EAAC,UAC/C;cAAA,eAAE,CACH;YAAA;cAAAtD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAEV,CAAC,EAAE,CAAC,EAGH,CAACvC,eAAe,IAAIF,YAAY,CAAC4F,MAAM,GAAG,CAAC,IAAIxE,yBAAyB,iBACvEzD,OAAA;UAAK4Q,SAAS,EAAC,yBAAyB;UAAApM,QAAA,eACtCxE,OAAA,CAACf,IAAI;YAAAuF,QAAA,gBACHxE,OAAA,CAACf,IAAI,CAACwF,MAAM;cAAAD,QAAA,eACVxE,OAAA;gBAAK4Q,SAAS,EAAC,mDAAmD;gBAAApM,QAAA,gBAChExE,OAAA;kBAAI4Q,SAAS,EAAC,MAAM;kBAAApM,QAAA,EAAC;gBAAuB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjD9E,OAAA;kBAAK4Q,SAAS,EAAC,gBAAgB;kBAAApM,QAAA,gBAC7BxE,OAAA,CAACd,MAAM;oBACLkU,OAAO,EAAE7P,WAAW,KAAK,KAAK,GAAG,SAAS,GAAG,iBAAkB;oBAC/DgQ,IAAI,EAAC,IAAI;oBACT3C,SAAS,EAAC,MAAM;oBAChB8B,OAAO,EAAEA,CAAA,KAAMlP,cAAc,CAAC,KAAK,CAAE;oBAAAgB,QAAA,EACtC;kBAED;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT9E,OAAA,CAACd,MAAM;oBACLkU,OAAO,EAAE7P,WAAW,KAAK,MAAM,GAAG,SAAS,GAAG,iBAAkB;oBAChEgQ,IAAI,EAAC,IAAI;oBACT3C,SAAS,EAAC,MAAM;oBAChB8B,OAAO,EAAEA,CAAA,KAAMlP,cAAc,CAAC,MAAM,CAAE;oBAAAgB,QAAA,EACvC;kBAED;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT9E,OAAA,CAACd,MAAM;oBACLkU,OAAO,EAAE7P,WAAW,KAAK,UAAU,GAAG,QAAQ,GAAG,gBAAiB;oBAClEgQ,IAAI,EAAC,IAAI;oBACTb,OAAO,EAAEA,CAAA,KAAMlP,cAAc,CAAC,UAAU,CAAE;oBAAAgB,QAAA,EAC3C;kBAED;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC,eACd9E,OAAA,CAACf,IAAI,CAAC8F,IAAI;cAAAP,QAAA,eACRxE,OAAA;gBAAK4Q,SAAS,EAAC,kBAAkB;gBAAApM,QAAA,eAC/BxE,OAAA;kBAAO4Q,SAAS,EAAC,qBAAqB;kBAAApM,QAAA,gBACpCxE,OAAA;oBAAAwE,QAAA,eACExE,OAAA;sBAAAwE,QAAA,gBACExE,OAAA;wBAAAwE,QAAA,EAAI;sBAAK;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACd9E,OAAA;wBAAAwE,QAAA,EAAI;sBAAgB;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC,eACR9E,OAAA;oBAAAwE,QAAA,EACGnC,YAAY,CACVuK,MAAM,CAACpE,MAAM,IAAI;sBAChB,IAAIjF,WAAW,KAAK,MAAM,EAAE,OAAOiF,MAAM,CAAC2C,MAAM;sBAChD,IAAI5H,WAAW,KAAK,UAAU,EAAE,OAAO,CAACiF,MAAM,CAAC2C,MAAM;sBACrD,OAAO,IAAI,CAAC,CAAC;oBACf,CAAC,CAAC,CACD+D,GAAG,CAAC,CAAC1G,MAAM,EAAE2L,KAAK,KAAK;sBACtB,MAAMlL,QAAQ,GAAGT,MAAM,CAACS,QAAQ;sBAChC,MAAMkC,MAAM,GAAG3C,MAAM,CAAC2C,MAAM;;sBAE5B;sBACA,IAAIyK,YAAY,GAAG,IAAI;sBACvB,IAAIvJ,SAAS,GAAG,IAAI;sBAEpB,IAAI1J,mBAAmB,CAACsG,QAAQ,CAAC,EAAE;wBACjC;wBACA2M,YAAY,GAAGjT,mBAAmB,CAACsG,QAAQ,CAAC,CAAC2C,aAAa;wBAC1DS,SAAS,GAAG1J,mBAAmB,CAACsG,QAAQ,CAAC;wBACzCvD,OAAO,CAAC8B,GAAG,CAAC,wBAAwB,EAAEyB,QAAQ,EAAE,WAAW,EAAE,CAAC,CAAC2M,YAAY,CAAC;sBAC9E,CAAC,MAAM,IAAIjV,gBAAgB,CAACsI,QAAQ,CAAC,EAAE;wBACrC;wBACA2M,YAAY,GAAGjV,gBAAgB,CAACsI,QAAQ,CAAC;wBACzCoD,SAAS,GAAG;0BACVT,aAAa,EAAEgK,YAAY;0BAC3B3U,cAAc,EAAE,IAAI;0BACpBE,OAAO,EAAE,IAAI;0BACbgK,MAAM,EAAEA;wBACV,CAAC;wBACDzF,OAAO,CAAC8B,GAAG,CAAC,0BAA0B,EAAEyB,QAAQ,EAAE,WAAW,EAAE,CAAC,CAAC2M,YAAY,CAAC;sBAChF,CAAC,MAAM;wBACLlQ,OAAO,CAAC8B,GAAG,CAAC,0BAA0B,EAAEyB,QAAQ,CAAC;sBACnD;sBAEA,oBACEjJ,OAAA;wBAAAwE,QAAA,gBACExE,OAAA;0BAAAwE,QAAA,eACExE,OAAA;4BAAK4Q,SAAS,EAAC,2BAA2B;4BAAApM,QAAA,GACvCoR,YAAY,gBACX5V,OAAA;8BACE4R,GAAG,EAAEgE,YAAa;8BAClB/D,GAAG,EAAE,aAAasC,KAAK,GAAG,CAAC,EAAG;8BAC9BvD,SAAS,EAAC,oBAAoB;8BAC9BtM,KAAK,EAAE;gCACLwN,KAAK,EAAE,MAAM;gCACbC,MAAM,EAAE,MAAM;gCACd8D,SAAS,EAAE,OAAO;gCAClBnE,MAAM,EAAE;8BACV,CAAE;8BACFgB,OAAO,EAAEA,CAAA,KAAMzF,oBAAoB,CAACZ,SAAS,CAAE;8BAC/C4E,KAAK,EAAC;4BAAyB;8BAAAtM,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAChC,CAAC,gBAEF9E,OAAA;8BACE4Q,SAAS,EAAC,qEAAqE;8BAC/EtM,KAAK,EAAE;gCACLwN,KAAK,EAAE,MAAM;gCACbC,MAAM,EAAE,MAAM;gCACdY,eAAe,EAAE,SAAS;gCAC1BG,MAAM,EAAE;8BACV,CAAE;8BAAAtO,QAAA,eAEFxE,OAAA;gCAAO4Q,SAAS,EAAC,YAAY;gCAAApM,QAAA,EAAC;8BAAQ;gCAAAG,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC3C,CACN,eACD9E,OAAA;8BAAO4Q,SAAS,EAAC,YAAY;8BAAApM,QAAA,EAAEyE;4BAAQ;8BAAAtE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC7C;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC,eACL9E,OAAA;0BAAAwE,QAAA,eACExE,OAAA;4BAAM4Q,SAAS,EAAE,SAASzF,MAAM,GAAG,YAAY,GAAG,WAAW,EAAG;4BAAA3G,QAAA,EAC7D2G,MAAM,GAAG,MAAM,GAAG;0BAAU;4BAAAxG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC;sBAAA,GArCEmE,QAAQ;wBAAAtE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAsCb,CAAC;oBAET,CAAC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEN9E,OAAA,CAACX,GAAG;QAAC2R,QAAQ,EAAC,OAAO;QAACC,KAAK,EAAC,iBAAiB;QAAAzM,QAAA,eAC3CxE,OAAA,CAACF,oBAAoB;UAAA6E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC,eAEN9E,OAAA,CAACX,GAAG;QAAC2R,QAAQ,EAAC,aAAa;QAACC,KAAK,EAAC,aAAa;QAAAzM,QAAA,eAC7CxE,OAAA,CAACf,IAAI;UAAAuF,QAAA,eACHxE,OAAA,CAACf,IAAI,CAAC8F,IAAI;YAAAP,QAAA,gBACRxE,OAAA;cAAAwE,QAAA,EAAI;YAAuB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChC9E,OAAA;cAAAwE,QAAA,EAAG;YAGH;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAEJ9E,OAAA;cAAAwE,QAAA,EAAI;YAAW;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpB9E,OAAA;cAAAwE,QAAA,EAAG;YAGH;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJ9E,OAAA;cAAAwE,QAAA,gBACExE,OAAA;gBAAAwE,QAAA,EAAI;cAA0B;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnC9E,OAAA;gBAAAwE,QAAA,EAAI;cAAoB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7B9E,OAAA;gBAAAwE,QAAA,EAAI;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACf9E,OAAA;gBAAAwE,QAAA,EAAI;cAA6C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eAEL9E,OAAA;cAAAwE,QAAA,EAAI;YAAmB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5B9E,OAAA;cAAAwE,QAAA,EAAG;YAIH;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJ9E,OAAA;cAAAwE,QAAA,gBACExE,OAAA;gBAAAwE,QAAA,EAAI;cAAgB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzB9E,OAAA;gBAAAwE,QAAA,EAAI;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpB9E,OAAA;gBAAAwE,QAAA,EAAI;cAAe;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxB9E,OAAA;gBAAAwE,QAAA,EAAI;cAAmB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5B9E,OAAA;gBAAAwE,QAAA,EAAI;cAAiB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,eAEL9E,OAAA;cAAAwE,QAAA,EAAI;YAAQ;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjB9E,OAAA;cAAAwE,QAAA,EAAG;YAGH;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJ9E,OAAA;cAAAwE,QAAA,gBACExE,OAAA;gBAAAwE,QAAA,EAAI;cAA2D;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpE9E,OAAA;gBAAAwE,QAAA,EAAI;cAAgE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzE9E,OAAA;gBAAAwE,QAAA,EAAI;cAAmE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1E,CAAC,eAEL9E,OAAA;cAAAwE,QAAA,EAAI;YAA4B;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrC9E,OAAA;cAAAwE,QAAA,EAAG;YAGH;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJ9E,OAAA;cAAAwE,QAAA,gBACExE,OAAA;gBAAAwE,QAAA,EAAI;cAAmC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5C9E,OAAA;gBAAAwE,QAAA,EAAI;cAA+B;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxC9E,OAAA;gBAAAwE,QAAA,EAAI;cAA4C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrD9E,OAAA;gBAAAwE,QAAA,EAAI;cAA2C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,eAEL9E,OAAA;cAAAwE,QAAA,EAAI;YAAsB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/B9E,OAAA;cAAAwE,QAAA,gBACExE,OAAA;gBAAAwE,QAAA,gBAAIxE,OAAA;kBAAAwE,QAAA,EAAQ;gBAAkB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,oCAAgC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5E9E,OAAA;gBAAAwE,QAAA,gBAAIxE,OAAA;kBAAAwE,QAAA,EAAQ;gBAAoB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,iDAA6C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3F9E,OAAA;gBAAAwE,QAAA,gBAAIxE,OAAA;kBAAAwE,QAAA,EAAQ;gBAAa;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,gDAA4C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnF9E,OAAA;gBAAAwE,QAAA,gBAAIxE,OAAA;kBAAAwE,QAAA,EAAQ;gBAAe;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,4DAAwD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/F,CAAC,eAEL9E,OAAA;cAAK4Q,SAAS,EAAC,kBAAkB;cAAApM,QAAA,gBAC/BxE,OAAA;gBAAAwE,QAAA,gBAAIxE,OAAA;kBAAG4Q,SAAS,EAAC;gBAAyB;kBAAAjM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,mCAA+B;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnF9E,OAAA;gBAAAwE,QAAA,eAAGxE,OAAA;kBAAAwE,QAAA,EAAQ;gBAA6B;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACrD9E,OAAA;gBAAI4Q,SAAS,EAAC,MAAM;gBAAApM,QAAA,gBAClBxE,OAAA;kBAAAwE,QAAA,gBAAIxE,OAAA;oBAAAwE,QAAA,EAAQ;kBAAO;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,gEAAkD;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnF9E,OAAA;kBAAAwE,QAAA,gBAAIxE,OAAA;oBAAAwE,QAAA,EAAQ;kBAAO;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,8EAA2D;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC5F9E,OAAA;kBAAAwE,QAAA,gBAAIxE,OAAA;oBAAAwE,QAAA,EAAQ;kBAAQ;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,0EAAuD;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvF,CAAC,eACL9E,OAAA;gBAAAwE,QAAA,gBAAGxE,OAAA;kBAAAwE,QAAA,EAAQ;gBAAkB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,+FAA2F;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClI,CAAC,eAEN9E,OAAA;cAAAwE,QAAA,EAAI;YAAsB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/B9E,OAAA;cAAAwE,QAAA,gBACExE,OAAA;gBAAAwE,QAAA,EAAI;cAAgE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzE9E,OAAA;gBAAAwE,QAAA,EAAI;cAAoD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7D9E,OAAA;gBAAAwE,QAAA,EAAI;cAA4E;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrF9E,OAAA;gBAAAwE,QAAA,EAAI;cAA4C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrD9E,OAAA;gBAAAwE,QAAA,EAAI;cAA6C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eAEL9E,OAAA;cAAAwE,QAAA,EAAG;YAGH;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eA6CP9E,OAAA,CAACN,KAAK;MACJ0S,IAAI,EAAEjP,cAAe;MACrB2S,MAAM,EAAEA,CAAA,KAAM1S,iBAAiB,CAAC,KAAK,CAAE;MACvCmQ,IAAI,EAAC,IAAI;MACTwC,QAAQ;MAAAvR,QAAA,gBAERxE,OAAA,CAACN,KAAK,CAAC+E,MAAM;QAACuR,WAAW;QAAAxR,QAAA,eACvBxE,OAAA,CAACN,KAAK,CAACuW,KAAK;UAAAzR,QAAA,gBACVxE,OAAA;YAAG4Q,SAAS,EAAC;UAAmB;YAAAjM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1B,EAAC,CAAAzB,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAE4F,QAAQ,kBACrCjJ,OAAA;YAAO4Q,SAAS,EAAC,YAAY;YAAApM,QAAA,GAAC,IAAE,EAACnB,iBAAiB,CAAC4F,QAAQ;UAAA;YAAAtE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACpE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACf9E,OAAA,CAACN,KAAK,CAACqF,IAAI;QAAAP,QAAA,EACRnB,iBAAiB,iBAChBrD,OAAA;UAAK4Q,SAAS,EAAC,aAAa;UAAApM,QAAA,gBAC1BxE,OAAA;YAAK4Q,SAAS,EAAC,MAAM;YAAApM,QAAA,gBACnBxE,OAAA;cAAI4Q,SAAS,EAAC,MAAM;cAAApM,QAAA,gBAClBxE,OAAA;gBAAG4Q,SAAS,EAAC;cAAoB;gBAAAjM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,kBAExC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL9E,OAAA;cACE4R,GAAG,EAAEvO,iBAAiB,CAACuI,aAAc;cACrCiG,GAAG,EAAC,gBAAgB;cACpBjB,SAAS,EAAC,WAAW;cACrBtM,KAAK,EAAE;gBACL4R,SAAS,EAAE,OAAO;gBAClBtD,YAAY,EAAE,KAAK;gBACnBE,MAAM,EAAE,mBAAmB;gBAC3BG,SAAS,EAAE;cACb;YAAE;cAAAtO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,EACLzB,iBAAiB,CAACpC,cAAc,IAAIoC,iBAAiB,CAAC8H,MAAM,iBAC3DnL,OAAA;YAAK4Q,SAAS,EAAC,MAAM;YAAApM,QAAA,gBACnBxE,OAAA;cAAI4Q,SAAS,EAAC,MAAM;cAAApM,QAAA,gBAClBxE,OAAA;gBAAG4Q,SAAS,EAAC;cAAoB;gBAAAjM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,uCAExC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL9E,OAAA;cACE4R,GAAG,EAAEvO,iBAAiB,CAACpC,cAAe;cACtC4Q,GAAG,EAAC,iBAAiB;cACrBjB,SAAS,EAAC,WAAW;cACrBtM,KAAK,EAAE;gBACL4R,SAAS,EAAE,OAAO;gBAClBtD,YAAY,EAAE,KAAK;gBACnBE,MAAM,EAAE,mBAAmB;gBAC3BG,SAAS,EAAE;cACb;YAAE;cAAAtO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN,EACA,CAACzB,iBAAiB,CAAC8H,MAAM,iBACxBnL,OAAA;YAAK4Q,SAAS,EAAC,MAAM;YAAApM,QAAA,eACnBxE,OAAA,CAACV,KAAK;cAAC8T,OAAO,EAAC,MAAM;cAAA5O,QAAA,gBACnBxE,OAAA;gBAAG4Q,SAAS,EAAC;cAAyB;gBAAAjM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,0FAE7C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,eACb9E,OAAA,CAACN,KAAK,CAACyW,MAAM;QAAA3R,QAAA,eACXxE,OAAA,CAACd,MAAM;UACLkU,OAAO,EAAC,WAAW;UACnBV,OAAO,EAAEA,CAAA,KAAMtP,iBAAiB,CAAC,KAAK,CAAE;UAAAoB,QAAA,EACzC;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEhB,CAAC;;AAED;AAAA1E,EAAA,CA1jEMD,QAAQ;EAAA,QAkDSN,aAAa;AAAA;AAAAuW,EAAA,GAlD9BjW,QAAQ;AA2jEd,MAAMkW,MAAM,GAAG;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,IAAI,OAAO9G,QAAQ,KAAK,WAAW,EAAE;EACnC,MAAM+G,UAAU,GAAG/G,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;EAElD8G,UAAU,CAACC,SAAS,GAAGF,MAAM;EAC7B9G,QAAQ,CAACiH,IAAI,CAACxG,WAAW,CAACsG,UAAU,CAAC;AACvC;AAEA,eAAenW,QAAQ;AAAC,IAAAiW,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}